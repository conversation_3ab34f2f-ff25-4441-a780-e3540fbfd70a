@charset "utf-8";
/* CSS Document */
/*  clearer */
body{padding:0;margin:0;font-size:12px;overflow-x:hidden;background:#fff url('') repeat-x;font-family:'微软雅黑';}
a{color:#333;text-decoration:none}
input,button,select,textarea{outline:none}
li,ul,form,h3{padding:0;margin:0;list-style-type:none}
img{border:0}
p,h1,h3,h2{margin:0;padding:0}
/*  清除浮动 */
.clearfloat{
clear:both;
height:0;
font-size:1px;
line-height::0px;
}

.clearfloat:after {content:".";clear:both;height:0;visibility:hidden;display:block;}/* 这是对Firefox进行的处理，因为Firefox支持生成元素，而IE所有版本都不支持生成元素 */
.clearfloat{display:inline-block;}/* 这是对 Mac 上的IE浏览器进行的处理*/
/* Hides from IE-mac */


/*  左右浮动*/
.fl{float:left;}
.fr{float:right;}
.ft{color:#ffcc00;}
/*  banner */

.banner {
	width:100%;
	height:507px;
	margin:0 auto;
	position: relative;

}
.banner_main{
	width:100%;
	height:507px;
	position: absolute;
	left:0;
	top:0;

}
.banner_main li{
	display:block;
	height:507px;
	position: absolute;
	left:0;
	top:0;
	background:#ebebeb url(../images/default-l.png) no-repeat center center;
	background-size:100%;
	width: 100%;
}
.banner_span {
	width:100%;
	height:35px;
	position: absolute;
	left:0;
	bottom:0;
	zoom:1;left: 50%;
	margin-left: -70px;
}
.banner_span span {
	width: 28px;
	height: 20px;
	display:block;
	float:left;
	margin-left:10px;
	background: url(../images/icon.png) no-repeat 11px -475px;
}
.banner_span p {
	height:35px;
	margin:0 auto;
}
.banner_span .banner_span_one{
	background: url(../images/icon.png) no-repeat 6px -440px;
}
.banner_left {
	width:61px;
	height:90px;
	cursor: pointer;
	background: url(../images/icon.png) no-repeat 0 -510px;
	filter:alpha(opacity:50);opacity:0.5;
	position: absolute;
	top:50%;
	margin-top:-30px;
	display:block;
left: 50%;
margin-left: -585px;
}
.banner_left1 {
	background: url(../images/icon.png) no-repeat 0 -510px;}
.banner_right {
	width:61px;
	height:90px;
	cursor: pointer;
	background: url(../images/icon.png) no-repeat 0 -600px;	;
	position: absolute;
	right:0;
	margin-top:-30px;
	top:50%;
	left: 50%;
	margin-left: 525px;
	display:block;
}
.banner_right1 {
background: url(../images/icon.png) no-repeat 0 -600px;	;
}

.index_bn{width:1200px;margin:0 auto;}

.index_chou_words,.index_publish_word{
	font-size:16px;
	color:#fff;
	height:37px;
	line-height:37px;
	background:#ee3531;
	margin-top:120px;
	margin-left:30px;
	padding:0 15px;
	border-radius:8px;
	-moz-border-radius:8px;
	-webkit-border-radius:8px;
	}
.index_publish_word{
	margin-top:-90px;
	margin-left:-70px;
	width: 380px;
	}

.index_chou  {
	display: block;
	float:left;
	margin-left:50px;
	background: url(../images/banner1.png) no-repeat ;float: left;
	text-align: center;
	margin-top: 120px;
}
.index_publish{

	display: block;
width: 411px;
height:300px;
float:right;
margin-right:80px;

text-align: center;
margin-top: 120px;

	}
.index_publish_pic{display: block;
width: 411px;
height: 182px;
margin: 30px auto;
background: url(../images/banner2.png) no-repeat 0 0px;}
}

.index_banner_form .index_chou .index_chou_words {
width: 420px;
margin: 30px auto;
text-align: center;
color: #fff;
font-size: 16px;
background-color: #1d3377;
line-height: 45px;
border-radius: 10px;
}
.index_chou_btn {
	display: block !important;
	width: 265px;
	height: 55px !important;
	margin-left:133px;
	margin-top:40px;
	font-size: 24px;
	color: #fff;
	line-height: 53px;
	background-color: transparent;
	border: 1px #fff solid;
	border-radius: 8px;
	font-family:'微软雅黑';
	-webkit-transition: all 0.3s;
-moz-transition: all 0.3s;
-o-transition: all 0.3s;

}
.index_publish_btn {
	display: block !important;
	width: 265px;
	height: 55px !important;
	margin-right:133px;
	font-size: 24px;
	color: #fff;
	line-height: 53px;
	background-color: transparent;
	border: 1px #fff solid;
	border-radius: 8px;
	font-family:'微软雅黑';
	-webkit-transition: all 0.3s;
	-moz-transition: all 0.3s;
	-o-transition: all 0.3s;

}
.itemlist1 li img{
	height:223px;
	width:382px;
}

.chou_corner,.publish_corner {
display: inline-block;
margin-left: 20px;
border-width: 10px;
border-color: transparent transparent transparent #fff;
border-style: solid;
}


/*  主要内容  */
/* css3动画效果 */
/* 鼠标移到图片上的效果 */
 .css3btn {
    width: 100%;
    overflow: hidden;
    position: relative;
    -webkit-box-shadow: 1px 1px 3px 0 rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 1px 1px 3px 0 rgba(0, 0, 0, 0.3);
    box-shadow: 1px 1px 3px 0 rgba(0, 0, 0, 0.3);
}

.css3btn img {
	width: 100%;
    top: 0;
    position: relative;
    left: 0;
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
    -webkit-transition: -webkit-transform 0.3s;
    -moz-transition: -moz-transform 0.3s;
    -o-transition: -o-transform 0.3s;
    transition: transform 0.3s;
}
.css3btn .ovrly {
	 background: rgba(0, 0, 0, 0.5);
    height: 100%;
    left: 0;
    top: 0;
    position: absolute;
    width: 100%;
    opacity: 0;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
.css3btn .buttonsxq {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.css3btn .buttonsxq .fa {
    background: rgb(256, 256, 256);
    -webkit-box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
    -webkt-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    display: inline-block;
    line-height: 40px;
    font-size: 16px;
    text-align: center;
    text-decoration: none;
    width: 40px;
    height: 40px;
    opacity: 0;
    -webkit-transform: scale(5);
    -moz-transform: scale(5);
    -ms-transform: scale(5);
    -o-transform: scale(5);
    transform: scale(5);
    position: relative;
    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
    -moz-transition: -moz-transform 0.3s, opacity 0.3s;
    -o-transition: -o-transform 0.3s, opacity 0.3s;
    transition: transform 0.3s, opacity 0.3s;
    color: rgba(50, 50, 50, 0.9);
}
.css3btn  .buttonsxq .fa-search {
    -webkit-transition-delay: 0.1s;
    -moz-transition-delay: 0.1s;
    -ms-transition-delay: 0.1s;
    transition-delay: 0.1s;
}
.css3btn:hover .buttonsxq .fa {
    opacity: 1;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
}
.css3btn:hover .ovrly {
    opacity: 1;
}
.css3btn:hover img {
    -webkit-filter: blur(2px);
    filter: blur(2px);
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
}
.fa-search:before {
	content:"";
	display:inline-block;
	height:20px;
	width:20px;
	background:url(../images/resizeApi.png) no-repeat;
	vertical-align:middle;
	margin-top:12px;
	margin-left:4px;
}
/*  结束*/


.main1,.main2,.main3,.main4,.main5{width:100%;margin:0 auto;overflow:hidden;}
.title{padding:30px 0;}
.title .tit_p1{color:#888;font-size:14px;}
.title .tit_p2{
	font-family:'微软雅黑';
	color:#000;
	font-size:22px;
line-height:1.2;
	}
.tit_p2 span{
	display:block;
	height:22px;
	width:22px;
	background:#ee3531;
	float:left;
	margin-top:5px;
	margin-right:5px;
	}
.header-pos{height: 320px;width: 100%;position: relative;
overflow: hidden;}
.headline{
	margin-bottom:55px;
	overflow: hidden;
	width: 100%;
	background:#fafafa;
	}
.pic img{
	/*height:223px;*/
	height:300px;
	width:390px;
	/*width:382px;*/
	float:left;
	}
.item_main{
	width:300px;
	/*width:633px;*/
	float:left;
	border-left:none;
	height:300px;
	/*margin-left: -152px;*/
	}
.item2{width: 490px;
margin: 0 auto;margin-top:15px;margin-left: 30px;}
.item_main .item1{padding:20px 30px;}
.item_main .item3 {
	margin: 30px auto 0px;
	padding: 20px 30px;
}
.item_main .item4 {
	width: 490px;
	margin: 0 auto;
	padding:20px 30px;
}

.pro_progress_num {
    position: relative;
    float: right;
    height: 35px;
   margin-right: 100px;
}
.pro_progress_num .progress_corner {
    display: inline-block;
    position: absolute;
    bottom: 0px;
    border-width: 3px;
    border-color: transparent #EE3531 #EE3531 transparent;
    border-style: solid;
}
 .pro_progress_num .progress_figure {
    display: inline-block;
    position: absolute;
    top: 0px;
    left: 6px;
    width: 80px;
    height: 35px;
    font-size: 14px;
    line-height: 35px;
    color: #FFF;
    text-align: center;
    background-color: #EE3531;
}

.item2-xq{
	text-align:center;
	line-height:42px;
	border:1px solid #999;
	margin-top:25px;
		color: #333;
	width: 140px;
	height: 40px;
	font-size: 16px;
	display: inline-block;
	margin-bottom: 75px;
	border-radius: 3px;
	transition: all 0.3s ease-out 0s;
	letter-spacing: 1.5px;

	}
.item2-xq:hover{border:1px solid #333;color:#000;}
.m1_pre{
	display:block;
	height:82px;
	width:40px;
	background:url('../images/icon1.png') 0px 0px;
	position:absolute;
	margin-left:-30px;
	margin-left: -100px;
	margin-top: -300px;
	cursor: pointer}
.m1_nex{
	display:block;
	height:82px;
	width:40px;
	background:url('../images/icon1.png') -40px 0px ;
	position:absolute;
	margin-left:-30px;
	margin-left: 1260px;
	margin-top: -300px;
	cursor: pointer}

/* 进度条 */


.main3,.main4{padding-bottom:15px;}
.main2{padding-bottom:20px;}
.licont{width:350px;margin:0 auto;}
.itemlist1 li{height:400px;width:382px;float:left;background:#fff;  transition: all 0.3s ease 0s;}
.itemlist1 li:hover{-webkit-box-shadow: 5px 5px 5px #ccc;
box-shadow: 5px 5px 5px #ccc/*opera或ie9*/;}
.main4 .itemlist1 li{background:#fafafa;}
.item1_jdt{margin-top:20px;}
.wz1{font-size:20px;color:#000;font-family:'微软雅黑';padding-top:15px;}
.wz2{font-size:14px;color:#888;padding-bottom:12px;width: 350px;
text-overflow: ellipsis;
white-space: nowrap;}
.warp{background:#fafafa;}
.c-666 {
	color: #666;
	font-family: '微软雅黑';
	font-size: 14px;
	padding-bottom: 5px;
}
.fmoney {
font-family: Century Gothic, Palatino, serif;
}
.pb2m-info-bar {
	position: relative;
	width: 100%;
	height: 10px;
	margin-bottom: 10px;
	background: #eee;
	overflow: hidden;
}
.radius10px {
	border-radius: 10px;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
}

.pb2m-info-percent {
	width: 100%;
	height: 10px;
	border: 0px;
    background: #fe645a url(../images/bg-progress-red.gif);
	margin: 0;
}



.btn1{
	display:block;
	height:32px;
	width:92px;
	line-height:32px;
	text-align:center;
	color:#fff;
	background:#333;
	float:left;
	margin-top:8px;
	border-radius:5px;
transition: all 0.3s ease-out 0s;
	letter-spacing: 1.5px;
	font-family:'微软雅黑';
}
.btn1:hover{
	background:#000;
	color:red;
}

.btn2{
	display:block;
	height:32px;
	width:92px;
	line-height:32px;
	text-align:center;
	color:#333;
	border:1px solid #999;
	float:right;
	margin-top:8px;
	border-radius:5px;
		font-family:'微软雅黑';
transition: all 0.3s ease-out 0s;
letter-spacing: 1.5px;
background: #fff;
	}
.btn2:hover{
	border:1px solid #333;
	color:#000;}
.mg{margin-left:6%;}

.itemlist2 li {
	height:156px;
	width:544px;
	background:#fafafa;
	margin:10px 10px 55px 0px;
	-webkit-transition: all 0.3s;
	-moz-transition: all 0.3s;
	-o-transition: all 0.3s;
	transition: all 0.3s;

	}
.itemlist2 li:hover{
	-moz-box-shadow: 5px 5px 5px #ccc;/*firefox*/
	-webkit-box-shadow: 5px 5px 5px #ccc;/*safari或chrome*/
	box-shadow: 5px 5px 5px #ccc;/*opera或ie9*/
}
.itemlist2 img{
	height:119px;
	width:119px;
	border-radius:50%;
	-moz-border-radius: 50%;
	-webkit-border-radius: 50%;
	float:left;
	margin-top:20px;
	margin-left:20px;
	position:relative;
	z-index:2;
	behavior: url(C:\Users\<USER>\Desktop\币众筹\css\ie-css3.htc)
	}
.licont2{
	width:348px;
	overflow:auto;
	padding-left:20px;
	}
.hr{
	display:block;
	background:#e7e7e7;
	height:1px;
	margin:15px 0 15px 0;
	}
.fontgr{color:#ee3531;}
.tags {
position: relative;
margin-top: 8px;
}.tags .tag {
display: inline;
display: inline-block;
padding: 0 4px 0 0;font-size:12px;color:#888;
}
.cont2sp span{
	letter-spacing: 2px;}
.dot-active{background:#ee3531;}
.dot li{display:inline-block;height:8px;width:8px;border:1px solid #dcd8d8;border-radius:50%;margin-left:20px;-moz-border-radius: 50%;
-webkit-border-radius:8px;position:relative}
.dot{margin-left: 44%;margin-bottom:60px;margin-top:20px;}
.itemlist2{overflow:auto;width: 1200px;float: left;}
.owl-wrapper-outer {overflow: hidden;position: relative;width: 100%;height:430px;}
.owl-wrapper{width: 4800px;position:absolute;left: 0px;display: block;}

.posibox{transition: all 0.3s;diplay:none;}
.posibox span{display:block;height:40px;width:40px;background:url(../images/ui_sidebar.png); transition: all 0.2s ease 0s;}
.posibox .ewm:hover{background-position:0 -40px;}
.posibox .qq{background-position:-90px 0;}
.posibox .qq:hover{background-position:-90px  -40px;}
.posibox .tel{background-position:-45px 0px;}
.posibox .tel:hover{background-position:-45px -40px;}
.posibox .ly{background-position:-135px 0px;}
.posibox .ly:hover{background-position:-135px -40px;}
.fixed{position:fixed;width:62px;right: 80px;top: 300px;transition: all 0.3s;z-index:9999;}
.ewm  .sidebox_ewm_hide {
	position: absolute;
	width: 124px;
	height: 113px;
	padding: 5px 0 5px 5px;
	left: -135px;
	top: -83px;
	visibility: hidden;
	background-position: 0 0;
	background: url(https://bizhongchou.com/app/Tpl/fanwe_1/images/float_ewmbg.png) no-repeat;
	-webkit-transition: all 0.2s;
	-moz-transition: all 0.2s;
	-ms-transition: all 0.2s;
	transition: all 0.2s;
}
.ewm .sidebox_ewm_hide img {
	width: 110px;
	height: 110px;
	overflow: hidden;
}

/* foot */
.foottop{height:411px;
#background:url(../images/footbg.jpg);
background-position: 50% 0%; background-repeat: no-repeat no-repeat;position:relative;
}
.foottop_empty_user {
	height:411px;
	background:url(../images/footbg.jpg);
	background-position: 50% 0%; background-repeat: no-repeat no-repeat;position:relative;
}
.foottop_empty_user .ft1div {
	height:190px;
	background:#ee3531;
	position:absolute;
	top:106px;
	width:100%;
	text-align:center;
}
.ft1div{
	height:190px;
	background:#ee3531;
	#position:absolute;
	padding-top: 20px;
	top:106px;
	width:100%;
	text-align:center;
}
.footer_userlist_img{
	width:1908px;
	height: 100px;
	overflow: hidden;
}
.footer_userlist_img img {
	width: 106px;
	height: 100px;
	-webkit-filter: grayscale(100%);
	-moz-filter: grayscale(100%);
	-ms-filter: grayscale(100%);
	-o-filter: grayscale(100%);
	filter: grayscale(100%);
	filter: gray;
}
.wz4{font-size:36px;font-family:'微软雅黑';color:#fff;}
.ft2div{
	background:url(../images/yh.png) no-repeat center center;
	margin:0 auto;
	#margin-top:20px;
}
.foottop_empty_user .ft2div {
	background:url(../images/yh.png) no-repeat center center;
	margin:0 auto;
	margin-top:20px;
}
.wz3{
	display: block !important;
	width: 265px;
	height: 55px !important;
	font-size: 24px;
	color: #FFF;
	line-height: 53px;
	background-color: transparent;
	border: 1px solid #FFF;
	border-radius: 8px;
	font-family: "微软雅黑";
	transition: all 0.3s ease 0s;
	margin:0 auto;
	cursor:pointer;
	}
.wz5{
	color:#fff;
	font-size:36px;
	line-height:2;}
a.wz5:hover {
	color:#fff;
}
.footbot{height:auto;background:#1c2026;}
.footul{overflow:auto;margin:0 auto;width:1200px;}
.footul li a{color:#fff;display:inline-block;float:left;padding:0 40px;margin-top: 95px;}
.borrg{border-right:1px solid #494d51;}
.panter {width:1200px;margin:0 auto;text-align:center;margin-top:30px;padding-top:30px;}
.panter a{color:#fff;opacity:0.58;padding-left:5px;}
.pd{width:850px;margin:0 auto;}

.Copyright{color:#fff;text-align:center;height:45px;line-height:45px;background:#000;margin-top:80px;opacity:0.75;}

/*幻灯片第二张(同股权众筹页面图片背景)*/
/* 字体样式 */
@font-face {
      font-family: FZZCHJW;
        src: url('../fonts/FZZCHJW.TTF'); /* IE9*/

    }
.font5{font-size:66px;color:#fff;font-family:'FZZCHJW';padding-bottom:30px;padding-top:80px;}
.font6{font-size:18px;color:#fff;padding-bottom:30px;}
/*  投资型主页banner */
.banner {
	width:100%;
	height:507px;
	margin:0 auto;
	position: relative;
	text-align:center;
}
.itemnu .listnu{text-shadow:2px 2px 10px #00ff00;}
#effect span{color:#fff;padding-top: 80px;}
.index_bn_deal_type .itemnu{height:61px;margin-top:30px;display:table;margin:0 auto;position:relative;}
.index_bn_deal_type .listnu{
	height:61px;
	width:61px;
	background:url('../images/a.png');
	font-size:26px;
	color:#fff;
	line-height:61px;
	text-align:center;
	}
.index_bn_deal_type .itemnu div{float:left;line-height:61px;}
.index_bn_deal_type .itembtn{
	width:1000px;
	margin:0 auto;
	position:relative;
	height:50px;
	margin-top:70px;}
.index_bn_deal_type .itembtn a{
	display:inline-block;
	height:48px;
	width:252px;
	border:1px solid #fff;
	border-radius:5px;
	color:#fff;
	line-height:48px;
	font-size:20px;
	font-family:'微软雅黑';
	transition: opacity 300ms ease-in-out 0ms;
	background-color: transparent;
	position:absolute;
}
.index_bn_deal_type .itembtn a:before ,.itembtn a:after {
	content: "";
	display: block;
	position: absolute;
	height: 100%;
	width: 100%;
	left: 0%;
	top: 0px;
	opacity: 1;
}
.index_bn_deal_type .itembtn a:hover {
    opacity: 0.7;
}
.index_bn_deal_type .itembtn a:hover:after  {
      transition: border-radius 800ms, box-shadow 400ms, opacity 1200ms;
      box-shadow: 0px 0px 50px 20px rgba(255, 255, 255, 0.5);
      opacity: 0;
}
.index_bn_deal_type .itembtn_a1{
	left:0px;
}
.index_bn_deal_type .itembtn_a2{
	right:0px;
}

/*机构投资人*/
.owl-wrapper-outer .btn_recommend_project {
    width: 128px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    display: none;
    border: 1px solid #999;
    float: right;
    font-size: 14px;
    color: #333;
    transition: all 0.3s ease-out 0s;
    letter-spacing: 1.5px;
    background: #fff;
}
.owl-wrapper-outer .btn_recommend_project:hover {
    border:1px solid #333;
	color:#000;
}
.ft2div .invite_invester:hover {
	background: #fff;
	color: #241442;
}
.ft2div .invite_invester:hover .chou_corner {
	display: inline-block;
	margin-left: 20px;
	border-width: 10px;
	border-color: transparent transparent transparent #241442;
	border-style: solid;
}

/* 项目状态图片左上角 */
.main_deal_list_index,
.main_deal_list_index ul.itemlist1 {

}
.main_deal_list_index .item_deal_invest,
.main_deal_list_index .itemlist1 li {
	position: relative;
}
.main_deal_list_index .itemlist1 li .css3btn {
	position: relative;
}
.main_deal_list_index .deal-angle-warp {
    position: absolute;
    left: 0;
    height: 26px;
    color: #FFF;
    background-color: #F63756;
    line-height: 26px;
    padding: 0px 10px;
    z-index: 20;
    top: 0px;
}
.main_deal_list_index .deal-angle-warp-before {
    background-color: #F28D00;
}
.main_deal_list_index .deal-angle-warp-ing {
    background-color: #EE3531;
}
.main_deal_list_index .deal-angle-warp-fail {
    background-color: #333;
}
.main_deal_list_index .deal-angle-warp-success {
    background-color: #BBB;
}
.main_deal_list_index .deal-angle-warp-before .remind {
    display: inline-block;
    height: 20px;
    line-height: 20px;
    color: #FFF;
    border-radius: 3px;
    background-color: #F2C200;
    font-size: 12px;
    padding: 0 10px;
    margin-left: 5px;
    margin-top: -2px;
    vertical-align: middle;
    cursor: pointer;
}
