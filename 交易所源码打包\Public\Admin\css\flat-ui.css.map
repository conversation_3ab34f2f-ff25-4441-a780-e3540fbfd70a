{"version": 3, "sources": ["less/modules/local-fonts.less", "flat-ui.css", "less/modules/glyphicons.less", "less/modules/scaffolding.less", "less/mixins/image.less", "less/modules/type.less", "less/mixins/text-emphasis.less", "less/mixins/background-variant.less", "less/modules/code.less", "less/modules/thumbnails.less", "less/modules/buttons.less", "less/mixins/opacity.less", "less/mixins/buttons.less", "less/modules/button-groups.less", "less/modules/forms.less", "less/mixins/vendor-prefixes.less", "less/mixins/forms.less", "less/mixins/grid.less", "less/modules/input-groups.less", "less/mixins/border-radius.less", "less/modules/radiocheck.less", "less/modules/tagsinput.less", "less/modules/typeahead.less", "less/modules/progress-bars.less", "less/modules/slider.less", "less/modules/pager.less", "less/modules/pagination.less", "less/mixins/pagination.less", "less/modules/tooltip.less", "less/modules/dropdowns.less", "less/mixins/nav-divider.less", "less/modules/select.less", "less/mixins/select.less", "less/modules/tiles.less", "less/modules/navbar.less", "less/mixins/navbar-vertical-align.less", "less/modules/switch.less", "less/mixins/switches.less", "less/modules/share.less", "less/mixins/clearfix.less", "less/modules/video.less", "less/modules/todo-list.less", "less/modules/palette.less", "less/mixins/pallets.less", "less/modules/login.less", "less/modules/footer.less", "less/spaces.less", "less/modules/print.less"], "names": [], "mappings": "AAIA;EACE,qBAAA;EACA,0CAAA;EACA,uPAAA;EAIA,kBAAA;EACA,oBAAA;ECND;ADSD;EACE,qBAAA;EACA,yCAAA;EACA,kPAAA;EAIA,mBAAA;EACA,oBAAA;ECVD;ADaD;EACE,qBAAA;EACA,+CAAA;EACA,iRAAA;EAIA,mBAAA;EACA,oBAAA;ECdD;ADiBD;EACE,qBAAA;EACA,2CAAA;EACA,4PAAA;EAIA,qBAAA;EACA,oBAAA;EClBD;ADqBD;EACE,qBAAA;EACA,0CAAA;EACA,uPAAA;EAIA,kBAAA;EACA,oBAAA;ECtBD;ADyBD;EACE,qBAAA;EACA,4CAAA;EACA,iQAAA;EAIA,qBAAA;EACA,oBAAA;EC1BD;ACrCD;EACE,8BAAA;EACA,2DAAA;EACA,uUAAA;EDuCD;ACjCD;;EAEE,8BAAA;EACA,aAAA;EACA,oBAAA;EACA,qBAAA;EACA,sBAAA;EACA,sBAAA;EACA,qCAAA;EACA,oCAAA;EDmCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AChCD;EACE,kBAAA;EDkCD;AEpVD;EACE,mDAAA;EACA,iBAAA;EACA,sBAAA;EACA,gBAAA;EACA,2BAAA;EFsVD;AEhVD;EACE,gBAAA;EACA,uBAAA;EACA,0BAAA;UAAA,kBAAA;EFkVD;AEhVC;;EAEE,gBAAA;EACA,uBAAA;EFkVH;AEhVC;EACE,eAAA;EFkVH;AE1UD;EACE,oBAAA;EF4UD;AEtUD;EACE,cAAA;EACA,sBAAA;EACA,2BAAA;EACA,2BAAA;EACA,oBAAA;EACA,2CAAA;UAAA,mCAAA;EC1CA,uBAAA;EACA,iBAAA;EACA,cAAA;EHmXD;AEpUD;EACE,iBAAA;EACA,kBAAA;EACA,oBAAA;EACA,gBAAA;EFsUD;AI5XD;;;;;;;;;;;;EAEE,sBAAA;EACA,kBAAA;EACA,kBAAA;EACA,gBAAA;EJwYD;AI7YD;;;;;;;;;;;;EAQI,gBAAA;EJmZH;AI/YD;;;EAGE,kBAAA;EACA,qBAAA;EJiZD;AI/YD;;;EAGE,kBAAA;EACA,qBAAA;EJiZD;AI9YD;EACE,qBAAA;EJgZD;AI7YD;;EAAU,iBAAA;EJiZT;AIhZD;;EAAU,iBAAA;EJoZT;AInZD;;EAAU,iBAAA;EJuZT;AItZD;;EAAU,iBAAA;EJ0ZT;AIzZD;;EAAU,iBAAA;EJ6ZT;AI5ZD;;EAAU,iBAAA;EJgaT;AI1ZD;EACE,iBAAA;EACA,sBAAA;EACA,kBAAA;EJ4ZD;AIzZD;EACE,qBAAA;EACA,iBAAA;EACA,yBAAA;EACA,kBAAA;EJ2ZD;AItZD;EAAA;IAFI,qBAAA;IJ4ZD;EACF;AIrZD;;EAEE,gBAAA;EACA,oBAAA;EJuZD;AInZD;EACE,gBAAA;EJqZD;AInZD;EACE,gBAAA;EJqZD;AInZD;EC/EE,gBAAA;ELqeD;AKpeC;EACE,gBAAA;ELseH;AItZD;EClFE,gBAAA;EL2eD;AK1eC;EACE,gBAAA;EL4eH;AIzZD;ECrFE,gBAAA;ELifD;AKhfC;EACE,gBAAA;ELkfH;AI5ZD;ECxFE,gBAAA;ELufD;AKtfC;EACE,gBAAA;ELwfH;AI/ZD;EC3FE,gBAAA;EL6fD;AK5fC;EACE,gBAAA;EL8fH;AIhaD;EAGE,gBAAA;EEnGA,2BAAA;ENogBD;AMngBC;EACE,2BAAA;ENqgBH;AIjaD;EEtGE,2BAAA;EN0gBD;AMzgBC;EACE,2BAAA;EN2gBH;AIpaD;EEzGE,2BAAA;ENghBD;AM/gBC;EACE,2BAAA;ENihBH;AIvaD;EE5GE,2BAAA;ENshBD;AMrhBC;EACE,2BAAA;ENuhBH;AI1aD;EE/GE,2BAAA;EN4hBD;AM3hBC;EACE,2BAAA;EN6hBH;AIxaD;EACE,sBAAA;EACA,qBAAA;EACA,kCAAA;EJ0aD;AIlaD;;EAEE,qBAAA;EJoaD;AIhaD;EACE,qBAAA;EJkaD;AIhaD;;EAEE,sBAAA;EJkaD;AI/YD;EAVE;IAEI,cAAA;IJ2ZH;EI7ZD;IAKI,oBAAA;IJ2ZH;EACF;AInZD;;EAEE,mCAAA;EJqZD;AIjZD;EACE,gCAAA;EACA,qBAAA;EACA,kBAAA;EJmZD;AItZD;EAMI,iBAAA;EACA,mBAAA;EACA,qBAAA;EACA,qBAAA;EJmZH;AI5ZD;;EAaI,iBAAA;EACA,sBAAA;EACA,oBAAA;EACA,gBAAA;EJmZH;AIjZG;;EACE,aAAA;EJoZL;AI/YC;EACE,qBAAA;EACA,iBAAA;EACA,iCAAA;EACA,gBAAA;EJiZH;AIrZC;EAOI,aAAA;EJiZL;AI3YD;EACE,qBAAA;EACA,sBAAA;EJ6YD;AIzYD;;EAEE,gBAAA;EJ2YD;AO/lBD;;;;EAIE,gEAAA;EPimBD;AO7lBD;EACE,kBAAA;EACA,gBAAA;EACA,gBAAA;EACA,2BAAA;EACA,oBAAA;EP+lBD;AO3lBD;EACE,kBAAA;EACA,gBAAA;EACA,gBAAA;EACA,2BAAA;EACA,oBAAA;EACA,kBAAA;EP6lBD;AOzlBD;EACE,cAAA;EACA,kBAAA;EACA,iBAAA;EACA,sBAAA;EACA,gBAAA;EACA,2BAAA;EACA,2BAAA;EACA,oBAAA;EACA,kBAAA;EP2lBD;AOvlBD;EACE,mBAAA;EPylBD;AQloBD;EACE,gBAAA;EACA,cAAA;EACA,oBAAA;EACA,sBAAA;EACA,2BAAA;EACA,2BAAA;EACA,oBAAA;EACA,8CAAA;UAAA,sCAAA;ERooBD;AQ5oBD;;ELGE,gBAAA;EACA,iBAAA;EACA,cAAA;EKQE,mBAAA;EACA,oBAAA;ERsoBH;AQloBC;;;EAGE,uBAAA;ERooBH;AQzpBD;EA0BI,cAAA;EACA,gBAAA;ERkoBH;AS5pBD;EACE,cAAA;EACA,iBAAA;EACA,qBAAA;EACA,kBAAA;EACA,oBAAA;EACA,oBAAA;EACA,8CAAA;EACA,4FAAA;UAAA,oFAAA;ET8pBD;AS3pBC;;EAEE,eAAA;EACA,gBAAA;ET6pBH;AS1pBC;;EAEE,eAAA;EACA,kBAAA;ET4pBH;ASzpBC;EACE,eAAA;ET2pBH;ASxpBC;;;EAGE,2BAAA;EACA,kCAAA;ECnCF,cAAA;EAGA,2BAAA;EDkCE,qBAAA;ET2pBH;AS5rBD;EAsCI,eAAA;EACA,oBAAA;EACA,gBAAA;EACA,UAAA;ETypBH;ASvpBG;EACE,iBAAA;EACA,QAAA;ETypBL;ASvpBG;EACE,UAAA;ETypBL;ASjpBD;EE5DE,gBAAA;EACA,2BAAA;EXgtBD;AW9sBC;;;;;;EAME,gBAAA;EACA,2BAAA;EACA,uBAAA;EXgtBH;AW9sBC;;;EAGE,qBAAA;EACA,uBAAA;EXgtBH;AW3sBG;;;;;;;;;;;;;;;;;;EAME,2BAAA;EACA,uBAAA;EXytBL;AS1rBD;EE1BI,gBAAA;EACA,2BAAA;EXutBH;AS3rBD;EE/DE,gBAAA;EACA,2BAAA;EX6vBD;AW3vBC;;;;;;EAME,gBAAA;EACA,2BAAA;EACA,uBAAA;EX6vBH;AW3vBC;;;EAGE,qBAAA;EACA,uBAAA;EX6vBH;AWxvBG;;;;;;;;;;;;;;;;;;EAME,2BAAA;EACA,uBAAA;EXswBL;ASpuBD;EE7BI,gBAAA;EACA,2BAAA;EXowBH;ASruBD;EElEE,gBAAA;EACA,2BAAA;EX0yBD;AWxyBC;;;;;;EAME,gBAAA;EACA,2BAAA;EACA,uBAAA;EX0yBH;AWxyBC;;;EAGE,qBAAA;EACA,uBAAA;EX0yBH;AWryBG;;;;;;;;;;;;;;;;;;EAME,2BAAA;EACA,uBAAA;EXmzBL;AS9wBD;EEhCI,gBAAA;EACA,2BAAA;EXizBH;AS/wBD;EErEE,gBAAA;EACA,2BAAA;EXu1BD;AWr1BC;;;;;;EAME,gBAAA;EACA,2BAAA;EACA,uBAAA;EXu1BH;AWr1BC;;;EAGE,qBAAA;EACA,uBAAA;EXu1BH;AWl1BG;;;;;;;;;;;;;;;;;;EAME,2BAAA;EACA,uBAAA;EXg2BL;ASxzBD;EEnCI,gBAAA;EACA,2BAAA;EX81BH;ASzzBD;EExEE,gBAAA;EACA,2BAAA;EXo4BD;AWl4BC;;;;;;EAME,gBAAA;EACA,2BAAA;EACA,uBAAA;EXo4BH;AWl4BC;;;EAGE,qBAAA;EACA,uBAAA;EXo4BH;AW/3BG;;;;;;;;;;;;;;;;;;EAME,2BAAA;EACA,uBAAA;EX64BL;ASl2BD;EEtCI,gBAAA;EACA,2BAAA;EX24BH;ASn2BD;EE3EE,gBAAA;EACA,2BAAA;EXi7BD;AW/6BC;;;;;;EAME,gBAAA;EACA,2BAAA;EACA,uBAAA;EXi7BH;AW/6BC;;;EAGE,qBAAA;EACA,uBAAA;EXi7BH;AW56BG;;;;;;;;;;;;;;;;;;EAME,2BAAA;EACA,uBAAA;EX07BL;AS54BD;EEzCI,gBAAA;EACA,2BAAA;EXw7BH;AS74BD;EE9EE,gBAAA;EACA,2BAAA;EX89BD;AW59BC;;;;;;EAME,gBAAA;EACA,2BAAA;EACA,uBAAA;EX89BH;AW59BC;;;EAGE,qBAAA;EACA,uBAAA;EX89BH;AWz9BG;;;;;;;;;;;;;;;;;;EAME,2BAAA;EACA,uBAAA;EXu+BL;ASt7BD;EE5CI,gBAAA;EACA,2BAAA;EXq+BH;ASv7BD;EACE,gDAAA;ETy7BD;ASv7BC;;EAEE,+CAAA;ETy7BH;ASt7BD;EACE,kBAAA;EACA,oBAAA;EACA,qBAAA;ETw7BD;ASh7BD;EACE,gBAAA;ETk7BD;ASh7BC;;EAEE,gBAAA;EACA,4BAAA;EACA,+BAAA;ETk7BH;AS96BG;;;;EAEE,gBAAA;EACA,uBAAA;ETk7BL;ASz6BD;;EElFE,oBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EX+/BD;AS76BD;;EErFE,oBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EXsgCD;ASj7BD;;EExFE,mBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EX6gCD;ASr7BD;;EE3FE,kBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EXohCD;ASt7BD;EACE,kBAAA;EACA,oBAAA;EACA,gBAAA;ETw7BD;ASl7BD;EACE,qBAAA;ETo7BD;AS96BD;EEjHE,oBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EXkiCD;AS96BD;EE/GE,gBAAA;EACA,2BAAA;EXgiCD;AW9hCC;;EAEE,2BAAA;EXgiCH;AW9hCC;;EAEE,2BAAA;EXgiCH;ASv7BD;EElHE,gBAAA;EACA,2BAAA;EX4iCD;AW1iCC;;EAEE,2BAAA;EX4iCH;AW1iCC;;EAEE,2BAAA;EX4iCH;ASh8BD;EErHE,gBAAA;EACA,2BAAA;EXwjCD;AWtjCC;;EAEE,2BAAA;EXwjCH;AWtjCC;;EAEE,2BAAA;EXwjCH;ASz8BD;EExHE,gBAAA;EACA,2BAAA;EXokCD;AWlkCC;;EAEE,2BAAA;EXokCH;AWlkCC;;EAEE,2BAAA;EXokCH;ASl9BD;EE3HE,gBAAA;EACA,2BAAA;EXglCD;AW9kCC;;EAEE,2BAAA;EXglCH;AW9kCC;;EAEE,2BAAA;EXglCH;AS39BD;EE9HE,gBAAA;EACA,2BAAA;EX4lCD;AW1lCC;;EAEE,2BAAA;EX4lCH;AW1lCC;;EAEE,2BAAA;EX4lCH;AYlpCG;EACE,gBAAA;EZopCL;AYlpCG;EACE,+CAAA;EACA,oBAAA;EZopCL;AYtpCG;EAKI,kBAAA;EACA,mBAAA;EZopCP;AYjpCG;EAEI,kBAAA;EACA,mBAAA;EZkpCP;AY/oCG;EAEI,gBAAA;EACA,iBAAA;EZgpCP;AY1oCD;EAEI,kBAAA;EZ2oCH;AYhoCC;EACE,kBAAA;EZkoCH;AY3nCC;EACE,mBAAA;EZ6nCH;AYtnCC;EACE,oBAAA;EZwnCH;AYjnCC;EACE,oBAAA;EZmnCH;AY5mCD;EACE,yBAAA;EACA,wBAAA;EZ8mCD;AY5mCD;EACE,yBAAA;EACA,wBAAA;EZ8mCD;AY3mCD;EACE,yBAAA;EZ6mCD;AY3mCD;EACE,yBAAA;EZ6mCD;AY1mCD;;;EAGE,kBAAA;EZ4mCD;AYzmCD;EACE,eAAA;EACA,0BAAA;UAAA,kBAAA;EZ2mCD;AYtmCD;EACE,kCAAA;EACA,kBAAA;EZwmCD;AYlmCC;EACE,gBAAA;EZomCH;AYtmCD;EAKI,iBAAA;EACA,eAAA;EZomCH;AaptCD;EACE,gBAAA;EACA,aAAA;EACA,YAAA;EACA,yBAAA;EACA,iBAAA;EACA,sBAAA;EACA,gBAAA;EACA,qBAAA;EbstCD;AahtCD;EACE,iBAAA;EACA,mBAAA;EACA,mBAAA;EbktCD;Aa9sCD;EACE,qCAAA;EbgtCD;Aa3sCD;EACE,qBAAA;EACA,iBAAA;EACA,kBAAA;Eb6sCD;AchpCC;;EAAgC,gBAAA;EACA,YAAA;EdopCjC;AcnpCC;;EAAgC,gBAAA;EdupCjC;ActpCC;;EAAgC,gBAAA;Ed0pCjC;AazrCD;;EACE,2BAAA;EACA,gBAAA;EACA,mDAAA;EACA,iBAAA;EACA,oBAAA;EACA,mBAAA;EACA,cAAA;EACA,oBAAA;EACA,kBAAA;EACA,4FAAA;UAAA,oFAAA;Eb4rCD;Ae1tCC;;;;EAEE,uBAAA;EACA,YAAA;EACA,kBAAA;Ef8tCH;Aa3rCC;;;;;;EAGE,2BAAA;EACA,uBAAA;EACA,gBAAA;EACA,iBAAA;EH9FF,cAAA;EAGA,2BAAA;EV6xCD;Aa7rCC;;EACE,2BAAA;EbgsCH;Aa9rCG;;EACE,uBAAA;EbisCL;Aa/rCG;;EACE,uBAAA;EbksCL;Aa5rCD;;;;EEhDE,cAAA;EACA,mBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EfkvCD;AehvCC;;;;EACE,cAAA;EACA,mBAAA;EfqvCH;AelvCC;;;;;;;;EAEE,cAAA;Ef0vCH;AaltCD;;;EErDE,cAAA;EACA,oBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;Ef4wCD;Ae1wCC;;;EACE,cAAA;EACA,mBAAA;Ef8wCH;Ae3wCC;;;;;;EAEE,cAAA;EfixCH;AapuCD;;;;;EE1DE,cAAA;EACA,oBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EfqyCD;AenyCC;;;EACE,cAAA;EACA,mBAAA;EfuyCH;AepyCC;;;;;;EAEE,cAAA;Ef0yCH;AapvCD;EACE,oBAAA;EACA,UAAA;EACA,YAAA;EACA,iBAAA;EACA,mBAAA;EACA,iBAAA;EACA,gBAAA;EACA,+BAAA;EACA,qBAAA;EACA,oBAAA;EACA,sBAAA;EbsvCD;AanvCD;;EAEE,iBAAA;EACA,mBAAA;EACA,qBAAA;EACA,aAAA;EACA,cAAA;EbqvCD;AanvCD;;EAEE,iBAAA;EACA,mBAAA;EACA,aAAA;EACA,cAAA;EACA,qBAAA;EbqvCD;AanvCD;;;EAEE,mBAAA;EACA,cAAA;EACA,aAAA;EACA,qBAAA;EbsvCD;AalvCD;;;;;;EEhKI,gBAAA;Ef05CH;Aa1vCD;;EE5JI,gBAAA;EACA,uBAAA;EACA,kBAAA;Ef05CH;Acz0CC;;EAAgC,gBAAA;EACA,YAAA;Ed60CjC;Ac50CC;;EAAgC,gBAAA;Edg1CjC;Ac/0CC;;EAAgC,gBAAA;Edm1CjC;Aep6CG;;EACE,uBAAA;EACA,kBAAA;Efu6CL;AalxCD;EEhJI,gBAAA;EACA,uBAAA;EACA,2BAAA;Efq6CH;AavxCD;EE3II,gBAAA;Efq6CH;AavxCD;;;;;;EEnKI,gBAAA;Efk8CH;Aa/xCD;;EE/JI,gBAAA;EACA,uBAAA;EACA,kBAAA;Efk8CH;Acj3CC;;EAAgC,gBAAA;EACA,YAAA;Edq3CjC;Acp3CC;;EAAgC,gBAAA;Edw3CjC;Acv3CC;;EAAgC,gBAAA;Ed23CjC;Ae58CG;;EACE,uBAAA;EACA,kBAAA;Ef+8CL;AavzCD;EEnJI,gBAAA;EACA,uBAAA;EACA,2BAAA;Ef68CH;Aa5zCD;EE9II,gBAAA;Ef68CH;Aa5zCD;;;;;;EEtKI,gBAAA;Ef0+CH;Aap0CD;;EElKI,gBAAA;EACA,uBAAA;EACA,kBAAA;Ef0+CH;Acz5CC;;EAAgC,gBAAA;EACA,YAAA;Ed65CjC;Ac55CC;;EAAgC,gBAAA;Edg6CjC;Ac/5CC;;EAAgC,gBAAA;Edm6CjC;Aep/CG;;EACE,uBAAA;EACA,kBAAA;Efu/CL;Aa51CD;EEtJI,gBAAA;EACA,uBAAA;EACA,2BAAA;Efq/CH;Aaj2CD;EEjJI,gBAAA;Efq/CH;Aah2CD;;;;;;;;EAIE,qBAAA;EACA,gBAAA;EACA,+BAAA;EH1LA,cAAA;EAGA,2BAAA;EV+hDD;Aa91CD;EACE,iBAAA;EACA,oBAAA;EACA,gBAAA;Ebg2CD;Aax1CD;EACE,oBAAA;EACA,qBAAA;Eb01CD;Aal1CD;;;;EASI,eAAA;EACA,kBAAA;EACA,gBAAA;Eb+0CH;Aan0CC;EAAA;IALI,kBAAA;IACA,qBAAA;Ib40CH;EACF;Aah2CD;EG7ME,oBAAA;EACA,qBAAA;EhBgjDD;Aap2CD;EA6BI,kBAAA;EACA,qBAAA;Eb00CH;Aa3zCG;EAAA;IALI,iBAAA;IACA,kBAAA;IACA,mBAAA;Ibo0CL;EACF;AatzCC;EAAA;IALM,iBAAA;IACA,kBAAA;IACA,qBAAA;Ib+zCL;EACF;AapzCC;EAAA;IALM,iBAAA;IACA,kBAAA;IACA,qBAAA;Ib6zCL;EACF;AiBllDD;;EAEI,kBAAA;EjBolDH;AiB3kDD;;;;EFiDE,cAAA;EACA,oBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EfgiDD;Ae9hDC;;;;EACE,cAAA;EACA,mBAAA;EfmiDH;AehiDC;;;;;;;;EAEE,cAAA;EfwiDH;AiBnmDD;;;;EF8CE,cAAA;EACA,oBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;Ef2jDD;AezjDC;;;;EACE,cAAA;EACA,mBAAA;Ef8jDH;Ae3jDC;;;;;;;;EAEE,cAAA;EfmkDH;AiB3nDD;;;;EF2CE,cAAA;EACA,mBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EfslDD;AeplDC;;;;EACE,cAAA;EACA,mBAAA;EfylDH;AetlDC;;;;;;;;EAEE,cAAA;Ef8lDH;AiBhpDD;EACE,oBAAA;EACA,iBAAA;EACA,gBAAA;EACA,oBAAA;EACA,2BAAA;EACA,2BAAA;EACA,oBAAA;EACA,4FAAA;UAAA,oFAAA;EjBkpDD;AiBhpDC;;;EAGE,gBAAA;EjBkpDH;AermDC;;;;;;EGjFA,+BAAA;EACG,4BAAA;ElB8rDJ;AevmDC;;;;;;EGhFA,8BAAA;EACG,2BAAA;ElB+rDJ;AiBzpDD;;EAGI,2BAAA;EACA,uBAAA;EjB0pDH;AiB9pDD;;EAQM,4BAAA;EjB0pDL;AiBlqDD;;EAWM,uBAAA;EACA,2BAAA;EACA,gBAAA;EjB2pDL;AiBxqDD;;ENnDE,gBAAA;EACA,2BAAA;EX+tDD;AW7tDC;;;;;;;;;;;;EAME,gBAAA;EACA,2BAAA;EACA,uBAAA;EXquDH;AWnuDC;;;;;;EAGE,qBAAA;EACA,uBAAA;EXwuDH;AWnuDG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAME,2BAAA;EACA,uBAAA;EXmwDL;AiB7uDD;;ENjBI,gBAAA;EACA,2BAAA;EXkwDH;AiB3tDD;EAEI,2BAAA;EACA,2BAAA;EACA,gBAAA;EACA,mBAAA;EACA,cAAA;EjB4tDH;AiBluDD;EN1EE,gBAAA;EACA,2BAAA;EX+yDD;AW7yDC;;;;;;EAME,gBAAA;EACA,2BAAA;EACA,uBAAA;EX+yDH;AW7yDC;;;EAGE,qBAAA;EACA,uBAAA;EX+yDH;AW1yDG;;;;;;;;;;;;;;;;;;EAME,2BAAA;EACA,uBAAA;EXwzDL;AiB3wDD;ENxCI,gBAAA;EACA,2BAAA;EXszDH;AiBpwDC;EACE,mBAAA;EjBswDH;AiBpwDC;EACE,mBAAA;EjBswDH;AiBpwDC;EACE,mBAAA;EjBswDH;AiBpwDC;EACE,uBAAA;EACA,oBAAA;EjBswDH;AiBpwDC;EACE,sBAAA;EACA,mBAAA;EjBswDH;AiBpwDC;EACE,gCAAA;EjBswDH;AiBpwDC;EAEI,gBAAA;EjBqwDL;AiB7vDD;;;EC/GE,kCAAA;EACG,+BAAA;ElBi3DJ;AiB9vDG;;;ECpHF,kCAAA;EACG,+BAAA;ElBu3DJ;AiBjwDG;;;ECvHF,kCAAA;EACG,+BAAA;ElB63DJ;AiB/wDD;;;ECvGE,iCAAA;EACG,8BAAA;ElB23DJ;AiBrwDG;;;ECvHF,iCAAA;EACG,8BAAA;ElBi4DJ;AiBxwDG;;;EC1HF,iCAAA;EACG,8BAAA;ElBu4DJ;AiBjyDD;;EAyBI,iBAAA;EjB4wDH;AmBv5DD;;EAEE,qBAAA;EACA,oBAAA;EACA,oBAAA;EACA,uCAAA;UAAA,+BAAA;EACA,iBAAA;EACA,kBAAA;EnBy5DD;AmBh6DD;;EAUI,gBAAA;EACA,gBAAA;EACA,cAAA;EACA,QAAA;EACA,SAAA;EACA,oBAAA;EACA,aAAA;EACA,oBAAA;EACA,mBAAA;EACA,iBAAA;EACA,iBAAA;EnB05DH;AmB96DD;;ETDE,YAAA;EAGA,0BAAA;EVi7DD;AmBv5DD;;;;EAII,uBAAA;EACA,oBAAA;EACA,SAAA;EACA,QAAA;EACA,+BAAA;EACA,WAAA;EACA,YAAA;EACA,sBAAA;UAAA,cAAA;EACA,uCAAA;UAAA,+BAAA;EnBy5DH;AmBv5DG;;;;EACE,8BAAA;EACA,aAAA;EACA,oBAAA;EACA,qBAAA;EACA,sBAAA;EACA,sBAAA;EACA,qCAAA;EACA,oCAAA;EnB45DL;AmBx5DD;EAEI,kBAAA;EnBy5DH;AmB35DD;EAKI,kBAAA;EnBy5DH;AmBt5DD;EAEI,kBAAA;EnBu5DH;AmBz5DD;EAKI,kBAAA;EnBu5DH;AmBn5DD;;;;EAII,0BAAA;EACA,YAAA;EACA,oBAAA;EACA,WAAA;EACA,YAAA;EACA,SAAA;EACA,QAAA;EACA,aAAA;EACA,cAAA;EnBq5DH;AmB/4DG;;;;ET1FF,YAAA;EAGA,0BAAA;EV6+DD;AmBt5DG;;;;EAKI,YAAA;EACA,sBAAA;UAAA,cAAA;EnBu5DP;AmBn5DG;;;;EACE,gBAAA;EnBw5DL;AmBz5DG;;;;ETpGF,YAAA;EAGA,0BAAA;EVigED;AmBh6DG;;;;EAOI,YAAA;EACA,sBAAA;UAAA,cAAA;EACA,gBAAA;EnB+5DP;AmB35DG;;;;EACE,iBAAA;EACA,gBAAA;EnBg6DL;AmBl6DG;;;;EAKI,YAAA;EACA,sBAAA;UAAA,cAAA;EnBm6DP;AmBz6DG;;;;ETjHF,YAAA;EAGA,0BAAA;EV8hED;AmBn6DG;;;;EACE,gBAAA;EnBw6DL;AmBz6DG;;;;ET9HF,YAAA;EAGA,0BAAA;EV2iED;AmBh7DG;;;;EAOI,YAAA;EACA,sBAAA;UAAA,cAAA;EACA,gBAAA;EnB+6DP;AmB36DG;;;;EACE,gBAAA;EnBg7DL;AmBj7DG;;;;EAII,YAAA;EACA,sBAAA;UAAA,cAAA;EnBm7DP;AmBx7DG;;;;ET3IF,YAAA;EAGA,0BAAA;EVukED;AmBr7DK;;;;EACE,kBAAA;EACA,oBAAA;EACA,QAAA;EACA,SAAA;EACA,mBAAA;EACA,aAAA;EACA,oBAAA;EACA,gBAAA;EACA,iBAAA;EACA,aAAA;EnB07DP;AmB76DG;;;;EACE,gBAAA;EnBk7DL;AmB/6DG;;;;EACE,gBAAA;EnBo7DL;AmBj7DG;;;;EACE,iBAAA;EACA,gBAAA;EnBs7DL;AmBp7DK;;;;EACE,gBAAA;EnBy7DP;AmBr7DG;;;;EACE,gBAAA;EnB07DL;AmBj7DC;;EACE,gBAAA;EACA,oBAAA;EnBo7DH;AmBt7DC;;EAKI,gBAAA;EnBq7DL;AmBj7DK;;;;EACE,gBAAA;EnBs7DP;AmBv7DK;;;;EAII,gBAAA;EnBy7DT;AmBt7DK;;;;EACE,iCAAA;EnB27DP;AmBz7DK;;;;EACE,iCAAA;EnB87DP;AmB/7DK;;;;EAII,iCAAA;EnBi8DT;AmB17DD;;EAEE,kBAAA;EnB47DD;AmBv7DD;;EACE,oBAAA;EnB07DD;AoBtqED;EACE,2BAAA;EACA,2BAAA;EACA,oBAAA;EACA,qBAAA;EACA,0BAAA;EACA,kBAAA;EACA,cAAA;EpBwqED;AoB/qED;EAUI,oBAAA;EACA,2BAAA;EACA,gBAAA;EACA,iBAAA;EACA,iBAAA;EACA,uBAAA;EACA,oBAAA;EACA,wBAAA;EACA,kBAAA;EACA,qBAAA;EACA,mBAAA;EACA,cAAA;EACA,mBAAA;EACA,iCAAA;UAAA,yBAAA;EpBwqEH;AoB/rED;EA0BM,gBAAA;EACA,iBAAA;EACA,iBAAA;EACA,oBAAA;EACA,UAAA;EACA,mBAAA;EACA,uBAAA;EACA,QAAA;EACA,aAAA;EACA,WAAA;EACA,qBAAA;EACA,YAAA;EVtCJ,YAAA;EAGA,0BAAA;EUqCI,yCAAA;UAAA,iCAAA;EpByqEL;AoBvqEK;EACE,kBAAA;EACA,8BAAA;EACA,qCAAA;EACA,oCAAA;EACA,mBAAA;EpByqEP;AoBrqEG;EACE,2BAAA;EACA,gBAAA;EACA,qBAAA;EACA,oBAAA;EpBuqEL;AoB3qEG;EAOI,YAAA;EACA,sBAAA;UAAA,cAAA;EpBuqEP;AoBjuED;EAgEI,iBAAA;EACA,cAAA;EACA,kBAAA;EACA,eAAA;EACA,+BAAA;EACA,YAAA;EACA,WAAA;EACA,wBAAA;EACA,oBAAA;EACA,iBAAA;EACA,qBAAA;EACA,cAAA;EACA,gBAAA;EpBoqEH;AoBlqEG;EACE,cAAA;EACA,mBAAA;EpBoqEL;AoB/pED;EACE,aAAA;EACA,aAAA;EACA,WAAA;EpBiqED;AoB/pED;EACE,gCAAA;EACA,2BAAA;EACA,6BAAA;EpBiqED;AoB5pED;EACE,qBAAA;EpB8pED;AoB/pED;EAII,uBAAA;EACA,kBAAA;EpB8pEH;AoBnqED;EAQI,2BAAA;EACA,gBAAA;EpB8pEH;AoB5pEG;EACE,2BAAA;EACA,gBAAA;EpB8pEL;AoBxpED;EACE,aAAA;EACA,qBAAA;EpB0pED;AoB5pED;EAKI,kBAAA;EpB0pEH;AoB/pED;EAQI,aAAA;EACA,kBAAA;EACA,kBAAA;EpB0pEH;AqBzxED;EACE,aAAA;ErB2xED;AqB5xED;EAII,aAAA;EACA,iBAAA;EACA,2BAAA;EACA,gBAAA;EACA,2BAAA;EACA,oBAAA;ErB2xEH;AqBpyED;EAcM,mBAAA;EACA,iBAAA;EACA,oBAAA;EACA,WAAA;ErByxEL;AqBtxEG;;EAGI,mBAAA;ErBuxEP;AqBpxEG;;EAEE,iBAAA;EACA,aAAA;EACA,2BAAA;ErBsxEL;AsBnzED;EACE,qBAAA;EACA,qBAAA;EACA,cAAA;EACA,kBAAA;EtBqzED;AsBjzED;EACE,qBAAA;EACA,mBAAA;EACA,kBAAA;EtBmzED;AsB7yED;EACE,2BAAA;EtB+yED;AsB7yED;EACE,2BAAA;EtB+yED;AsB7yED;EACE,2BAAA;EtB+yED;AsB7yED;EACE,2BAAA;EtB+yED;AuBz0ED;EDAE,qBAAA;EACA,qBAAA;EACA,cAAA;EACA,kBAAA;ECDA,qBAAA;EACA,oBAAA;EACA,iBAAA;EvB80ED;AuB30ED;EACE,2BAAA;EACA,oBAAA;EACA,iBAAA;EACA,cAAA;EACA,oBAAA;EACA,aAAA;EACA,YAAA;EACA,qCAAA;UAAA,6BAAA;EvB60ED;AuB30EC;;EAEE,2BAAA;EACA,eAAA;EvB60EH;AuB30EC;EACE,2BAAA;EvB60EH;AuBz0ED;EACE,2BAAA;EACA,gBAAA;EACA,cAAA;EACA,oBAAA;EACA,YAAA;EvB20ED;AuBt0ED;EACE,2BAAA;EACA,oBAAA;EACA,aAAA;EACA,YAAA;EvBw0ED;AuBn0ED;EACE,cAAA;EACA,iBAAA;EACA,kBAAA;EvBq0ED;AuBn0EC;EACE,aAAA;EACA,aAAA;EvBq0EH;AuB9zED;EAEI,mBAAA;EACA,WAAA;EvB+zEH;AuB7zEG;EACE,oBAAA;EvB+zEL;AuBr0ED;EAUI,8BAAA;EvB8zEH;AuBx0ED;EAaI,aAAA;EACA,sBAAA;EvB8zEH;AuBvzED;EACE,aAAA;EvByzED;AuB1zED;EAII,mBAAA;EACA,sBAAA;EACA,WAAA;EvByzEH;AuB/zED;EASI,aAAA;EACA,WAAA;EACA,8BAAA;EvByzEH;AuBp0ED;EAcI,oBAAA;EACA,YAAA;EvByzEH;AwB35ED;EACE,2BAAA;EACA,oBAAA;EACA,gBAAA;EACA,iBAAA;EACA,kBAAA;EACA,uBAAA;ExB65ED;AwB15EG;;EAGI,mBAAA;EACA,4BAAA;ExB25EP;AwBx6ED;;EAmBM,kBAAA;EACA,cAAA;EACA,gCAAA;EACA,gBAAA;EACA,wBAAA;EACA,uBAAA;EACA,qBAAA;EACA,4BAAA;EACA,oBAAA;ExBy5EL;AwBv5EK;;;;EAEE,2BAAA;ExB25EP;AwBz5EK;;EACE,2BAAA;ExB45EP;AwB97ED;;EAuCQ,kBAAA;ExB25EP;AwBl8ED;;EA0CQ,kBAAA;ExB45EP;AyBt8ED;EACE,oBAAA;EACA,gBAAA;EACA,qBAAA;EACA,gBAAA;EACA,YAAA;EACA,uBAAA;EACA,oBAAA;EACA,sBAAA;EzBw8ED;AyBl8EC;EAAA;IAHE,uBAAA;IzBy8ED;EACF;AyB/7EC;EAAA;IAPE,cAAA;IACA,wBAAA;IACA,gBAAA;IACA,qBAAA;IACA,oBAAA;IzB08ED;EACF;AyB99ED;EAuBI,uBAAA;EACA,oBAAA;EACA,wBAAA;EACA,sBAAA;EzB08EH;AyBp+ED;EA6BM,kBAAA;EzB08EL;AyBt8EG;;EAEI,2BAAA;EACA,gBAAA;EACA,uBAAA;EzBw8EP;AyBt8EO;;;;;;EAGE,2BAAA;EACA,gBAAA;EACA,uBAAA;EzB28ET;AyBx8EK;;;;EAGI,WAAA;EzB28ET;AyBz8ES;;;;;;;;;;;;EAGE,2BAAA;EACA,gBAAA;EzBo9EX;AyB/8EG;;EAGI,4BAAA;EACA,mBAAA;EzBg9EP;AyB98EK;;EAGI,sBAAA;EzB+8ET;AyB38EG;EACE,iBAAA;EzB68EL;AyBz8EO;;;;;;EAGE,4BAAA;EzB88ET;AyB18EG;;;;EAII,iCAAA;EACA,iBAAA;EACA,iBAAA;EACA,oBAAA;EACA,+BAAA;EzB48EP;AyBz8EG;;EAGI,oBAAA;EzB08EP;AyBv8EG;;EAGI,gBAAA;EACA,4CAAA;EACA,6BAAA;EACA,qBAAA;EzBw8EP;AyBt8EO;;;;;;EAGE,4CAAA;EACA,gBAAA;EzB28ET;AyBr7EG;EAhBE;;IAEE,2BAAA;IACA,oBAAA;IACA,UAAA;IACA,QAAA;IACA,aAAA;IACA,4BAAA;IzBw8EL;EyBt8EG;IACE,SAAA;IACA,aAAA;IACA,4BAAA;IzBw8EL;EACF;AyB9kFD;;EA4IM,uBAAA;EACA,yBAAA;EACA,cAAA;EACA,gCAAA;EACA,gBAAA;EACA,iBAAA;EACA,mBAAA;EACA,kBAAA;EACA,iBAAA;EACA,eAAA;EACA,oBAAA;EACA,oBAAA;EACA,oCAAA;UAAA,4BAAA;EzBs8EL;AyBp8EK;;;;EAEE,2BAAA;EACA,gBAAA;EzBw8EP;AyBt8EK;;EACE,2BAAA;EACA,gBAAA;EzBy8EP;AyBl8EG;;EAEE,mBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EACA,qBAAA;EzBo8EL;AyB18EG;;EASI,iBAAA;EACA,mBAAA;EACA,kBAAA;EzBq8EP;AyBj8EG;EACE,kBAAA;EACA,iBAAA;EzBm8EL;AyBr8EG;EAKI,oBAAA;EACA,kBAAA;EzBm8EP;AyBx4ED;EAAA;IAjDQ,gBAAA;IACA,WAAA;IACA,YAAA;IzB67EL;EyB37EK;IACE,kCAAA;IACA,wBAAA;IzB67EP;EyB37EO;IAAgB,8BAAA;IzB87EvB;EyB77EO;IAAgB,8BAAA;IzBg8EvB;EyB/7EO;IAAgB,8BAAA;IzBk8EvB;EyBj8EO;IAAgB,8BAAA;IzBo8EvB;EyBn8EO;IAAgB,8BAAA;IzBs8EvB;EyBr8EO;IAAgB,8BAAA;IzBw8EvB;EyBj9EK;IAUqB,oBAAA;IzB08E1B;EyBx8EK;IACE,YAAA;IACA,oBAAA;IACA,4BAAA;IzB08EP;EyBx8EK;IACE,kCAAA;IACA,oBAAA;IACA,UAAA;IACA,QAAA;IACA,0BAAA;IzB08EP;EyBx8EO;IAAgB,8BAAA;IzB28EvB;EyB18EO;IAAgB,8BAAA;IzB68EvB;EyB58EO;IAAgB,8BAAA;IzB+8EvB;EyB98EO;IAAgB,8BAAA;IzBi9EvB;EyBh9EO;IAAgB,8BAAA;IzBm9EvB;EyBl9EO;IAAgB,8BAAA;IzBq9EvB;EyBj+EK;IAaqB,qBAAA;IzBu9E1B;EyBx8EH;IAVQ,gBAAA;IzBq9EL;EyB38EH;IANY,kBAAA;IzBo9ET;EACF;AyB38ED;EC5PI,2BAAA;E1B0sFH;A0BvsFK;EAEI,6BAAA;E1BwsFT;AyBj9ED;;ECnPQ,4BAAA;E1BwsFP;A0BtsFO;;;;EACE,4BAAA;EACA,2BAAA;E1B2sFT;A0BzsFO;;EACE,2BAAA;E1B4sFT;A0BzsFK;;EAEI,4BAAA;EACA,2BAAA;E1B2sFT;A0BzsFS;;;;EACE,4BAAA;EACA,2BAAA;E1B8sFX;AyB3+ED;EC7PI,2BAAA;E1B2uFH;A0BxuFK;EAEI,6BAAA;E1ByuFT;AyBj/ED;;ECpPQ,4BAAA;E1ByuFP;A0BvuFO;;;;EACE,4BAAA;EACA,2BAAA;E1B4uFT;A0B1uFO;;EACE,2BAAA;E1B6uFT;A0B1uFK;;EAEI,4BAAA;EACA,2BAAA;E1B4uFT;A0B1uFS;;;;EACE,4BAAA;EACA,2BAAA;E1B+uFX;AyB3gFD;EC9PI,2BAAA;E1B4wFH;A0BzwFK;EAEI,6BAAA;E1B0wFT;AyBjhFD;;ECrPQ,4BAAA;E1B0wFP;A0BxwFO;;;;EACE,4BAAA;EACA,2BAAA;E1B6wFT;A0B3wFO;;EACE,2BAAA;E1B8wFT;A0B3wFK;;EAEI,4BAAA;EACA,2BAAA;E1B6wFT;A0B3wFS;;;;EACE,4BAAA;EACA,2BAAA;E1BgxFX;AyB3iFD;EC/PI,2BAAA;E1B6yFH;A0B1yFK;EAEI,6BAAA;E1B2yFT;AyBjjFD;;ECtPQ,4BAAA;E1B2yFP;A0BzyFO;;;;EACE,4BAAA;EACA,2BAAA;E1B8yFT;A0B5yFO;;EACE,2BAAA;E1B+yFT;A0B5yFK;;EAEI,4BAAA;EACA,2BAAA;E1B8yFT;A0B5yFS;;;;EACE,4BAAA;EACA,2BAAA;E1BizFX;AyB3kFD;EChQI,2BAAA;E1B80FH;A0B30FK;EAEI,6BAAA;E1B40FT;AyBjlFD;;ECvPQ,4BAAA;E1B40FP;A0B10FO;;;;EACE,4BAAA;EACA,2BAAA;E1B+0FT;A0B70FO;;EACE,2BAAA;E1Bg1FT;A0B70FK;;EAEI,4BAAA;EACA,2BAAA;E1B+0FT;A0B70FS;;;;EACE,4BAAA;EACA,2BAAA;E1Bk1FX;AyBtmFG;EACE,4BAAA;EzBwmFL;AyBtmFK;;EAGI,wBAAA;EzBumFT;AyBnmFG;EACE,4BAAA;EzBqmFL;AyBnmFG;;;;EAII,yBAAA;EACA,cAAA;EACA,iCAAA;EACA,mBAAA;EACA,oBAAA;EACA,4BAAA;EzBqmFP;AyBnmFO;;;;;;;;;;;;EAGE,kCAAA;EzB8mFT;AyBxmFK;EAAA;;;;IAFI,iBAAA;IzBinFP;EACF;AyB9mFG;EACE,kBAAA;EzBgnFL;AyBjnFG;;EAKI,gCAAA;EACA,oBAAA;EACA,WAAA;EACA,4BAAA;EzBgnFP;AyB7mFG;;EAGI,2BAAA;EACA,uBAAA;EACA,8BAAA;EACA,gBAAA;EACA,sBAAA;EzB8mFP;AyB5mFO;;;;EAEE,2BAAA;EACA,uBAAA;EACA,gBAAA;EzBgnFT;AyB7mFK;;EAEE,uBAAA;EzB+mFP;AyB7mFK;EACE,mBAAA;EzB+mFP;AyBprFD;;EA4EM,qBAAA;EACA,2BAAA;EACA,gBAAA;EACA,mBAAA;EACA,qBAAA;EACA,cAAA;EACA,kBAAA;EACA,gBAAA;EACA,qBAAA;EACA,8BAAA;EACA,6FAAA;UAAA,qFAAA;EzB4mFL;AyB1mFK;;;;EAEE,2BAAA;EACA,uBAAA;EACA,gBAAA;EACA,+FAAA;UAAA,uFAAA;EzB8mFP;AyB5mFK;;EACE,2BAAA;EACA,uBAAA;EzB+mFP;AyBvmFD;EACE,iBAAA;EACA,kBAAA;EACA,uBAAA;EACA,kBAAA;EACA,YAAA;EACA,cAAA;EzBymFD;AyB/mFD;EASI,iBAAA;EzBymFH;AyBvmFG;EACE,qBAAA;EzBymFL;AyBvmFG;EACE,oBAAA;EzBymFL;AyBvmFG;EAEI,gBAAA;EzBwmFP;AyB3nFD;EAuBM,gBAAA;EzBumFL;AyB/kFC;EAAA;IAnBE,kBAAA;IACA,oBAAA;IzBsmFD;EyBnmFG;IACE,gBAAA;IACA,qBAAA;IACA,kBAAA;IACA,YAAA;IzBqmFL;EyBnmFG;IACE,cAAA;IACA,mBAAA;IACA,mBAAA;IACA,YAAA;IzBqmFL;EACF;AyB9lFD;EAAA;IAFI,cAAA;IzBomFD;EACF;AyB/lFD;EAEI,iBAAA;EACA,aAAA;EACA,WAAA;EACA,oBAAA;EzBgmFH;AyBrmFD;EAQM,gBAAA;EACA,iBAAA;EzBgmFL;AyB9lFK;;EAGI,4BAAA;EzB+lFT;AyB5lFK;;EAGI,uCAAA;EzB6lFT;AyBjnFD;;EAyBQ,mBAAA;EACA,gBAAA;EACA,aAAA;EACA,uBAAA;EACA,oBAAA;EACA,eAAA;EzB4lFP;AyBvlFC;EACE,oBAAA;EzBylFH;A2B/hGD;EACE,iBAAA;EACA,oBAAA;EACA,eAAA;E3BiiGD;A2B/hGC;EjBPA,YAAA;EAGA,4BAAA;EVuiGD;A2BliGC;EAAW,kBAAA;EAAmB,gBAAA;E3BsiG/B;A2BriGC;EAAW,kBAAA;EAAmB,gBAAA;E3ByiG/B;A2BxiGC;EAAW,iBAAA;EAAmB,gBAAA;E3B4iG/B;A2B3iGC;EAAW,mBAAA;EAAmB,gBAAA;E3B+iG/B;A2B3iGD;EACE,kBAAA;EACA,oBAAA;EACA,oBAAA;EACA,gBAAA;EACA,2BAAA;EACA,oBAAA;E3B6iGD;A2BxiGC;EACE,mBAAA;EACA,yBAAA;EACA,2BAAA;E3B0iGH;A2BxiGC;EACE,kBAAA;EACA,6BAAA;EACA,6BAAA;E3B0iGH;A2BxiGC;EACE,kBAAA;EACA,6BAAA;EACA,4BAAA;E3B0iGH;A2BxiGC;EACE,mBAAA;EACA,yBAAA;EACA,8BAAA;E3B0iGH;A4BplGD;EACE,uBAAA;EACA,UAAA;EACA,WAAA;EACA,kBAAA;EACA,wBAAA;EACA,uBAAA;EACA,qCAAA;EACA,oCAAA;EACA,qDAAA;UAAA,6CAAA;E5BslGD;A4BllGD;;EACE,eAAA;EACA,2BAAA;EACA,kBAAA;EACA,cAAA;EACA,iBAAA;EACA,YAAA;EACA,iBAAA;EACA,oBAAA;EACA,kBAAA;E5BqlGD;A4B9lGD;ECbE,aAAA;EACA,eAAA;EACA,kBAAA;EACA,4CAAA;E7B8mGD;A4BpmGD;EAkBI,mBAAA;EACA,oBAAA;EACA,gBAAA;E5BqlGH;A4BzmGD;EVfE,8BAAA;EACC,6BAAA;ElB2nGF;A4B7mGD;EVPE,iCAAA;EACC,gCAAA;ElBunGF;A4BllGC;EACE,eAAA;EACA,aAAA;EACA,iBAAA;EACA,2BAAA;EACA,gBAAA;EACA,2BAAA;EACA,oBAAA;E5BolGH;A4B3lGC;EAWM,mBAAA;E5BmlGP;A4BjlGK;;EAGI,mBAAA;EACA,kBAAA;E5BklGT;A4BzkGC;;EAEE,gBAAA;EACA,4CAAA;E5B2kGH;A4BrkGC;;;EAGE,gBAAA;EACA,2BAAA;E5BukGH;A4B9jGC;;;EAGE,gBAAA;EACA,+BAAA;EACA,qBAAA;E5BgkGH;A4BxjGD;EACE,YAAA;EACA,UAAA;E5B0jGD;A4BljGD;EACE,SAAA;EACA,aAAA;E5BojGD;A4BhjGD;EACE,mBAAA;EACA,oBAAA;EACA,iBAAA;EACA,2BAAA;EACA,8BAAA;E5BkjGD;A4BhjGC;EACE,iBAAA;E5BkjGH;A4B7iGD;EACE,cAAA;E5B+iGD;A4BxiGD;;EAII,0BAAA;EACA,sBAAA;E5BwiGH;A4B7iGD;;EASI,eAAA;EACA,oBAAA;E5BwiGH;A4BhiGD;EACE,2BAAA;E5BkiGD;A4BniGD;EChKE,aAAA;EACA,eAAA;EACA,kBAAA;EACA,yCAAA;E7BssGD;A4BziGD;EASI,kCAAA;E5BmiGH;A4B/hGG;;EAEE,kCAAA;EACA,yCAAA;E5BiiGL;A4B3hGG;;;EAGE,kCAAA;EACA,2BAAA;E5B6hGL;A4BrhGG;;;EAGE,iCAAA;E5BuhGL;A4BlhGG;;EAEE,+BAAA;E5BohGL;A4BhkGD;EAkDI,iCAAA;E5BihGH;A4B9/FA;EAVC;IAjHA,YAAA;IACA,UAAA;I5B6nGC;E4B7gGD;IAvGA,SAAA;IACA,aAAA;I5BunGC;EACF;A8B5uGD;EACE,oBAAA;EACA,uBAAA;EACA,qBAAA;EACA,kBAAA;EACA,aAAA;E9B8uGD;A8B5uGC;EACE,aAAA;E9B8uGH;A8B/uGC;EAII,aAAA;E9B8uGL;A8B3uGC;;EACE,cAAA;EACA,YAAA;EACA,cAAA;E9B8uGH;A8BzuGD;EACE,aAAA;EACA,uBAAA;EACA,oBAAA;EACA,cAAA;EACA,iBAAA;EACA,qBAAA;EACA,kBAAA;EACA,oBAAA;EACA,8BAAA;EACA,4FAAA;UAAA,oFAAA;E9B2uGD;A8BzuGC;;EAEE,eAAA;E9B2uGH;A8BzuGC;EACE,eAAA;EACA,kBAAA;E9B2uGH;A8BzuGC;EpB7CA,cAAA;EAGA,2BAAA;EVuxGD;A8BvuGD;EACE,kBAAA;EACA,kBAAA;E9ByuGD;A8BruGD;EACE,uBAAA;EACA,uBAAA;EACA,mCAAA;EACA,qBAAA;EACA,2BAAA;EACA,oBAAA;EACA,aAAA;EACA,UAAA;EACA,iCAAA;MAAA,6BAAA;UAAA,yBAAA;E9BuuGD;A8BhvGD;EAYI,eAAA;E9BuuGH;A8BpuGC;EACE,uBAAA;EACA,yBAAA;EACA,wBAAA;E9BsuGH;A8BjuGD;EC9EI,gBAAA;EACA,2BAAA;E/BkzGH;A+BhzGG;;;;EAIE,gBAAA;EACA,2BAAA;EACA,uBAAA;E/BkzGL;A+BhzGG;EACE,qBAAA;EACA,uBAAA;E/BkzGL;A+B/yGK;;;;EAIE,2BAAA;EACA,uBAAA;E/BizGP;A8BxvGD;ECpDM,2BAAA;E/B+yGL;A8BxvGD;ECjFI,gBAAA;EACA,2BAAA;E/B40GH;A+B10GG;;;;EAIE,gBAAA;EACA,2BAAA;EACA,uBAAA;E/B40GL;A+B10GG;EACE,qBAAA;EACA,uBAAA;E/B40GL;A+Bz0GK;;;;EAIE,2BAAA;EACA,uBAAA;E/B20GP;A8B/wGD;ECvDM,2BAAA;E/By0GL;A8B/wGD;ECpFI,gBAAA;EACA,2BAAA;E/Bs2GH;A+Bp2GG;;;;EAIE,gBAAA;EACA,2BAAA;EACA,uBAAA;E/Bs2GL;A+Bp2GG;EACE,qBAAA;EACA,uBAAA;E/Bs2GL;A+Bn2GK;;;;EAIE,2BAAA;EACA,uBAAA;E/Bq2GP;A8BtyGD;EC1DM,2BAAA;E/Bm2GL;A8BtyGD;ECvFI,gBAAA;EACA,2BAAA;E/Bg4GH;A+B93GG;;;;EAIE,gBAAA;EACA,2BAAA;EACA,uBAAA;E/Bg4GL;A+B93GG;EACE,qBAAA;EACA,uBAAA;E/Bg4GL;A+B73GK;;;;EAIE,2BAAA;EACA,uBAAA;E/B+3GP;A8B7zGD;EC7DM,2BAAA;E/B63GL;A8B7zGD;EC1FI,gBAAA;EACA,2BAAA;E/B05GH;A+Bx5GG;;;;EAIE,gBAAA;EACA,2BAAA;EACA,uBAAA;E/B05GL;A+Bx5GG;EACE,qBAAA;EACA,uBAAA;E/B05GL;A+Bv5GK;;;;EAIE,2BAAA;EACA,uBAAA;E/By5GP;A8Bp1GD;EChEM,2BAAA;E/Bu5GL;A8Bp1GD;EC7FI,gBAAA;EACA,2BAAA;E/Bo7GH;A+Bl7GG;;;;EAIE,gBAAA;EACA,2BAAA;EACA,uBAAA;E/Bo7GL;A+Bl7GG;EACE,qBAAA;EACA,uBAAA;E/Bo7GL;A+Bj7GK;;;;EAIE,2BAAA;EACA,uBAAA;E/Bm7GP;A8B32GD;ECnEM,2BAAA;E/Bi7GL;A8B32GD;EChGI,gBAAA;EACA,2BAAA;E/B88GH;A+B58GG;;;;EAIE,gBAAA;EACA,2BAAA;EACA,uBAAA;E/B88GL;A+B58GG;EACE,qBAAA;EACA,uBAAA;E/B88GL;A+B38GK;;;;EAIE,2BAAA;EACA,uBAAA;E/B68GP;A8Bl4GD;ECtEM,2BAAA;E/B28GL;A8B/3GC;EnB/DA,oBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EoBTA,qBAAA;EACA,kBAAA;E/B28GD;A8Bv4GC;EAKM,YAAA;EACA,aAAA;EACA,WAAA;E9Bq4GP;A8B54GC;EAUM,aAAA;E9Bq4GP;A8B/4GC;EAaM,UAAA;E9Bq4GP;A8B/3GC;EnBlFA,oBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EoBTA,qBAAA;EACA,kBAAA;E/B89GD;A8Bv4GC;EAKM,YAAA;EACA,aAAA;E9Bq4GP;A8B/3GC;EnB9FA,mBAAA;EACA,iBAAA;EACA,oBAAA;EACA,oBAAA;EoBTA,qBAAA;EACA,kBAAA;E/B0+GD;A8Bv4GC;EAKM,YAAA;EACA,aAAA;E9Bq4GP;A8B34GC;EASM,aAAA;E9Bq4GP;A8Bz3GD;EACE,oBAAA;EACA,uBAAA;EACA,qBAAA;EACA,kBAAA;EAEA,2BAAA;EACA,oBAAA;EACA,kBAAA;EACA,cAAA;EACA,aAAA;EACA,iBAAA;E9B03GD;A8Bx3GC;EACE,aAAA;E9B03GH;A8B33GC;EAII,aAAA;E9B03GL;A8Bv3GC;;EACE,cAAA;EACA,0BAAA;EACA,2BAAA;E9B03GH;A8Br3GD;EACE,WAAA;EACA,YAAA;EACA,oBAAA;EACA,cAAA;EACA,kBAAA;EACA,kBAAA;E9Bu3GD;A8B73GD;EAUI,aAAA;EACA,kBAAA;E9Bs3GH;A8Bj3GD;EACE,oBAAA;EACA,gBAAA;EACA,iBAAA;EACA,iBAAA;EACA,uBAAA;EACA,oBAAA;EACA,wBAAA;EACA,kBAAA;EACA,qBAAA;EACA,mBAAA;EACA,cAAA;EACA,mBAAA;EACA,iCAAA;UAAA,yBAAA;E9Bm3GD;A8Bj3GC;EACE,qBAAA;EACA,oBAAA;EACA,gBAAA;E9Bm3GH;A8Bt3GC;EAMI,YAAA;EACA,sBAAA;UAAA,cAAA;EACA,gBAAA;E9Bm3GL;A8B14GD;EA6BI,gBAAA;EACA,iBAAA;EACA,iBAAA;EACA,oBAAA;EACA,UAAA;EACA,mBAAA;EACA,uBAAA;EACA,QAAA;EACA,aAAA;EACA,WAAA;EACA,qBAAA;EACA,YAAA;EpBhPF,YAAA;EAGA,0BAAA;EoB+OE,yCAAA;UAAA,iCAAA;E9Bi3GH;A8B/2GG;EACE,kBAAA;EACA,8BAAA;EACA,mBAAA;EACA,qCAAA;EACA,oCAAA;E9Bi3GL;A8B32GD;EAEI,gBAAA;EACA,iBAAA;EACA,cAAA;EACA,kBAAA;EACA,eAAA;EACA,+BAAA;EACA,YAAA;EACA,WAAA;EACA,aAAA;EACA,oBAAA;EACA,iBAAA;EACA,qBAAA;EACA,cAAA;E9B42GH;A8B12GC;EACE,cAAA;EACA,mBAAA;E9B42GH;A+BrlHC;EACI,uBAAA;E/BulHL;A+BxlHC;EAIM,2BAAA;E/BulHP;A+BrlHO;EACE,2BAAA;E/BulHT;A+B9lHC;EACI,uBAAA;E/BgmHL;A+BjmHC;EAIM,2BAAA;E/BgmHP;A+B9lHO;EACE,2BAAA;E/BgmHT;A+BvmHC;EACI,uBAAA;E/BymHL;A+B1mHC;EAIM,2BAAA;E/BymHP;A+BvmHO;EACE,2BAAA;E/BymHT;A+BhnHC;EACI,uBAAA;E/BknHL;A+BnnHC;EAIM,2BAAA;E/BknHP;A+BhnHO;EACE,2BAAA;E/BknHT;A+BznHC;EACI,uBAAA;E/B2nHL;A+B5nHC;EAIM,2BAAA;E/B2nHP;A+BznHO;EACE,2BAAA;E/B2nHT;A+BloHC;EACI,uBAAA;E/BooHL;A+BroHC;EAIM,2BAAA;E/BooHP;A+BloHO;EACE,2BAAA;E/BooHT;A+B3oHC;EACI,uBAAA;E/B6oHL;A+B9oHC;EAIM,2BAAA;E/B6oHP;A+B3oHO;EACE,2BAAA;E/B6oHT;A8B14GD;EAEE,kBAAA;EACA,iBAAA;EACA,qBAAA;EACA,YAAA;EACA,sBAAA;UAAA,cAAA;EACA,oBAAA;EACA,iBAAA;EACA,oBAAA;EACA,eAAA;EACA,WAAA;EACA,0BAAA;UAAA,kBAAA;E9B24GD;A8Bz4GC;EACE,kBAAA;E9B24GH;A8Bz4GC;EACE,aAAA;E9B24GH;A8Bz4GC;EACE,gBAAA;E9B24GH;A8B54GC;EAKM,kBAAA;E9B04GP;A8Bn6GD;EAgCI,YAAA;EACA,WAAA;EACA,kBAAA;E9Bs4GH;A8Bx6GD;EZlTE,8BAAA;EACC,6BAAA;ElB6tHF;A8B56GD;EZ1SE,iCAAA;EACC,gCAAA;ElBytHF;A8Bh7GD;EA4CI,YAAA;EACA,WAAA;EACA,kBAAA;E9Bu4GH;A8Br7GD;EZ1SE,iCAAA;EACC,gCAAA;ElBkuHF;A8Bz7GD;EAsDI,mBAAA;E9Bs4GH;A8B57GD;EA0DI,oBAAA;EACA,mBAAA;EACA,2BAAA;KAAA,wBAAA;MAAA,uBAAA;UAAA,mBAAA;EACA,yDAAA;UAAA,iDAAA;E9Bq4GH;A8Bl8GD;EAkEM,+BAAA;EACA,iBAAA;E9Bm4GL;A8Bj4GK;;;EAGE,2BAAA;EACA,gBAAA;EACA,eAAA;E9Bm4GP;A8B78GD;EAgFI,iBAAA;EACA,+BAAA;EpBnYF,cAAA;EAGA,2BAAA;EVkwHD;A8B/3GG;;;EAGE,6BAAA;E9Bi4GL;A8Bx9GD;EA6FM,qBAAA;EACA,gBAAA;E9B83GL;A8B59GD;EAqGM,iBAAA;EACA,2BAAA;EACA,8BAAA;EACA,iBAAA;E9B03GL;A8Bl+GD;EA4GQ,kBAAA;E9By3GP;A8Bl3GD;EACE,mBAAA;EACA,oBAAA;EACA,oBAAA;EACA,kBAAA;EACA,+CAAA;E9Bo3GD;A8Bh3GD;EACE,kBAAA;EACA,aAAA;EACA,eAAA;E9Bk3GD;A8Br3GD;EAOI,aAAA;EACA,yBAAA;E9Bi3GH;A8B52GD;EACE,2BAAA;EACA,kCAAA;E9B82GD;A8Bh3GD;EAOM,gBAAA;E9B42GL;A8B12GK;;;EAGE,qBAAA;E9B42GP;A8Bz2GG;EACE,gBAAA;E9B22GL;A8B33GD;EAwBM,iCAAA;E9Bs2GL;A8Bp2GK;EACE,gBAAA;EACA,6BAAA;E9Bs2GP;A8B/1GD;EACE,oBAAA;E9Bi2GD;A8Bl2GD;EAII,gBAAA;E9Bi2GH;A8Br2GD;EAOI,kBAAA;E9Bi2GH;A8Bx2GD;EAUI,oBAAA;E9Bi2GH;A8B32GD;EAaI,eAAA;E9Bi2GH;A8Bx1GD;;EAEE,gCAAA;EACA,uBAAA;EACA,wBAAA;EACA,sBAAA;EACA,sBAAA;EACA,uBAAA;EACA,6BAAA;EACA,+BAAA;EACA,uBAAA;EACA,oBAAA;EACA,mBAAA;E9B01GD;A8Bv1GD;EACE,WAAA;EACA,qBAAA;EACA,aAAA;EACA,cAAA;EACA,kBAAA;EACA,YAAA;EACA,oBAAA;EACA,YAAA;E9By1GD;A8Bt1GD;;EAEE,gCAAA;EACA,uBAAA;EACA,wBAAA;EACA,sBAAA;EACA,sBAAA;EACA,uBAAA;EACA,6BAAA;EACA,+BAAA;EACA,uBAAA;EACA,oBAAA;EACA,mBAAA;E9Bw1GD;A8Br1GD;EACE,eAAA;E9Bu1GD;A8Bp1GD;EACE,oBAAA;EACA,eAAA;EACA,gBAAA;EACA,cAAA;EACA,eAAA;EACA,kBAAA;E9Bs1GD;A8Bn1GD;EACE,WAAA;EACA,WAAA;EACA,YAAA;EACA,iBAAA;EACA,SAAA;EACA,QAAA;EACA,kBAAA;EACA,iBAAA;EACA,cAAA;EACA,aAAA;EACA,eAAA;E9Bq1GA,qCAAoC;E8Bn1GpC,wBAAA;EpBvjBA,YAAA;EAGA,0BAAA;EV24HD;AgC74HD;EACE,2BAAA;EACA,oBAAA;EACA,eAAA;EACA,qBAAA;EACA,oBAAA;EACA,oBAAA;EhC+4HD;AgCr5HD;EASI,gBAAA;EACA,oBAAA;EACA,aAAA;EACA,WAAA;EACA,aAAA;EhC+4HH;AgC55HD;EAgBI,iBAAA;EACA,qBAAA;EhC+4HH;AgC54HD;EACE,eAAA;EACA,qBAAA;EACA,wBAAA;EhC84HD;AgC54HC;EACE,eAAA;EACA,kBAAA;EACA,cAAA;EhC84HH;AgC34HD;EACE,iBAAA;EACA,WAAA;EhC64HD;AiCz6HD;EACE,iBAAA;EACA,kBAAA;EACA,qBAAA;EACA,cAAA;EACA,oBAAA;EjC26HD;AiC/5HD;EAAA;IAFI,aAAA;IjCq6HD;EACF;AiCx5HD;EACE,kBAAA;EACA,qBAAA;EACA,oBAAA;EjC05HD;AiC75HD;EAMI,cAAA;EjC05HH;AiCj4HC;EAAA;IApBI,oBAAA;IjCy5HH;EiCr4HD;IftDA,gCAAA;IACG,6BAAA;IlB87HF;EiCz4HD;IAbI,qBAAA;IjCy5HH;EiC54HD;IAVM,4BAAA;IjCy5HL;EiCr5HG;;IAEE,iBAAA;IjCu5HL;EACF;AiC/4HD;EAAA;IAHM,oBAAA;IjCs5HH;EACF;AiC94HD;;EAGI,oBAAA;EACA,qBAAA;EjC+4HH;AiCn5HD;;;;EAQM,qBAAA;EACA,oBAAA;EjCi5HL;AiC34HG;EAAA;;;;IAHI,iBAAA;IACA,gBAAA;IjCq5HL;EACF;AiCz4HD;EACE,eAAA;EACA,iBAAA;EACA,kBAAA;EjC24HD;AiCv4HD;;EAEE,eAAA;EACA,kBAAA;EjCy4HD;AiCv4HD;EACE,iBAAA;EjCy4HD;AiCv4HD;EACE,kBAAA;EACA,iBAAA;EjCy4HD;AiCp4HD;EACE,iBAAA;EACA,oBAAA;EACA,cAAA;EACA,kBAAA;EACA,oBAAA;EjCs4HD;AiC34HD;EAQI,iBAAA;EACA,oBAAA;EACA,qBAAA;EjCs4HH;AiC73HD;EALI;;IAEE,oBAAA;IjCq4HH;EACF;AiC53HD;EACE,cAAA;EACA,gBAAA;EACA,oBAAA;EACA,iBAAA;EACA,cAAA;EACA,mBAAA;EjC83HD;AiC53HC;EACE,gBAAA;EACA,kBAAA;EACA,8BAAA;EACA,iBAAA;EACA,oBAAA;EACA,qBAAA;EACA,qCAAA;EACA,oCAAA;EACA,uCAAA;UAAA,+BAAA;EjC83HH;AiC53HC;;EAEE,eAAA;EjC83HH;AiC53HG;;EACE,gBAAA;EjC+3HL;AiCv5HD;EA4BI,eAAA;EjC83HH;AiCx3HD;EAAA;IAFI,eAAA;IjC83HD;EACF;AiCt3HD;EACE,WAAA;EjCw3HD;AiCz3HD;EAII,iBAAA;EACA,oBAAA;EACA,mBAAA;EACA,kBAAA;EjCw3HH;AiC/3HD;;;;EAaI,+BAAA;EjCw3HH;AiCr4HD;EAiBI,mBAAA;EACA,oBAAA;EACA,UAAA;EjCu3HH;AiC14HD;;EAwBM,mBAAA;EjCs3HL;AiCh2HD;EAAA;IAjBI,iBAAA;IjCq3HD;EiCp2HH;;IAXQ,uCAAA;IjCm3HL;EiCx2HH;IARQ,mBAAA;IjCm3HL;EiC32HH;IAJM,kBAAA;IACA,qBAAA;IjCk3HH;EACF;AiCz2HD;ElBrLE,cAAA;EACA,mBAAA;EACA,iBAAA;EACA,kBAAA;EACA,oBAAA;EfiiID;Ae/hIC;EACE,cAAA;EACA,mBAAA;EfiiIH;Ae9hIC;;EAEE,cAAA;EfgiIH;AiCp3HD;EACE,kBAAA;EACA,eAAA;EACA,kBAAA;EACA,qBAAA;EACA,oBAAA;EC3PA,kBAAA;EACA,qBAAA;ElCknID;AiC92HC;EAAA;IAJE,mBAAA;IACA,aAAA;IjCs3HD;EACF;AiCn4HD;;;;ElBzLE,cAAA;EACA,mBAAA;EACA,iBAAA;EACA,kBAAA;EACA,oBAAA;EfkkID;AehkIC;;;;EACE,cAAA;EACA,mBAAA;EfqkIH;AelkIC;;;;;;;;EAEE,cAAA;Ef0kIH;AiC95HD;EAqBI,WAAA;EjC44HH;AiCj6HD;;;;;;EfrPE,+BAAA;EACG,4BAAA;ElB8pIJ;AiC16HD;;;;;;Ef7OE,8BAAA;EACG,2BAAA;ElB+pIJ;AiCn7HD;;EA4BI,iBAAA;EACA,oBAAA;EACA,qBAAA;EjC25HH;AiCz7HD;EAiCI,iBAAA;EACA,oBAAA;EACA,kBAAA;EjC25HH;AiC97HD;EAsCI,mBAAA;EjC25HH;AiCl5HC;EAAA;IAJI,cAAA;IjC05HH;EACF;AiC34HD;EAAA;IAVM,oBAAA;IjCy5HH;EiCv5HG;IACE,kBAAA;IjCy5HL;EiCl5HH;IAHM,gBAAA;IjCw5HH;EACF;AiCj5HD;EAEI,iBAAA;EACA,iBAAA;EACA,oBAAA;EjCk5HH;AiC54HC;EAAA;IAFI,0BAAA;IjCk5HH;EACF;AiC74HD;Ef/TE,iCAAA;EACC,gCAAA;ElB+sIF;AiC74HD;;;EAGE,+BAAA;EjC+4HD;AiCx4HD;EACE,iBAAA;EACA,oBAAA;EACA,gBAAA;EACA,eAAA;EACA,kBAAA;ECvVA,mBAAA;EACA,sBAAA;ElCkuID;AiC/3HD;EAAA;IARI,mBAAA;IACA,oBAAA;IjC24HD;EiCx4HC;IACE,iBAAA;IjC04HH;EACF;AiCl4HD;EACE,iBAAA;EACA,oBAAA;EjCo4HD;AiCl4HC;EACE,iBAAA;EACA,oBAAA;EjCo4HH;AiCl4HC;EACE,kBAAA;EACA,qBAAA;EjCo4HH;AiC93HD;;EAEE,mDAAA;EACA,2BAAA;EACA,oBAAA;EACA,gBAAA;EACA,cAAA;EACA,kBAAA;EACA,aAAA;EACA,gBAAA;EACA,oBAAA;EACA,aAAA;EACA,oBAAA;EACA,UAAA;EACA,YAAA;EACA,aAAA;EjCg4HD;AiCx3HC;EAAA;;IALE,kBAAA;IACA,cAAA;IACA,oBAAA;IjCk4HD;EACF;AiCh4HC;;EACE,2BAAA;EACA,eAAA;EjCm4HH;AiC/3HD;EACE,2BAAA;EACA,iBAAA;EACA,cAAA;EACA,mBAAA;EACA,oBAAA;EACA,iBAAA;EACA,gBAAA;EACA,aAAA;EACA,8CAAA;EjCi4HD;AiC13HD;EACE,2BAAA;EjC43HD;AiC73HD;EAII,gBAAA;EjC43HH;AiC33HG;;EAEE,gBAAA;EACA,+BAAA;EjC63HL;AiCx3HG;EACE,gBAAA;EjC03HL;AiCx3HG;;EAEE,+BAAA;EjC03HL;AiCx3HK;;EACE,gBAAA;EjC23HP;AiCh5HD;;EA4BI,uBAAA;EACA,mBAAA;EjCw3HH;AiCr5HD;EAkCM,gBAAA;EjCs3HL;AiCp3HK;;EAEE,gBAAA;EACA,+BAAA;EjCs3HP;AiCl3HK;;;EAGE,gBAAA;EACA,+BAAA;EjCo3HP;AiCh3HK;;;EAGE,gBAAA;EACA,+BAAA;EjCk3HP;AiCz6HD;EAgEM,2BAAA;EACA,8BAAA;EjC42HL;AiC76HD;EAqEM,2BAAA;EACA,8BAAA;EjC22HL;AiCj7HD;;EA2EM,2BAAA;EACA,8BAAA;EjC02HL;AiCr2HK;;;EAGE,+BAAA;EACA,gBAAA;EjCu2HP;AiC32HK;;;EAMI,2BAAA;EACA,8BAAA;EjC02HT;AiCv0HC;EAAA;IAzBQ,gBAAA;IjCo2HP;EiCn2HO;;IAEE,gBAAA;IACA,+BAAA;IjCq2HT;EiCj2HO;;;IAGE,gBAAA;IACA,+BAAA;IjCm2HT;EiC/1HO;;;IAGE,gBAAA;IACA,+BAAA;IjCi2HT;EACF;AiCx9HD;;EA+HM,2BAAA;EjC61HL;AcryIC;;EAAgC,gBAAA;EACA,YAAA;EdyyIjC;AcxyIC;;EAAgC,gBAAA;Ed4yIjC;Ac3yIC;;EAAgC,gBAAA;Ed+yIjC;AiCv2HK;;EACE,uBAAA;EACC,gBAAA;EjC02HR;AiC9+HD;EAwIM,2BAAA;EACA,gBAAA;EjCy2HL;AiCl/HD;;;EA8IS,uBAAA;EACA,gBAAA;EjCy2HR;AiCx/HD;EAqJI,gBAAA;EjCs2HH;AiC3/HD;EA6JI,gBAAA;EjCi2HH;AiCh2HG;EACE,gBAAA;EjCk2HL;AiCjgID;EAoKI,gBAAA;EjCg2HH;AiC/1HG;;EAEE,gBAAA;EjCi2HL;AiC71HK;;;;EAEE,gBAAA;EjCi2HP;AiC11HD;EACE,2BAAA;EjC41HD;AiC71HD;EAII,gBAAA;EjC41HH;AiC31HG;;EAEE,gBAAA;EACA,+BAAA;EjC61HL;AiCx1HG;EACE,gBAAA;EjC01HL;AiCx1HG;;EAEE,+BAAA;EjC01HL;AiCx1HK;;EACE,gBAAA;EjC21HP;AiCh3HD;EA2BI,uBAAA;EACA,mBAAA;EjCw1HH;AiCp3HD;EAiCM,gBAAA;EjCs1HL;AiCp1HK;;EAEE,gBAAA;EACA,+BAAA;EjCs1HP;AiCl1HK;;;EAGE,gBAAA;EACA,2BAAA;EjCo1HP;AiCh1HK;;;EAGE,gBAAA;EACA,+BAAA;EjCk1HP;AiCx4HD;;EAgEM,2BAAA;EACA,8BAAA;EjC40HL;AiCv0HK;;;EAGE,2BAAA;EACA,gBAAA;EACA,gCAAA;EjCy0HP;AiC90HK;;;EAOI,2BAAA;EACA,8BAAA;EjC40HT;AiC15HD;EAmFM,2BAAA;EACA,8BAAA;EjC00HL;AiC95HD;EAyFQ,2BAAA;EACA,kBAAA;EjCw0HP;AiCl6HD;EA6FU,gBAAA;EACA,oBAAA;EACA,kBAAA;EjCw0HT;AiCt0HS;;EAEE,gBAAA;EACA,2BAAA;EjCw0HX;AiC56HD;EAwGU,2BAAA;EACA,aAAA;EACA,mBAAA;EACA,oBAAA;EjCu0HT;AiC9xHC;EAAA;IAlCO,sBAAA;IjCo0HN;EiClyHD;IA7BQ,gBAAA;IjCk0HP;EiCj0HO;;IAEE,gBAAA;IACA,+BAAA;IjCm0HT;EiC/zHO;;;IAGE,gBAAA;IACA,2BAAA;IjCi0HT;EiC7zHO;;;IAGE,gBAAA;IACA,+BAAA;IjC+zHT;EiCtzHD;IAHM,2BAAA;IjC4zHL;EACF;AiC98HD;;EAwJM,gBAAA;EACA,2BAAA;EACA,2BAAA;EjC0zHL;Acj9IC;;EAAgC,gBAAA;EACA,YAAA;Edq9IjC;Acp9IC;;EAAgC,gBAAA;Edw9IjC;Acv9IC;;EAAgC,gBAAA;Ed29IjC;AiCp0HK;;EACE,uBAAA;EACC,gBAAA;EjCu0HR;AiCt+HD;EtB9lBE,gBAAA;EACA,2BAAA;EXukJD;AWrkJC;;;;;;EAME,gBAAA;EACA,2BAAA;EACA,uBAAA;EXukJH;AWrkJC;;;EAGE,qBAAA;EACA,uBAAA;EXukJH;AWlkJG;;;;;;;;;;;;;;;;;;EAME,2BAAA;EACA,uBAAA;EXglJL;AiC/gID;EtB5jBI,gBAAA;EACA,2BAAA;EX8kJH;AiCnhID;EAsKM,2BAAA;EACA,2BAAA;EACA,gBAAA;EjCg3HL;AiCxhID;;;EA6KS,uBAAA;EACA,gBAAA;EjCg3HR;AiCx2HC;EAAA;IAHI,uBAAA;IACA,qBAAA;IjC+2HH;EACF;AiCpiID;EAyLI,gBAAA;EjC82HH;AiCviID;EA4LM,gBAAA;EjC82HL;AiC52HK;;EAEE,gBAAA;EjC82HP;AiC9iID;EtB9lBE,gBAAA;EACA,2BAAA;EX+oJD;AW7oJC;;;;;;EAME,gBAAA;EACA,2BAAA;EACA,uBAAA;EX+oJH;AW7oJC;;;EAGE,qBAAA;EACA,uBAAA;EX+oJH;AW1oJG;;;;;;;;;;;;;;;;;;EAME,2BAAA;EACA,uBAAA;EXwpJL;AiCvlID;EtB5jBI,gBAAA;EACA,2BAAA;EXspJH;AiCn4HD;EAAA;IAVM,oBAAA;IACA,gDAAA;IjCi5HH;EiC/4HC;;IAGI,gDAAA;IjCg5HL;EACF;AiC14HD;EACE,kBAAA;EjC44HD;AiC74HD;EAII,gBAAA;EACA,cAAA;EACA,mBAAA;EACA,sBAAA;EjC44HH;AiCn5HD;EAUM,iBAAA;EACA,gBAAA;EjC44HL;AiCv5HD;EAiBM,iBAAA;EACA,kBAAA;EjCy4HL;AiCn4HG;EAAA;IAHI,mBAAA;IACA,sBAAA;IjC04HL;EACF;AiCj6HD;EA4BI,cAAA;EACA,mBAAA;EjCw4HH;AiCr6HD;ECtzBE,qBAAA;EACA,wBAAA;ElC8tJD;AiCz6HD;ECtzBE,qBAAA;EACA,wBAAA;ElCkuJD;AiC76HD;EAyCI,oBAAA;EACA,uBAAA;EjCu4HH;AiCr4HG;EACE,oBAAA;EACA,uBAAA;EjCu4HL;AiCr4HG;EACE,oBAAA;EACA,uBAAA;EjCu4HL;AmC9uJD;EACE,iBAAA;EACA,mBAAA;EACA,uBAAA;EACA,iBAAA;EACA,qBAAA;EACA,oBAAA;EACA,kBAAA;EACA,kBAAA;EACA,wBAAA;EACA,aAAA;EACA,cAAA;EACA,y0BAAA;EACA,2BAAA;KAAA,wBAAA;MAAA,uBAAA;UAAA,mBAAA;EnCgvJD;AmC7vJD;EAiBI,uBAAA;EACA,cAAA;EACA,qBAAA;EACA,yCAAA;UAAA,iCAAA;EnC+uJH;AmCnwJD;EAwBM,kBAAA;EACA,mBAAA;EACA,iBAAA;EACA,uBAAA;EACA,cAAA;EACA,qBAAA;EACA,kBAAA;EACA,oBAAA;EACA,YAAA;EACA,aAAA;EACA,+CAAA;UAAA,uCAAA;EnC8uJL;AmChxJD;EAqCQ,gBAAA;EnC8uJP;AmCnxJD;EAyCM,iBAAA;EACA,gBAAA;EACA,oBAAA;EACA,aAAA;EACA,cAAA;EACA,sBAAA;EACA,cAAA;EACA,QAAA;EACA,SAAA;EACA,WAAA;EACA,cAAA;EzBvDJ,YAAA;EAGA,0BAAA;EVmyJD;AmClyJD;;EA0DI,+BAAA;EACA,WAAA;EACA,QAAA;EACA,SAAA;EACA,aAAA;EzBlEF,YAAA;EAGA,0BAAA;EV6yJD;AmCxuJD;EjB5DE,iCAAA;EACG,8BAAA;ElBuyJJ;AoChyJC;EACE,sDAAA;EpCkyJH;AmC5uJC;EClDE,uBAAA;EACA,2BAAA;EpCiyJH;AoCvyJC;EACE,sDAAA;EpCyyJH;AmCnvJC;EClDE,uBAAA;EACA,2BAAA;EpCwyJH;AoC9yJC;EACE,sDAAA;EpCgzJH;AmC1vJC;EClDE,uBAAA;EACA,2BAAA;EpC+yJH;AoCrzJC;EACE,sDAAA;EpCuzJH;AmCjwJC;EClDE,uBAAA;EACA,2BAAA;EpCszJH;AoC5zJC;EACE,sDAAA;EpC8zJH;AmCxwJC;EClDE,uBAAA;EACA,2BAAA;EpC6zJH;AoCn0JC;EACE,sDAAA;EpCq0JH;AmC/wJC;EClDE,uBAAA;EACA,2BAAA;EpCo0JH;AmCxwJD;EjBlFE,kCAAA;EACG,+BAAA;ElB61JJ;AmCzwJC;EACE,uBAAA;EACA,cAAA;EACA,+BAAA;EACA,oBAAA;EACA,oBAAA;EACA,qBAAA;EACA,YAAA;EACA,cAAA;EACA,aAAA;EACA,oBAAA;EACA,QAAA;EACA,YAAA;EACA,cAAA;EACA,8BAAA;EACA,kFAAA;UAAA,0EAAA;EnC2wJH;AmCtwJD;EAEI,gDAAA;UAAA,wCAAA;EnCuwJH;AmCpwJD;EACE,gBAAA;EnCswJD;AmCnwJD;EACE,oBAAA;EnCqwJD;AmCjwJD;;EzB3HE,cAAA;EAGA,2BAAA;EyB2HA,iBAAA;EnCowJD;AmCvwJD;;;;EAOI,4BAAA;EnCswJH;AmCjwJD;EACE,YAAA;EnCmwJD;AmC5vJD;EC/IE,gBAAA;EACA,2BAAA;EpC84JD;AmChwJD;EC1II,2BAAA;EACA,uBAAA;EpC64JH;AoCz4JC;EAEI,oCAAA;EpC04JL;AmCnwJD;ECnJE,gBAAA;EACA,2BAAA;EpCy5JD;AmCvwJD;EC9II,2BAAA;EACA,uBAAA;EpCw5JH;AoCp5JC;EAEI,oCAAA;EpCq5JL;AmC1wJD;ECvJE,gBAAA;EACA,2BAAA;EpCo6JD;AmC9wJD;EClJI,2BAAA;EACA,uBAAA;EpCm6JH;AoC/5JC;EAEI,oCAAA;EpCg6JL;AmCjxJD;EC3JE,gBAAA;EACA,2BAAA;EpC+6JD;AmCrxJD;ECtJI,2BAAA;EACA,uBAAA;EpC86JH;AoC16JC;EAEI,oCAAA;EpC26JL;AmCxxJD;EC/JE,gBAAA;EACA,2BAAA;EpC07JD;AmC5xJD;EC1JI,2BAAA;EACA,uBAAA;EpCy7JH;AoCr7JC;EAEI,oCAAA;EpCs7JL;AmC/xJD;ECnKE,gBAAA;EACA,2BAAA;EpCq8JD;AmCnyJD;EC9JI,2BAAA;EACA,uBAAA;EpCo8JH;AoCh8JC;EAEI,oCAAA;EpCi8JL;AmClyJD;EAEI,6xBAAA;EACA,oBAAA;EnCmyJH;AmCtyJD;EAMM,oBAAA;EnCmyJL;AmCzyJD;EASM,oBAAA;EjBxKJ,gCAAA;EACG,6BAAA;ElB48JJ;AmC9yJD;EAaM,mBAAA;EjBpLJ,iCAAA;EACG,8BAAA;ElBy9JJ;AmCnyJK;EACG,cAAA;EjBhLP,8BAAA;EACG,2BAAA;EATH,iCAAA;EACG,8BAAA;ElBg+JJ;AmC1zJD;EjB/JE,gCAAA;EACG,6BAAA;EATH,+BAAA;EACG,4BAAA;ElBs+JJ;AqCv+JD;EACE,2BAAA;EACA,oBAAA;EACA,oBAAA;ErCy+JD;AqC5+JD;EAMI,uBAAA;EACA,WAAA;EACA,eAAA;ErCy+JH;AqCj/JD;EAWI,iBAAA;EACA,kBAAA;EACA,mBAAA;ErCy+JH;AsCh/JC;;EAEE,cAAA;EACA,gBAAA;EtCk/JH;AsCh/JC;EACE,aAAA;EtCk/JH;AqC9+JG;EACE,gBAAA;ErCg/JL;AqCjgKD;EAqBI,cAAA;EACA,WAAA;ErC++JH;AqCrgKD;EnBJE,4BAAA;EACC,2BAAA;ElB4gKF;AqC5+JD;EACE,aAAA;EACA,iBAAA;EACA,kBAAA;EACA,kBAAA;EACA,YAAA;ErC8+JD;AuClhKD;EACE,+BAAA;EACA,oBAAA;EACA,sBAAA;EACA,cAAA;EACA,wBAAA;EACA,kBAAA;EACA,qCAAA;UAAA,6BAAA;EACA,wBAAA;EACA,wBAAA;EACA,yBAAA;EvCohKD;AuC9hKD;EAcI,cAAA;EACA,aAAA;EACA,gBAAA;EvCmhKH;AuCjhKC;EACE,oBAAA;EvCmhKH;AuCjhKC;EACE,wBAAA;EACA,yBAAA;EvCmhKH;AuC9gKD;EACE,iBAAA;EACA,kBAAA;EACA,gBAAA;EACA,SAAA;EACA,QAAA;EACA,WAAA;EACA,UAAA;EACA,wBAAA;EACA,yBAAA;EACA,sBAAA;EvCghKD;AuC1hKD;EAaI,eAAA;ErBnCF,+BAAA;EACC,8BAAA;ElBojKF;AuC/hKD;EAiBI,2BAAA;EvCihKH;AuC5gKD;EACE,gBAAA;EACA,YAAA;EACA,iBAAA;EACA,oBAAA;EACA,aAAA;EACA,kBAAA;EACA,wBAAA;EvC8gKD;AuC1gKD;EACE,oBAAA;EACA,cAAA;EACA,gBAAA;EACA,qBAAA;EACA,kBAAA;ErB5DA,iCAAA;EACC,gCAAA;EqB6DD,2BAAA;KAAA,wBAAA;MAAA,uBAAA;UAAA,mBAAA;EvC6gKD;AuC3gKC;EACE,gCAAA;EACA,uBAAA;EvC6gKH;AuCxgKD;EACE,oBAAA;EACA,oBAAA;EACA,aAAA;EACA,WAAA;EACA,YAAA;EACA,mDAAA;EvC0gKD;AuCxgKD;EACE,eAAA;EACA,gBAAA;EACA,kBAAA;EACA,oBAAA;EACA,qBAAA;EACA,sCAAA;EvC0gKD;AuCxgKD;EACE,gBAAA;EvC0gKD;AuCxgKD;EACE,aAAA;EvC0gKD;AuCxgKD;EACE,gBAAA;EvC0gKD;AuCvgKD;EACE,gCAAA;EACA,uBAAA;EACA,iEAAA;UAAA,yDAAA;EvCygKD;AuCvgKD;EACE,+BAAA;EACA,uBAAA;EACA,oEAAA;UAAA,4DAAA;EvCygKD;AuCngKD;EACE,6BAAA;EACA,8BAAA;EACA,oBAAA;EACA,oBAAA;EACA,uBAAA;EACA,cAAA;EACA,aAAA;EACA,wBAAA;EvCqgKD;AuCngKC;EACE,YAAA;EvCqgKH;AuChhKD;EAcI,6BAAA;EACA,8BAAA;EvCqgKH;AuC9/JD;EACE,WAAA;EACA,qBAAA;EACA,aAAA;EACA,cAAA;EACA,kBAAA;EACA,YAAA;EACA,oBAAA;EACA,YAAA;EvCggKD;AuC5/JD;EACE,iBAAA;EACA,cAAA;EACA,aAAA;EvC8/JD;AuCjgKD;EAMI,oBAAA;EACA,cAAA;EvC8/JH;AuC5/JG;;EAEE,oBAAA;EACA,8BAAA;EACA,gBAAA;EACA,iBAAA;EACA,UAAA;EACA,WAAA;EACA,2BAAA;EACA,qCAAA;EACA,8CAAA;UAAA,sCAAA;EvC8/JL;AuC5/JG;EACE,kBAAA;EvC8/JL;AuC5/JG;EACE,kBAAA;EvC8/JL;AuCz/JG;EAEI,gBAAA;EvC0/JP;AuCt/JK;E7B7LJ,YAAA;EAGA,0BAAA;EVorKD;AuCv/JK;EACE,YAAA;EACA,sBAAA;UAAA,cAAA;EvCy/JP;AuCp/JG;EAEI,gBAAA;EvCq/JP;AuCj/JK;EACE,YAAA;EACA,sBAAA;UAAA,cAAA;EvCm/JP;AuCj/JK;E7BjNJ,YAAA;EAGA,0BAAA;EVmsKD;AuC7+JD;EACE,YAAA;EACA,4BAAA;EvC++JD;AuCj/JD;EAKI,aAAA;EACA,cAAA;EACA,8BAAA;EACA,qBAAA;EvC++JH;AuC1+JD;EACE,cAAA;EACA,gBAAA;EACA,4BAAA;EvC4+JD;AuC1+JC;;EAGI,gBAAA;EvC2+JL;AuCn/JD;EAaI,cAAA;EACA,gBAAA;EvCy+JH;AuCv+JG;;EAEE,8BAAA;EACA,iBAAA;EACA,mBAAA;EACA,oBAAA;EACA,WAAA;EACA,sBAAA;EACA,qCAAA;EACA,oCAAA;EACA,8CAAA;UAAA,sCAAA;EvCy+JL;AuCv+JG;EACE,kBAAA;EvCy+JL;AuCv+JG;EACE,kBAAA;E7BtQJ,YAAA;EAGA,0BAAA;EV8uKD;AuCn+JK;E7B9QJ,YAAA;EAGA,0BAAA;EVkvKD;AuCp+JK;EACE,YAAA;EACA,sBAAA;UAAA,cAAA;EvCs+JP;AuC/9JD;;;;EAIE,eAAA;EvCi+JD;AuC79JD;EACE,cAAA;EACA,oBAAA;EACA,YAAA;EACA,cAAA;EACA,aAAA;EACA,WAAA;EACA,qBAAA;EACA,qBAAA;EvC+9JD;AuC59JD;EACE,oBAAA;EACA,4BAAA;EACA,YAAA;EACA,WAAA;EACA,cAAA;EvC89JD;AuC39JD;;EAEE,gBAAA;EACA,cAAA;EACA,WAAA;EACA,YAAA;EACA,qBAAA;EvC69JD;AuC19JD;EACE,qBAAA;EACA,YAAA;EACA,oBAAA;EACA,QAAA;ErB9TA,+BAAA;EACG,4BAAA;ElB2xKJ;AuC19JD;EACE,qBAAA;EvC49JD;AuC19JC;;EAEE,qBAAA;EvC49JH;AuCx9JD;EACE,2BAAA;EACA,aAAA;EACA,cAAA;EACA,QAAA;EACA,oBAAA;EACA,uBAAA;EACA,oBAAA;EACA,4CAAA;UAAA,oCAAA;EvC09JD;AuCx9JC;EACE,kBAAA;EvC09JH;AuCx9JC;EACE,mBAAA;EvC09JH;AuCx9JC;;EAEE,2BAAA;EvC09JH;AuCx9JC;EACE,2BAAA;EvC09JH;AuCp9JD;EACE,mDAAA;EACA,kBAAA;EACA,iBAAA;EACA,qBAAA;EACA,aAAA;EACA,cAAA;EACA,oBAAA;EvCs9JD;AuCn9JD;EACE,gBAAA;EACA,iBAAA;EACA,oBAAA;EACA,cAAA;EACA,WAAA;EvCq9JD;AuCl9JD;EACE,eAAA;EvCo9JD;AuCj9JD;EACE,cAAA;EACA,WAAA;EvCm9JD;AuCh9JD;EACE,gBAAA;EACA,aAAA;EACA,WAAA;EvCk9JD;AuC98JD;EACE,iBAAA;EACA,cAAA;EACA,mBAAA;EvCg9JD;AuC98JC;;EAGI,gBAAA;EvC+8JL;AuCv9JD;EAYI,cAAA;EACA,gBAAA;EvC88JH;AuC58JG;EACE,8BAAA;EACA,kBAAA;EACA,iBAAA;EACA,mBAAA;EACA,oBAAA;EACA,WAAA;EACA,sBAAA;EACA,qCAAA;EACA,oCAAA;EACA,8CAAA;UAAA,sCAAA;EvC88JL;AuCx8JD;EACE,0BAAA;EvC08JD;AuCp8JD;EACE,oBAAA;EACA,UAAA;EACA,WAAA;EACA,qBAAA;EACA,eAAA;EACA,cAAA;EACA,aAAA;EACA,qBAAA;EACA,uBAAA;EACA,2CAAA;UAAA,mCAAA;EvCs8JD;AuCj7JD;EAjBE;IACE,2BAAA;IACA,qBAAA;IACA,iCAAA;YAAA,yBAAA;IvCq8JD;EuCn8JD;IACE,2BAAA;IACA,kBAAA;IACA,mCAAA;YAAA,2BAAA;IvCq8JD;EuCn8JD;IACE,2BAAA;IACA,qBAAA;IACA,mCAAA;YAAA,2BAAA;IvCq8JD;EACF;AuCt7JD;EA7BE;IACE,2BAAA;IACA,qBAAA;IACA,iCAAA;YAAA,yBAAA;IvCw/JD;EuCt/JD;IACE,2BAAA;IACA,kBAAA;IACA,mCAAA;YAAA,2BAAA;IvCw/JD;EuCt/JD;IACE,2BAAA;IACA,qBAAA;IACA,mCAAA;YAAA,2BAAA;IvCw/JD;EACF;AwCr8KD;EACE,gBAAA;EACA,qBAAA;EACA,oBAAA;ExCu8KD;AwC18KD;EAMI,2BAAA;EACA,WAAA;EACA,YAAA;EACA,uBAAA;EACA,4BAAA;ExCu8KH;AwCj9KD;EAaI,qBAAA;EACA,4BAAA;EACA,iBAAA;EACA,iBAAA;EACA,oBAAA;EACA,iBAAA;EACA,8BAAA;EACA,oBAAA;EACA,0BAAA;UAAA,kBAAA;ExCu8KH;AwCr8KG;EACE,eAAA;ExCu8KL;AwCr8KG;EACE,4BAAA;EACA,sBAAA;ExCu8KL;AwCr8KG;EACE,yBAAA;EACA,gBAAA;ExCu8KL;AwCz8KG;EAKI,gBAAA;ExCu8KP;AwCp8KG;EACE,cAAA;EACA,gBAAA;EACA,aAAA;EACA,cAAA;EACA,oBAAA;EACA,UAAA;EACA,aAAA;EACA,mBAAA;EACA,qBAAA;EACA,oBAAA;ExCs8KL;AwCp8KG;EACE,kBAAA;EACA,8BAAA;EACA,oBAAA;EACA,iBAAA;EACA,mBAAA;EACA,oBAAA;EACA,qBAAA;EACA,sBAAA;EACA,sBAAA;EACA,qCAAA;EACA,oCAAA;EACA,qBAAA;EACA,gBAAA;ExCs8KL;AwCj8KD;EACE,oBAAA;EACA,qBAAA;EACA,4BAAA;EACA,4BAAA;EACA,gBAAA;EACA,yBAAA;ExCm8KD;AwCj8KC;EACE,oBAAA;EACA,8BAAA;EACA,kBAAA;EACA,iBAAA;EACA,mBAAA;EACA,uBAAA;EACA,UAAA;EACA,WAAA;EACA,yBAAA;ExCm8KH;AwC/7KD;EACE,kBAAA;EACA,cAAA;EACA,gBAAA;EACA,iBAAA;EACA,kBAAA;EACA,WAAA;EACA,mBAAA;EACA,gBAAA;EACA,gBAAA;EACA,kBAAA;EAEA,eAAA;ExCg8KD;Acr8KC;EAAgC,gBAAA;EACA,YAAA;Edw8KjC;Acv8KC;EAAgC,gBAAA;Ed08KjC;Acz8KC;EAAgC,gBAAA;Ed48KjC;AwCv8KD;EACE,aAAA;EACA,iBAAA;EACA,wBAAA;ExCy8KD;AwCt8KD;EACE,kBAAA;EACA,kBAAA;ExCw8KD;AwCr8KD;EACE,gBAAA;EACA,iBAAA;EACA,mBAAA;ExCu8KD;AyC7jLD;EACE,cAAA;EACA,aAAA;EACA,uBAAA;EzC+jLD;AyC7jLD;EACE,iBAAA;EACA,oBAAA;EACA,gBAAA;EACA,WAAA;EACA,eAAA;EACA,2BAAA;EzC+jLD;AyCrkLD;;EAUI,oBAAA;EzC+jLH;AyCzkLD;EAaI,gBAAA;EACA,mBAAA;EACA,aAAA;EzC+jLH;AyC9kLD;EAkBI,kBAAA;EACA,gBAAA;EACA,aAAA;EACA,8CAAA;EzC+jLH;A0CzlLC;EACE,2BAAA;E1C2lLH;A0CzlLC;EACE,2BAAA;E1C2lLH;A0C/lLC;EACE,2BAAA;E1CimLH;A0C/lLC;EACE,2BAAA;E1CimLH;A0CrmLC;EACE,2BAAA;E1CumLH;A0CrmLC;EACE,2BAAA;E1CumLH;A0C3mLC;EACE,2BAAA;E1C6mLH;A0C3mLC;EACE,2BAAA;E1C6mLH;A0CjnLC;EACE,2BAAA;E1CmnLH;A0CjnLC;EACE,2BAAA;E1CmnLH;A0CvnLC;EACE,2BAAA;E1CynLH;A0CvnLC;EACE,2BAAA;E1CynLH;A0C7nLC;EACE,2BAAA;E1C+nLH;A0C7nLC;EACE,2BAAA;E1C+nLH;A0CnoLC;EACE,2BAAA;E1CqoLH;A0CnoLC;EACE,2BAAA;E1CqoLH;A0CzoLC;EACE,2BAAA;E1C2oLH;A0CzoLC;EACE,2BAAA;E1C2oLH;A0C/oLC;EACE,2BAAA;E1CipLH;A0C/oLC;EACE,2BAAA;E1CipLH;AyCxmLD;EACE,gBAAA;EzC0mLD;AyCtmLD;EACE,gBAAA;EACA,iBAAA;EACA,mBAAA;EzCwmLD;AyC3mLD;EAMI,gBAAA;EzCwmLH;AyCnmLD;EACE,gBAAA;EACA,iBAAA;EACA,kBAAA;EACA,kBAAA;EzCqmLD;A2CnqLD;EACE,sDAAA;EACA,8BAAA;EACA,gBAAA;EACA,qBAAA;EACA,0BAAA;EACA,oBAAA;E3CqqLD;A2ClqLD;EACE,2BAAA;EACA,mBAAA;EACA,iCAAA;E3CoqLD;A2CjqLD;EACE,aAAA;EACA,oBAAA;EACA,YAAA;EACA,aAAA;E3CmqLD;A2CvqLD;EAOI,gBAAA;EACA,oBAAA;EACA,aAAA;E3CmqLH;A2C5qLD;EAYI,iBAAA;EACA,kBAAA;EACA,mBAAA;EACA,cAAA;E3CmqLH;A2ClrLD;EAkBM,gBAAA;EACA,gBAAA;EACA,oBAAA;EACA,kBAAA;E3CmqLL;A2C5pLD;EACE,2BAAA;EACA,yBAAA;EACA,oBAAA;EACA,oBAAA;E3C8pLD;A2ClqLD;EAOI,oBAAA;EACA,oBAAA;E3C8pLH;A2CtqLD;EAWI,2BAAA;EACA,iBAAA;EACA,kBAAA;E3C8pLH;A2C5pLG;EACE,uBAAA;E3C8pLL;A2C5pLK;EACE,gBAAA;E3C8pLP;A2CjrLD;EAwBI,gBAAA;EACA,iBAAA;EACA,oBAAA;EACA,aAAA;EACA,UAAA;EACA,8BAAA;UAAA,sBAAA;E3C4pLH;A2CxpLD;EACE,gBAAA;EACA,gBAAA;EACA,iBAAA;EACA,kBAAA;EACA,oBAAA;E3C0pLD;A2CjpLD;EAJE;IACE,iDAAA;I3CwpLD;EACF;A4CnvLD;EACE,2BAAA;EACA,gBAAA;EACA,iBAAA;EACA,YAAA;E5CqvLD;A4CzvLD;EAOI,gBAAA;EACA,kBAAA;E5CqvLH;A4C7vLD;EAWI,iBAAA;EACA,mBAAA;EACA,qBAAA;E5CqvLH;A4CjvLD;EACE,kBAAA;EACA,mBAAA;EACA,iBAAA;EACA,mBAAA;E5CmvLD;A4ChvLD;EACE,gBAAA;EACA,qBAAA;EACA,cAAA;E5CkvLD;A4CrvLD;EAMI,cAAA;E5CkvLH;A4C7uLD;EACE,2BAAA;EACA,gBAAA;EACA,mBAAA;EACA,mBAAA;EACA,sBAAA;E5C+uLD;A4CpvLD;EAQI,gBAAA;E5C+uLH;A4CvvLD;EAWI,gBAAA;EACA,4BAAA;E5C+uLH;A4C7uLG;EACE,uBAAA;E5C+uLL;A4C9vLD;EAmBI,uBAAA;EACA,kBAAA;EACA,YAAA;E5C8uLH;A4CnwLD;EAwBM,+BAAA;EACA,mBAAA;EACA,gBAAA;E5C8uLL;A4C5uLK;EACE,kBAAA;EACA,kBAAA;E5C8uLP;A6C1yLD;EACE,kBAAA;E7C4yLD;A6CzyLD;;;EACE,gBAAA;E7C6yLD;A6C1yLD;;;EACE,kBAAA;E7C8yLD;A6C3yLD;;;EACE,kBAAA;E7C+yLD;A6C5yLD;;;EACE,mBAAA;E7CgzLD;A6C7yLD;;;EACE,mBAAA;E7CizLD;A6C9yLD;;;EACE,kBAAA;E7CkzLD;A6C/yLD;;;EACE,oBAAA;E7CmzLD;A6ChzLD;;;EACE,oBAAA;E7CozLD;A6CjzLD;;;EACE,qBAAA;E7CqzLD;A6ClzLD;;;EACE,qBAAA;E7CszLD;A6CnzLD;;;EACE,mBAAA;E7CuzLD;A6CpzLD;;;EACE,qBAAA;E7CwzLD;A6CrzLD;;;EACE,qBAAA;E7CyzLD;A6CtzLD;;;EACE,sBAAA;E7C0zLD;A6CvzLD;;;EACE,sBAAA;E7C2zLD;A6CxzLD;;;EACE,iBAAA;E7C4zLD;A6CzzLD;;;EACE,mBAAA;E7C6zLD;A6C1zLD;;;EACE,mBAAA;E7C8zLD;A6C3zLD;;;EACE,oBAAA;E7C+zLD;A6C5zLD;;;EACE,oBAAA;E7Cg0LD;A6C7zLD;;;EACE,iBAAA;E7Ci0LD;A6C9zLD;;;EACE,iBAAA;E7Ck0LD;A6C/zLD;;;EACE,iBAAA;E7Cm0LD;A6Ch0LD;;;EACE,kBAAA;E7Co0LD;A6Cj0LD;;;EACE,kBAAA;E7Cq0LD;A6Cl0LD;;;EACE,mBAAA;E7Cs0LD;A6Cn0LD;;;EACE,mBAAA;E7Cu0LD;A6Cp0LD;;;EACE,mBAAA;E7Cw0LD;A6Cr0LD;;;EACE,oBAAA;E7Cy0LD;A6Ct0LD;;;EACE,oBAAA;E7C00LD;A6Cv0LD;;;EACE,oBAAA;E7C20LD;A6Cx0LD;;;EACE,oBAAA;E7C40LD;A6Cz0LD;;;EACE,oBAAA;E7C60LD;A6C10LD;;;EACE,qBAAA;E7C80LD;A6C30LD;;;EACE,qBAAA;E7C+0LD;A6C50LD;;;EACE,kBAAA;E7Cg1LD;A6C70LD;;;EACE,kBAAA;E7Ci1LD;A6C90LD;;;EACE,kBAAA;E7Ck1LD;A6C/0LD;;;EACE,mBAAA;E7Cm1LD;A6Ch1LD;;;EACE,mBAAA;E7Co1LD;AACD,sFAAqF;A8Cv9LrF;EAhCE;IACE,qBAAA;IACA,mBAAA;I9C0/LD;E8Cx/LD;;IACE,6BAAA;IACA,wBAAA;I9C2/LD;E8Cx/LC;;;IAEE,oBAAA;I9C2/LH;E8Cx/LD;;IACE,mCAAA;I9C2/LD;E8Cz/LD;IACE,cAAA;IACA,aAAA;IACA,2BAAA;I9C2/LD;E8Cz/LD;IACE,2BAAA;I9C2/LD;E8Cz/LD;;IACE,6BAAA;I9C4/LD;E8C1/LD;;;IACE,gCAAA;I9C8/LD;EACF", "file": "flat-ui.css", "sourcesContent": ["//\n// Fonts\n// --------------------------------------------------\n\n@font-face {\n  font-family: 'Lato';\n  src: url('@{local-font-path}@{local-font-name-black}.eot');\n  src: url('@{local-font-path}@{local-font-name-black}.eot?#iefix') format('embedded-opentype'),\n       url('@{local-font-path}@{local-font-name-black}.woff') format('woff'),\n       url('@{local-font-path}@{local-font-name-black}.ttf') format('truetype'),\n       url('@{local-font-path}@{local-font-name-black}.svg#@{local-font-svg-id-black}') format('svg');\n  font-weight: 900;\n  font-style: normal;\n}\n\n@font-face {\n  font-family: 'Lato';\n  src: url('@{local-font-path}@{local-font-name-bold}.eot');\n  src: url('@{local-font-path}@{local-font-name-bold}.eot?#iefix') format('embedded-opentype'),\n       url('@{local-font-path}@{local-font-name-bold}.woff') format('woff'),\n       url('@{local-font-path}@{local-font-name-bold}.ttf') format('truetype'),\n       url('@{local-font-path}@{local-font-name-bold}.svg#@{local-font-svg-id-bold}') format('svg');\n  font-weight: bold;\n  font-style: normal;\n}\n\n@font-face {\n  font-family: 'Lato';\n  src: url('@{local-font-path}@{local-font-name-bold-italic}.eot');\n  src: url('@{local-font-path}@{local-font-name-bold-italic}.eot?#iefix') format('embedded-opentype'),\n       url('@{local-font-path}@{local-font-name-bold-italic}.woff') format('woff'),\n       url('@{local-font-path}@{local-font-name-bold-italic}.ttf') format('truetype'),\n       url('@{local-font-path}@{local-font-name-bold-italic}.svg#@{local-font-svg-id-bold-italic}') format('svg');\n  font-weight: bold;\n  font-style: italic;\n}\n\n@font-face {\n  font-family: 'Lato';\n  src: url('@{local-font-path}@{local-font-name-italic}.eot');\n  src: url('@{local-font-path}@{local-font-name-italic}.eot?#iefix') format('embedded-opentype'),\n       url('@{local-font-path}@{local-font-name-italic}.woff') format('woff'),\n       url('@{local-font-path}@{local-font-name-italic}.ttf') format('truetype'),\n       url('@{local-font-path}@{local-font-name-italic}.svg#@{local-font-svg-id-italic}') format('svg');\n  font-weight: normal;\n  font-style: italic;\n}\n\n@font-face {\n  font-family: 'Lato';\n  src: url('@{local-font-path}@{local-font-name-light}.eot');\n  src: url('@{local-font-path}@{local-font-name-light}.eot?#iefix') format('embedded-opentype'),\n       url('@{local-font-path}@{local-font-name-light}.woff') format('woff'),\n       url('@{local-font-path}@{local-font-name-light}.ttf') format('truetype'),\n       url('@{local-font-path}@{local-font-name-light}.svg#@{local-font-svg-id-light}') format('svg');\n  font-weight: 300;\n  font-style: normal;\n}\n\n@font-face {\n  font-family: 'Lato';\n  src: url('@{local-font-path}@{local-font-name}.eot');\n  src: url('@{local-font-path}@{local-font-name}.eot?#iefix') format('embedded-opentype'),\n       url('@{local-font-path}@{local-font-name}.woff') format('woff'),\n       url('@{local-font-path}@{local-font-name}.ttf') format('truetype'),\n       url('@{local-font-path}@{local-font-name}.svg#@{local-font-svg-id}') format('svg');\n  font-weight: normal;\n  font-style: normal;\n}\n", "@font-face {\n  font-family: 'Lato';\n  src: url('../fonts/lato/lato-black.eot');\n  src: url('../fonts/lato/lato-black.eot?#iefix') format('embedded-opentype'), url('../fonts/lato/lato-black.woff') format('woff'), url('../fonts/lato/lato-black.ttf') format('truetype'), url('../fonts/lato/lato-black.svg#latoblack') format('svg');\n  font-weight: 900;\n  font-style: normal;\n}\n@font-face {\n  font-family: 'Lato';\n  src: url('../fonts/lato/lato-bold.eot');\n  src: url('../fonts/lato/lato-bold.eot?#iefix') format('embedded-opentype'), url('../fonts/lato/lato-bold.woff') format('woff'), url('../fonts/lato/lato-bold.ttf') format('truetype'), url('../fonts/lato/lato-bold.svg#latobold') format('svg');\n  font-weight: bold;\n  font-style: normal;\n}\n@font-face {\n  font-family: 'Lato';\n  src: url('../fonts/lato/lato-bolditalic.eot');\n  src: url('../fonts/lato/lato-bolditalic.eot?#iefix') format('embedded-opentype'), url('../fonts/lato/lato-bolditalic.woff') format('woff'), url('../fonts/lato/lato-bolditalic.ttf') format('truetype'), url('../fonts/lato/lato-bolditalic.svg#latobold-italic') format('svg');\n  font-weight: bold;\n  font-style: italic;\n}\n@font-face {\n  font-family: 'Lato';\n  src: url('../fonts/lato/lato-italic.eot');\n  src: url('../fonts/lato/lato-italic.eot?#iefix') format('embedded-opentype'), url('../fonts/lato/lato-italic.woff') format('woff'), url('../fonts/lato/lato-italic.ttf') format('truetype'), url('../fonts/lato/lato-italic.svg#latoitalic') format('svg');\n  font-weight: normal;\n  font-style: italic;\n}\n@font-face {\n  font-family: 'Lato';\n  src: url('../fonts/lato/lato-light.eot');\n  src: url('../fonts/lato/lato-light.eot?#iefix') format('embedded-opentype'), url('../fonts/lato/lato-light.woff') format('woff'), url('../fonts/lato/lato-light.ttf') format('truetype'), url('../fonts/lato/lato-light.svg#latolight') format('svg');\n  font-weight: 300;\n  font-style: normal;\n}\n@font-face {\n  font-family: 'Lato';\n  src: url('../fonts/lato/lato-regular.eot');\n  src: url('../fonts/lato/lato-regular.eot?#iefix') format('embedded-opentype'), url('../fonts/lato/lato-regular.woff') format('woff'), url('../fonts/lato/lato-regular.ttf') format('truetype'), url('../fonts/lato/lato-regular.svg#latoregular') format('svg');\n  font-weight: normal;\n  font-style: normal;\n}\n@font-face {\n  font-family: 'Flat-UI-Icons';\n  src: url('../fonts/glyphicons/flat-ui-icons-regular.eot');\n  src: url('../fonts/glyphicons/flat-ui-icons-regular.eot?#iefix') format('embedded-opentype'), url('../fonts/glyphicons/flat-ui-icons-regular.woff') format('woff'), url('../fonts/glyphicons/flat-ui-icons-regular.ttf') format('truetype'), url('../fonts/glyphicons/flat-ui-icons-regular.svg#flat-ui-icons-regular') format('svg');\n}\n[class^=\"fui-\"],\n[class*=\"fui-\"] {\n  font-family: 'Flat-UI-Icons';\n  speak: none;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.fui-triangle-up:before {\n  content: \"\\e600\";\n}\n.fui-triangle-down:before {\n  content: \"\\e601\";\n}\n.fui-triangle-up-small:before {\n  content: \"\\e602\";\n}\n.fui-triangle-down-small:before {\n  content: \"\\e603\";\n}\n.fui-triangle-left-large:before {\n  content: \"\\e604\";\n}\n.fui-triangle-right-large:before {\n  content: \"\\e605\";\n}\n.fui-arrow-left:before {\n  content: \"\\e606\";\n}\n.fui-arrow-right:before {\n  content: \"\\e607\";\n}\n.fui-plus:before {\n  content: \"\\e608\";\n}\n.fui-cross:before {\n  content: \"\\e609\";\n}\n.fui-check:before {\n  content: \"\\e60a\";\n}\n.fui-radio-unchecked:before {\n  content: \"\\e60b\";\n}\n.fui-radio-checked:before {\n  content: \"\\e60c\";\n}\n.fui-checkbox-unchecked:before {\n  content: \"\\e60d\";\n}\n.fui-checkbox-checked:before {\n  content: \"\\e60e\";\n}\n.fui-info-circle:before {\n  content: \"\\e60f\";\n}\n.fui-alert-circle:before {\n  content: \"\\e610\";\n}\n.fui-question-circle:before {\n  content: \"\\e611\";\n}\n.fui-check-circle:before {\n  content: \"\\e612\";\n}\n.fui-cross-circle:before {\n  content: \"\\e613\";\n}\n.fui-plus-circle:before {\n  content: \"\\e614\";\n}\n.fui-pause:before {\n  content: \"\\e615\";\n}\n.fui-play:before {\n  content: \"\\e616\";\n}\n.fui-volume:before {\n  content: \"\\e617\";\n}\n.fui-mute:before {\n  content: \"\\e618\";\n}\n.fui-resize:before {\n  content: \"\\e619\";\n}\n.fui-list:before {\n  content: \"\\e61a\";\n}\n.fui-list-thumbnailed:before {\n  content: \"\\e61b\";\n}\n.fui-list-small-thumbnails:before {\n  content: \"\\e61c\";\n}\n.fui-list-large-thumbnails:before {\n  content: \"\\e61d\";\n}\n.fui-list-numbered:before {\n  content: \"\\e61e\";\n}\n.fui-list-columned:before {\n  content: \"\\e61f\";\n}\n.fui-list-bulleted:before {\n  content: \"\\e620\";\n}\n.fui-window:before {\n  content: \"\\e621\";\n}\n.fui-windows:before {\n  content: \"\\e622\";\n}\n.fui-loop:before {\n  content: \"\\e623\";\n}\n.fui-cmd:before {\n  content: \"\\e624\";\n}\n.fui-mic:before {\n  content: \"\\e625\";\n}\n.fui-heart:before {\n  content: \"\\e626\";\n}\n.fui-location:before {\n  content: \"\\e627\";\n}\n.fui-new:before {\n  content: \"\\e628\";\n}\n.fui-video:before {\n  content: \"\\e629\";\n}\n.fui-photo:before {\n  content: \"\\e62a\";\n}\n.fui-time:before {\n  content: \"\\e62b\";\n}\n.fui-eye:before {\n  content: \"\\e62c\";\n}\n.fui-chat:before {\n  content: \"\\e62d\";\n}\n.fui-home:before {\n  content: \"\\e62e\";\n}\n.fui-upload:before {\n  content: \"\\e62f\";\n}\n.fui-search:before {\n  content: \"\\e630\";\n}\n.fui-user:before {\n  content: \"\\e631\";\n}\n.fui-mail:before {\n  content: \"\\e632\";\n}\n.fui-lock:before {\n  content: \"\\e633\";\n}\n.fui-power:before {\n  content: \"\\e634\";\n}\n.fui-calendar:before {\n  content: \"\\e635\";\n}\n.fui-gear:before {\n  content: \"\\e636\";\n}\n.fui-bookmark:before {\n  content: \"\\e637\";\n}\n.fui-exit:before {\n  content: \"\\e638\";\n}\n.fui-trash:before {\n  content: \"\\e639\";\n}\n.fui-folder:before {\n  content: \"\\e63a\";\n}\n.fui-bubble:before {\n  content: \"\\e63b\";\n}\n.fui-export:before {\n  content: \"\\e63c\";\n}\n.fui-calendar-solid:before {\n  content: \"\\e63d\";\n}\n.fui-star:before {\n  content: \"\\e63e\";\n}\n.fui-star-2:before {\n  content: \"\\e63f\";\n}\n.fui-credit-card:before {\n  content: \"\\e640\";\n}\n.fui-clip:before {\n  content: \"\\e641\";\n}\n.fui-link:before {\n  content: \"\\e642\";\n}\n.fui-tag:before {\n  content: \"\\e643\";\n}\n.fui-document:before {\n  content: \"\\e644\";\n}\n.fui-image:before {\n  content: \"\\e645\";\n}\n.fui-facebook:before {\n  content: \"\\e646\";\n}\n.fui-youtube:before {\n  content: \"\\e647\";\n}\n.fui-vimeo:before {\n  content: \"\\e648\";\n}\n.fui-twitter:before {\n  content: \"\\e649\";\n}\n.fui-spotify:before {\n  content: \"\\e64a\";\n}\n.fui-skype:before {\n  content: \"\\e64b\";\n}\n.fui-pinterest:before {\n  content: \"\\e64c\";\n}\n.fui-path:before {\n  content: \"\\e64d\";\n}\n.fui-linkedin:before {\n  content: \"\\e64e\";\n}\n.fui-google-plus:before {\n  content: \"\\e64f\";\n}\n.fui-dribbble:before {\n  content: \"\\e650\";\n}\n.fui-behance:before {\n  content: \"\\e651\";\n}\n.fui-stumbleupon:before {\n  content: \"\\e652\";\n}\n.fui-yelp:before {\n  content: \"\\e653\";\n}\n.fui-wordpress:before {\n  content: \"\\e654\";\n}\n.fui-windows-8:before {\n  content: \"\\e655\";\n}\n.fui-vine:before {\n  content: \"\\e656\";\n}\n.fui-tumblr:before {\n  content: \"\\e657\";\n}\n.fui-paypal:before {\n  content: \"\\e658\";\n}\n.fui-lastfm:before {\n  content: \"\\e659\";\n}\n.fui-instagram:before {\n  content: \"\\e65a\";\n}\n.fui-html5:before {\n  content: \"\\e65b\";\n}\n.fui-github:before {\n  content: \"\\e65c\";\n}\n.fui-foursquare:before {\n  content: \"\\e65d\";\n}\n.fui-dropbox:before {\n  content: \"\\e65e\";\n}\n.fui-android:before {\n  content: \"\\e65f\";\n}\n.fui-apple:before {\n  content: \"\\e660\";\n}\nbody {\n  font-family: \"Lato\", Helvetica, Arial, sans-serif;\n  font-size: 18px;\n  line-height: 1.72222;\n  color: #34495e;\n  background-color: #ffffff;\n}\na {\n  color: #16a085;\n  text-decoration: none;\n  transition: .25s;\n}\na:hover,\na:focus {\n  color: #1abc9c;\n  text-decoration: none;\n}\na:focus {\n  outline: none;\n}\n.img-rounded {\n  border-radius: 6px;\n}\n.img-thumbnail {\n  padding: 4px;\n  line-height: 1.72222;\n  background-color: #ffffff;\n  border: 2px solid #bdc3c7;\n  border-radius: 6px;\n  transition: all 0.25s ease-in-out;\n  display: inline-block;\n  max-width: 100%;\n  height: auto;\n}\n.img-comment {\n  font-size: 15px;\n  line-height: 1.2;\n  font-style: italic;\n  margin: 24px 0;\n}\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\n.h1,\n.h2,\n.h3,\n.h4,\n.h5,\n.h6 {\n  font-family: inherit;\n  font-weight: 700;\n  line-height: 1.1;\n  color: inherit;\n}\nh1 small,\nh2 small,\nh3 small,\nh4 small,\nh5 small,\nh6 small,\n.h1 small,\n.h2 small,\n.h3 small,\n.h4 small,\n.h5 small,\n.h6 small {\n  color: #e7e9ec;\n}\nh1,\nh2,\nh3 {\n  margin-top: 30px;\n  margin-bottom: 15px;\n}\nh4,\nh5,\nh6 {\n  margin-top: 15px;\n  margin-bottom: 15px;\n}\nh6 {\n  font-weight: normal;\n}\nh1,\n.h1 {\n  font-size: 61px;\n}\nh2,\n.h2 {\n  font-size: 53px;\n}\nh3,\n.h3 {\n  font-size: 40px;\n}\nh4,\n.h4 {\n  font-size: 29px;\n}\nh5,\n.h5 {\n  font-size: 28px;\n}\nh6,\n.h6 {\n  font-size: 24px;\n}\np {\n  font-size: 18px;\n  line-height: 1.72222;\n  margin: 0 0 15px;\n}\n.lead {\n  margin-bottom: 30px;\n  font-size: 28px;\n  line-height: 1.46428571;\n  font-weight: 300;\n}\n@media (min-width: 768px) {\n  .lead {\n    font-size: 30.006px;\n  }\n}\nsmall,\n.small {\n  font-size: 83%;\n  line-height: 2.067;\n}\n.text-muted {\n  color: #bdc3c7;\n}\n.text-inverse {\n  color: #ffffff;\n}\n.text-primary {\n  color: #1abc9c;\n}\na.text-primary:hover {\n  color: #148f77;\n}\n.text-warning {\n  color: #f1c40f;\n}\na.text-warning:hover {\n  color: #c29d0b;\n}\n.text-danger {\n  color: #e74c3c;\n}\na.text-danger:hover {\n  color: #d62c1a;\n}\n.text-success {\n  color: #2ecc71;\n}\na.text-success:hover {\n  color: #25a25a;\n}\n.text-info {\n  color: #3498db;\n}\na.text-info:hover {\n  color: #217dbb;\n}\n.bg-primary {\n  color: #ffffff;\n  background-color: #34495e;\n}\na.bg-primary:hover {\n  background-color: #222f3d;\n}\n.bg-success {\n  background-color: #dff0d8;\n}\na.bg-success:hover {\n  background-color: #c1e2b3;\n}\n.bg-info {\n  background-color: #d9edf7;\n}\na.bg-info:hover {\n  background-color: #afd9ee;\n}\n.bg-warning {\n  background-color: #fcf8e3;\n}\na.bg-warning:hover {\n  background-color: #f7ecb5;\n}\n.bg-danger {\n  background-color: #f2dede;\n}\na.bg-danger:hover {\n  background-color: #e4b9b9;\n}\n.page-header {\n  padding-bottom: 14px;\n  margin: 60px 0 30px;\n  border-bottom: 2px solid #e7e9ec;\n}\nul,\nol {\n  margin-bottom: 15px;\n}\ndl {\n  margin-bottom: 30px;\n}\ndt,\ndd {\n  line-height: 1.72222;\n}\n@media (min-width: 768px) {\n  .dl-horizontal dt {\n    width: 160px;\n  }\n  .dl-horizontal dd {\n    margin-left: 180px;\n  }\n}\nabbr[title],\nabbr[data-original-title] {\n  border-bottom: 1px dotted #bdc3c7;\n}\nblockquote {\n  border-left: 3px solid #e7e9ec;\n  padding: 0 0 0 16px;\n  margin: 0 0 30px;\n}\nblockquote p {\n  font-size: 20px;\n  line-height: 1.55;\n  font-weight: normal;\n  margin-bottom: .4em;\n}\nblockquote small,\nblockquote .small {\n  font-size: 18px;\n  line-height: 1.72222;\n  font-style: italic;\n  color: inherit;\n}\nblockquote small:before,\nblockquote .small:before {\n  content: \"\";\n}\nblockquote.pull-right {\n  padding-right: 16px;\n  padding-left: 0;\n  border-right: 3px solid #e7e9ec;\n  border-left: 0;\n}\nblockquote.pull-right small:after {\n  content: \"\";\n}\naddress {\n  margin-bottom: 30px;\n  line-height: 1.72222;\n}\nsub,\nsup {\n  font-size: 70%;\n}\ncode,\nkbd,\npre,\nsamp {\n  font-family: Monaco, Menlo, Consolas, \"Courier New\", monospace;\n}\ncode {\n  padding: 2px 6px;\n  font-size: 85%;\n  color: #c7254e;\n  background-color: #f9f2f4;\n  border-radius: 4px;\n}\nkbd {\n  padding: 2px 6px;\n  font-size: 85%;\n  color: #ffffff;\n  background-color: #34495e;\n  border-radius: 4px;\n  box-shadow: none;\n}\npre {\n  padding: 8px;\n  margin: 0 0 15px;\n  font-size: 13px;\n  line-height: 1.72222;\n  color: inherit;\n  background-color: #ffffff;\n  border: 2px solid #e7e9ec;\n  border-radius: 6px;\n  white-space: pre;\n}\n.pre-scrollable {\n  max-height: 340px;\n}\n.thumbnail {\n  display: block;\n  padding: 4px;\n  margin-bottom: 5px;\n  line-height: 1.72222;\n  background-color: #ffffff;\n  border: 2px solid #bdc3c7;\n  border-radius: 6px;\n  transition: border 0.25s ease-in-out;\n}\n.thumbnail > img,\n.thumbnail a > img {\n  display: block;\n  max-width: 100%;\n  height: auto;\n  margin-left: auto;\n  margin-right: auto;\n}\na.thumbnail:hover,\na.thumbnail:focus,\na.thumbnail.active {\n  border-color: #16a085;\n}\n.thumbnail .caption {\n  padding: 9px;\n  color: #34495e;\n}\n.btn {\n  border: none;\n  font-size: 15px;\n  font-weight: normal;\n  line-height: 1.4;\n  border-radius: 4px;\n  padding: 10px 15px;\n  -webkit-font-smoothing: subpixel-antialiased;\n  transition: border 0.25s linear, color 0.25s linear, background-color 0.25s linear;\n}\n.btn:hover,\n.btn:focus {\n  outline: none;\n  color: #ffffff;\n}\n.btn:active,\n.btn.active {\n  outline: none;\n  box-shadow: none;\n}\n.btn:focus:active {\n  outline: none;\n}\n.btn.disabled,\n.btn[disabled],\nfieldset[disabled] .btn {\n  background-color: #bdc3c7;\n  color: rgba(255, 255, 255, 0.75);\n  opacity: 0.7;\n  filter: alpha(opacity=70);\n  cursor: not-allowed;\n}\n.btn [class^=\"fui-\"] {\n  margin: 0 1px;\n  position: relative;\n  line-height: 1;\n  top: 1px;\n}\n.btn-xs.btn [class^=\"fui-\"] {\n  font-size: 11px;\n  top: 0;\n}\n.btn-hg.btn [class^=\"fui-\"] {\n  top: 2px;\n}\n.btn-default {\n  color: #ffffff;\n  background-color: #bdc3c7;\n}\n.btn-default:hover,\n.btn-default.hover,\n.btn-default:focus,\n.btn-default:active,\n.btn-default.active,\n.open > .dropdown-toggle.btn-default {\n  color: #ffffff;\n  background-color: #cacfd2;\n  border-color: #cacfd2;\n}\n.btn-default:active,\n.btn-default.active,\n.open > .dropdown-toggle.btn-default {\n  background: #a1a6a9;\n  border-color: #a1a6a9;\n}\n.btn-default.disabled,\n.btn-default[disabled],\nfieldset[disabled] .btn-default,\n.btn-default.disabled:hover,\n.btn-default[disabled]:hover,\nfieldset[disabled] .btn-default:hover,\n.btn-default.disabled.hover,\n.btn-default[disabled].hover,\nfieldset[disabled] .btn-default.hover,\n.btn-default.disabled:focus,\n.btn-default[disabled]:focus,\nfieldset[disabled] .btn-default:focus,\n.btn-default.disabled:active,\n.btn-default[disabled]:active,\nfieldset[disabled] .btn-default:active,\n.btn-default.disabled.active,\n.btn-default[disabled].active,\nfieldset[disabled] .btn-default.active {\n  background-color: #bdc3c7;\n  border-color: #bdc3c7;\n}\n.btn-default .badge {\n  color: #bdc3c7;\n  background-color: #ffffff;\n}\n.btn-primary {\n  color: #ffffff;\n  background-color: #1abc9c;\n}\n.btn-primary:hover,\n.btn-primary.hover,\n.btn-primary:focus,\n.btn-primary:active,\n.btn-primary.active,\n.open > .dropdown-toggle.btn-primary {\n  color: #ffffff;\n  background-color: #48c9b0;\n  border-color: #48c9b0;\n}\n.btn-primary:active,\n.btn-primary.active,\n.open > .dropdown-toggle.btn-primary {\n  background: #16a085;\n  border-color: #16a085;\n}\n.btn-primary.disabled,\n.btn-primary[disabled],\nfieldset[disabled] .btn-primary,\n.btn-primary.disabled:hover,\n.btn-primary[disabled]:hover,\nfieldset[disabled] .btn-primary:hover,\n.btn-primary.disabled.hover,\n.btn-primary[disabled].hover,\nfieldset[disabled] .btn-primary.hover,\n.btn-primary.disabled:focus,\n.btn-primary[disabled]:focus,\nfieldset[disabled] .btn-primary:focus,\n.btn-primary.disabled:active,\n.btn-primary[disabled]:active,\nfieldset[disabled] .btn-primary:active,\n.btn-primary.disabled.active,\n.btn-primary[disabled].active,\nfieldset[disabled] .btn-primary.active {\n  background-color: #bdc3c7;\n  border-color: #1abc9c;\n}\n.btn-primary .badge {\n  color: #1abc9c;\n  background-color: #ffffff;\n}\n.btn-info {\n  color: #ffffff;\n  background-color: #3498db;\n}\n.btn-info:hover,\n.btn-info.hover,\n.btn-info:focus,\n.btn-info:active,\n.btn-info.active,\n.open > .dropdown-toggle.btn-info {\n  color: #ffffff;\n  background-color: #5dade2;\n  border-color: #5dade2;\n}\n.btn-info:active,\n.btn-info.active,\n.open > .dropdown-toggle.btn-info {\n  background: #2c81ba;\n  border-color: #2c81ba;\n}\n.btn-info.disabled,\n.btn-info[disabled],\nfieldset[disabled] .btn-info,\n.btn-info.disabled:hover,\n.btn-info[disabled]:hover,\nfieldset[disabled] .btn-info:hover,\n.btn-info.disabled.hover,\n.btn-info[disabled].hover,\nfieldset[disabled] .btn-info.hover,\n.btn-info.disabled:focus,\n.btn-info[disabled]:focus,\nfieldset[disabled] .btn-info:focus,\n.btn-info.disabled:active,\n.btn-info[disabled]:active,\nfieldset[disabled] .btn-info:active,\n.btn-info.disabled.active,\n.btn-info[disabled].active,\nfieldset[disabled] .btn-info.active {\n  background-color: #bdc3c7;\n  border-color: #3498db;\n}\n.btn-info .badge {\n  color: #3498db;\n  background-color: #ffffff;\n}\n.btn-danger {\n  color: #ffffff;\n  background-color: #e74c3c;\n}\n.btn-danger:hover,\n.btn-danger.hover,\n.btn-danger:focus,\n.btn-danger:active,\n.btn-danger.active,\n.open > .dropdown-toggle.btn-danger {\n  color: #ffffff;\n  background-color: #ec7063;\n  border-color: #ec7063;\n}\n.btn-danger:active,\n.btn-danger.active,\n.open > .dropdown-toggle.btn-danger {\n  background: #c44133;\n  border-color: #c44133;\n}\n.btn-danger.disabled,\n.btn-danger[disabled],\nfieldset[disabled] .btn-danger,\n.btn-danger.disabled:hover,\n.btn-danger[disabled]:hover,\nfieldset[disabled] .btn-danger:hover,\n.btn-danger.disabled.hover,\n.btn-danger[disabled].hover,\nfieldset[disabled] .btn-danger.hover,\n.btn-danger.disabled:focus,\n.btn-danger[disabled]:focus,\nfieldset[disabled] .btn-danger:focus,\n.btn-danger.disabled:active,\n.btn-danger[disabled]:active,\nfieldset[disabled] .btn-danger:active,\n.btn-danger.disabled.active,\n.btn-danger[disabled].active,\nfieldset[disabled] .btn-danger.active {\n  background-color: #bdc3c7;\n  border-color: #e74c3c;\n}\n.btn-danger .badge {\n  color: #e74c3c;\n  background-color: #ffffff;\n}\n.btn-success {\n  color: #ffffff;\n  background-color: #2ecc71;\n}\n.btn-success:hover,\n.btn-success.hover,\n.btn-success:focus,\n.btn-success:active,\n.btn-success.active,\n.open > .dropdown-toggle.btn-success {\n  color: #ffffff;\n  background-color: #58d68d;\n  border-color: #58d68d;\n}\n.btn-success:active,\n.btn-success.active,\n.open > .dropdown-toggle.btn-success {\n  background: #27ad60;\n  border-color: #27ad60;\n}\n.btn-success.disabled,\n.btn-success[disabled],\nfieldset[disabled] .btn-success,\n.btn-success.disabled:hover,\n.btn-success[disabled]:hover,\nfieldset[disabled] .btn-success:hover,\n.btn-success.disabled.hover,\n.btn-success[disabled].hover,\nfieldset[disabled] .btn-success.hover,\n.btn-success.disabled:focus,\n.btn-success[disabled]:focus,\nfieldset[disabled] .btn-success:focus,\n.btn-success.disabled:active,\n.btn-success[disabled]:active,\nfieldset[disabled] .btn-success:active,\n.btn-success.disabled.active,\n.btn-success[disabled].active,\nfieldset[disabled] .btn-success.active {\n  background-color: #bdc3c7;\n  border-color: #2ecc71;\n}\n.btn-success .badge {\n  color: #2ecc71;\n  background-color: #ffffff;\n}\n.btn-warning {\n  color: #ffffff;\n  background-color: #f1c40f;\n}\n.btn-warning:hover,\n.btn-warning.hover,\n.btn-warning:focus,\n.btn-warning:active,\n.btn-warning.active,\n.open > .dropdown-toggle.btn-warning {\n  color: #ffffff;\n  background-color: #f4d313;\n  border-color: #f4d313;\n}\n.btn-warning:active,\n.btn-warning.active,\n.open > .dropdown-toggle.btn-warning {\n  background: #cda70d;\n  border-color: #cda70d;\n}\n.btn-warning.disabled,\n.btn-warning[disabled],\nfieldset[disabled] .btn-warning,\n.btn-warning.disabled:hover,\n.btn-warning[disabled]:hover,\nfieldset[disabled] .btn-warning:hover,\n.btn-warning.disabled.hover,\n.btn-warning[disabled].hover,\nfieldset[disabled] .btn-warning.hover,\n.btn-warning.disabled:focus,\n.btn-warning[disabled]:focus,\nfieldset[disabled] .btn-warning:focus,\n.btn-warning.disabled:active,\n.btn-warning[disabled]:active,\nfieldset[disabled] .btn-warning:active,\n.btn-warning.disabled.active,\n.btn-warning[disabled].active,\nfieldset[disabled] .btn-warning.active {\n  background-color: #bdc3c7;\n  border-color: #f1c40f;\n}\n.btn-warning .badge {\n  color: #f1c40f;\n  background-color: #ffffff;\n}\n.btn-inverse {\n  color: #ffffff;\n  background-color: #34495e;\n}\n.btn-inverse:hover,\n.btn-inverse.hover,\n.btn-inverse:focus,\n.btn-inverse:active,\n.btn-inverse.active,\n.open > .dropdown-toggle.btn-inverse {\n  color: #ffffff;\n  background-color: #415b76;\n  border-color: #415b76;\n}\n.btn-inverse:active,\n.btn-inverse.active,\n.open > .dropdown-toggle.btn-inverse {\n  background: #2c3e50;\n  border-color: #2c3e50;\n}\n.btn-inverse.disabled,\n.btn-inverse[disabled],\nfieldset[disabled] .btn-inverse,\n.btn-inverse.disabled:hover,\n.btn-inverse[disabled]:hover,\nfieldset[disabled] .btn-inverse:hover,\n.btn-inverse.disabled.hover,\n.btn-inverse[disabled].hover,\nfieldset[disabled] .btn-inverse.hover,\n.btn-inverse.disabled:focus,\n.btn-inverse[disabled]:focus,\nfieldset[disabled] .btn-inverse:focus,\n.btn-inverse.disabled:active,\n.btn-inverse[disabled]:active,\nfieldset[disabled] .btn-inverse:active,\n.btn-inverse.disabled.active,\n.btn-inverse[disabled].active,\nfieldset[disabled] .btn-inverse.active {\n  background-color: #bdc3c7;\n  border-color: #34495e;\n}\n.btn-inverse .badge {\n  color: #34495e;\n  background-color: #ffffff;\n}\n.btn-embossed {\n  box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.15);\n}\n.btn-embossed.active,\n.btn-embossed:active {\n  box-shadow: inset 0 2px 0 rgba(0, 0, 0, 0.15);\n}\n.btn-wide {\n  min-width: 140px;\n  padding-left: 30px;\n  padding-right: 30px;\n}\n.btn-link {\n  color: #16a085;\n}\n.btn-link:hover,\n.btn-link:focus {\n  color: #1abc9c;\n  text-decoration: underline;\n  background-color: transparent;\n}\n.btn-link[disabled]:hover,\nfieldset[disabled] .btn-link:hover,\n.btn-link[disabled]:focus,\nfieldset[disabled] .btn-link:focus {\n  color: #bdc3c7;\n  text-decoration: none;\n}\n.btn-hg,\n.btn-group-hg > .btn {\n  padding: 13px 20px;\n  font-size: 22px;\n  line-height: 1.227;\n  border-radius: 6px;\n}\n.btn-lg,\n.btn-group-lg > .btn {\n  padding: 10px 19px;\n  font-size: 17px;\n  line-height: 1.471;\n  border-radius: 6px;\n}\n.btn-sm,\n.btn-group-sm > .btn {\n  padding: 9px 13px;\n  font-size: 13px;\n  line-height: 1.385;\n  border-radius: 4px;\n}\n.btn-xs,\n.btn-group-xs > .btn {\n  padding: 6px 9px;\n  font-size: 12px;\n  line-height: 1.083;\n  border-radius: 3px;\n}\n.btn-tip {\n  font-weight: 300;\n  padding-left: 10px;\n  font-size: 92%;\n}\n.btn-block {\n  white-space: normal;\n}\n[class*=\"btn-social-\"] {\n  padding: 10px 15px;\n  font-size: 13px;\n  line-height: 1.077;\n  border-radius: 4px;\n}\n.btn-social-pinterest {\n  color: #ffffff;\n  background-color: #cb2028;\n}\n.btn-social-pinterest:hover,\n.btn-social-pinterest:focus {\n  background-color: #d54d53;\n}\n.btn-social-pinterest:active,\n.btn-social-pinterest.active {\n  background-color: #ad1b22;\n}\n.btn-social-linkedin {\n  color: #ffffff;\n  background-color: #0072b5;\n}\n.btn-social-linkedin:hover,\n.btn-social-linkedin:focus {\n  background-color: #338ec4;\n}\n.btn-social-linkedin:active,\n.btn-social-linkedin.active {\n  background-color: #00619a;\n}\n.btn-social-stumbleupon {\n  color: #ffffff;\n  background-color: #ed4a13;\n}\n.btn-social-stumbleupon:hover,\n.btn-social-stumbleupon:focus {\n  background-color: #f16e42;\n}\n.btn-social-stumbleupon:active,\n.btn-social-stumbleupon.active {\n  background-color: #c93f10;\n}\n.btn-social-googleplus {\n  color: #ffffff;\n  background-color: #2d2d2d;\n}\n.btn-social-googleplus:hover,\n.btn-social-googleplus:focus {\n  background-color: #575757;\n}\n.btn-social-googleplus:active,\n.btn-social-googleplus.active {\n  background-color: #262626;\n}\n.btn-social-facebook {\n  color: #ffffff;\n  background-color: #2f4b93;\n}\n.btn-social-facebook:hover,\n.btn-social-facebook:focus {\n  background-color: #596fa9;\n}\n.btn-social-facebook:active,\n.btn-social-facebook.active {\n  background-color: #28407d;\n}\n.btn-social-twitter {\n  color: #ffffff;\n  background-color: #00bdef;\n}\n.btn-social-twitter:hover,\n.btn-social-twitter:focus {\n  background-color: #33caf2;\n}\n.btn-social-twitter:active,\n.btn-social-twitter.active {\n  background-color: #00a1cb;\n}\n.btn-group > .btn + .btn {\n  margin-left: 0;\n}\n.btn-group > .btn + .dropdown-toggle {\n  border-left: 2px solid rgba(52, 73, 94, 0.15);\n  padding: 10px 12px;\n}\n.btn-group > .btn + .dropdown-toggle .caret {\n  margin-left: 3px;\n  margin-right: 3px;\n}\n.btn-group > .btn.btn-gh + .dropdown-toggle .caret {\n  margin-left: 7px;\n  margin-right: 7px;\n}\n.btn-group > .btn.btn-sm + .dropdown-toggle .caret {\n  margin-left: 0;\n  margin-right: 0;\n}\n.dropdown-toggle .caret {\n  margin-left: 8px;\n}\n.btn-group-xs > .btn + .dropdown-toggle {\n  padding: 6px 9px;\n}\n.btn-group-sm > .btn + .dropdown-toggle {\n  padding: 9px 13px;\n}\n.btn-group-lg > .btn + .dropdown-toggle {\n  padding: 10px 19px;\n}\n.btn-group-hg > .btn + .dropdown-toggle {\n  padding: 13px 20px;\n}\n.btn-xs .caret {\n  border-width: 6px 4px 0;\n  border-bottom-width: 0;\n}\n.btn-lg .caret {\n  border-width: 8px 6px 0;\n  border-bottom-width: 0;\n}\n.dropup .btn-lg .caret {\n  border-width: 0 6px 8px;\n}\n.dropup .btn-xs .caret {\n  border-width: 0 4px 6px;\n}\n.btn-group > .btn,\n.btn-group > .dropdown-menu,\n.btn-group > .popover {\n  font-weight: 400;\n}\n.btn-group:focus .dropdown-toggle {\n  outline: none;\n  transition: .25s;\n}\n.btn-group.open .dropdown-toggle {\n  color: rgba(255, 255, 255, 0.75);\n  box-shadow: none;\n}\n.btn-toolbar .btn.active {\n  color: #ffffff;\n}\n.btn-toolbar .btn > [class^=\"fui-\"] {\n  font-size: 16px;\n  margin: 0 1px;\n}\nlegend {\n  display: block;\n  width: 100%;\n  padding: 0;\n  margin-bottom: 30px / 2;\n  font-size: 24px;\n  line-height: inherit;\n  color: inherit;\n  border-bottom: none;\n}\ntextarea {\n  font-size: 20px;\n  line-height: 24px;\n  padding: 5px 11px;\n}\ninput[type=\"search\"] {\n  -webkit-appearance: none !important;\n}\nlabel {\n  font-weight: normal;\n  font-size: 15px;\n  line-height: 2.3;\n}\n.form-control::-moz-placeholder,\n.select2-search input[type=\"text\"]::-moz-placeholder {\n  color: #b2bcc5;\n  opacity: 1;\n}\n.form-control:-ms-input-placeholder,\n.select2-search input[type=\"text\"]:-ms-input-placeholder {\n  color: #b2bcc5;\n}\n.form-control::-webkit-input-placeholder,\n.select2-search input[type=\"text\"]::-webkit-input-placeholder {\n  color: #b2bcc5;\n}\n.form-control,\n.select2-search input[type=\"text\"] {\n  border: 2px solid #bdc3c7;\n  color: #34495e;\n  font-family: \"Lato\", Helvetica, Arial, sans-serif;\n  font-size: 15px;\n  line-height: 1.467;\n  padding: 8px 12px;\n  height: 42px;\n  border-radius: 6px;\n  box-shadow: none;\n  transition: border 0.25s linear, color 0.25s linear, background-color 0.25s linear;\n}\n.form-group.focus .form-control,\n.form-control:focus,\n.form-group.focus .select2-search input[type=\"text\"],\n.select2-search input[type=\"text\"]:focus {\n  border-color: #1abc9c;\n  outline: 0;\n  box-shadow: none;\n}\n.form-control[disabled],\n.form-control[readonly],\nfieldset[disabled] .form-control,\n.select2-search input[type=\"text\"][disabled],\n.select2-search input[type=\"text\"][readonly],\nfieldset[disabled] .select2-search input[type=\"text\"] {\n  background-color: #f4f6f6;\n  border-color: #d5dbdb;\n  color: #d5dbdb;\n  cursor: default;\n  opacity: 0.7;\n  filter: alpha(opacity=70);\n}\n.form-control.flat,\n.select2-search input[type=\"text\"].flat {\n  border-color: transparent;\n}\n.form-control.flat:hover,\n.select2-search input[type=\"text\"].flat:hover {\n  border-color: #bdc3c7;\n}\n.form-control.flat:focus,\n.select2-search input[type=\"text\"].flat:focus {\n  border-color: #1abc9c;\n}\n.input-sm,\n.form-group-sm .form-control,\n.form-group-sm .select2-search input[type=\"text\"],\n.select2-search input[type=\"text\"] {\n  height: 35px;\n  padding: 6px 10px;\n  font-size: 13px;\n  line-height: 1.462;\n  border-radius: 6px;\n}\nselect.input-sm,\nselect.form-group-sm .form-control,\nselect.form-group-sm .select2-search input[type=\"text\"],\nselect.select2-search input[type=\"text\"] {\n  height: 35px;\n  line-height: 35px;\n}\ntextarea.input-sm,\ntextarea.form-group-sm .form-control,\nselect[multiple].input-sm,\nselect[multiple].form-group-sm .form-control,\ntextarea.form-group-sm .select2-search input[type=\"text\"],\nselect[multiple].form-group-sm .select2-search input[type=\"text\"],\ntextarea.select2-search input[type=\"text\"],\nselect[multiple].select2-search input[type=\"text\"] {\n  height: auto;\n}\n.input-lg,\n.form-group-lg .form-control,\n.form-group-lg .select2-search input[type=\"text\"] {\n  height: 45px;\n  padding: 10px 15px;\n  font-size: 17px;\n  line-height: 1.235;\n  border-radius: 6px;\n}\nselect.input-lg,\nselect.form-group-lg .form-control,\nselect.form-group-lg .select2-search input[type=\"text\"] {\n  height: 45px;\n  line-height: 45px;\n}\ntextarea.input-lg,\ntextarea.form-group-lg .form-control,\nselect[multiple].input-lg,\nselect[multiple].form-group-lg .form-control,\ntextarea.form-group-lg .select2-search input[type=\"text\"],\nselect[multiple].form-group-lg .select2-search input[type=\"text\"] {\n  height: auto;\n}\n.input-hg,\n.form-group-hg .form-control,\n.form-horizontal .form-group-hg .form-control,\n.form-group-hg .select2-search input[type=\"text\"],\n.form-horizontal .form-group-hg .select2-search input[type=\"text\"] {\n  height: 53px;\n  padding: 10px 16px;\n  font-size: 22px;\n  line-height: 1.318;\n  border-radius: 6px;\n}\nselect.input-hg,\nselect.form-group-hg .form-control,\nselect.form-group-hg .select2-search input[type=\"text\"] {\n  height: 53px;\n  line-height: 53px;\n}\ntextarea.input-hg,\ntextarea.form-group-hg .form-control,\nselect[multiple].input-hg,\nselect[multiple].form-group-hg .form-control,\ntextarea.form-group-hg .select2-search input[type=\"text\"],\nselect[multiple].form-group-hg .select2-search input[type=\"text\"] {\n  height: auto;\n}\n.form-control-feedback {\n  position: absolute;\n  top: 2px;\n  right: 2px;\n  margin-top: 1px;\n  line-height: 36px;\n  font-size: 17px;\n  color: #b2bcc5;\n  background-color: transparent;\n  padding: 0 12px 0 0;\n  border-radius: 6px;\n  pointer-events: none;\n}\n.input-hg + .form-control-feedback,\n.control-feedback-hg {\n  font-size: 20px;\n  line-height: 48px;\n  padding-right: 16px;\n  width: auto;\n  height: 48px;\n}\n.input-lg + .form-control-feedback,\n.control-feedback-lg {\n  font-size: 18px;\n  line-height: 40px;\n  width: auto;\n  height: 40px;\n  padding-right: 15px;\n}\n.input-sm + .form-control-feedback,\n.control-feedback-sm,\n.select2-search input[type=\"text\"] + .form-control-feedback {\n  line-height: 29px;\n  height: 29px;\n  width: auto;\n  padding-right: 10px;\n}\n.has-success .help-block,\n.has-success .control-label,\n.has-success .radio,\n.has-success .checkbox,\n.has-success .radio-inline,\n.has-success .checkbox-inline {\n  color: #2ecc71;\n}\n.has-success .form-control,\n.has-success .select2-search input[type=\"text\"] {\n  color: #2ecc71;\n  border-color: #2ecc71;\n  box-shadow: none;\n}\n.has-success .form-control::-moz-placeholder,\n.has-success .select2-search input[type=\"text\"]::-moz-placeholder {\n  color: #2ecc71;\n  opacity: 1;\n}\n.has-success .form-control:-ms-input-placeholder,\n.has-success .select2-search input[type=\"text\"]:-ms-input-placeholder {\n  color: #2ecc71;\n}\n.has-success .form-control::-webkit-input-placeholder,\n.has-success .select2-search input[type=\"text\"]::-webkit-input-placeholder {\n  color: #2ecc71;\n}\n.has-success .form-control:focus,\n.has-success .select2-search input[type=\"text\"]:focus {\n  border-color: #2ecc71;\n  box-shadow: none;\n}\n.has-success .input-group-addon {\n  color: #2ecc71;\n  border-color: #2ecc71;\n  background-color: #ffffff;\n}\n.has-success .form-control-feedback {\n  color: #2ecc71;\n}\n.has-warning .help-block,\n.has-warning .control-label,\n.has-warning .radio,\n.has-warning .checkbox,\n.has-warning .radio-inline,\n.has-warning .checkbox-inline {\n  color: #f1c40f;\n}\n.has-warning .form-control,\n.has-warning .select2-search input[type=\"text\"] {\n  color: #f1c40f;\n  border-color: #f1c40f;\n  box-shadow: none;\n}\n.has-warning .form-control::-moz-placeholder,\n.has-warning .select2-search input[type=\"text\"]::-moz-placeholder {\n  color: #f1c40f;\n  opacity: 1;\n}\n.has-warning .form-control:-ms-input-placeholder,\n.has-warning .select2-search input[type=\"text\"]:-ms-input-placeholder {\n  color: #f1c40f;\n}\n.has-warning .form-control::-webkit-input-placeholder,\n.has-warning .select2-search input[type=\"text\"]::-webkit-input-placeholder {\n  color: #f1c40f;\n}\n.has-warning .form-control:focus,\n.has-warning .select2-search input[type=\"text\"]:focus {\n  border-color: #f1c40f;\n  box-shadow: none;\n}\n.has-warning .input-group-addon {\n  color: #f1c40f;\n  border-color: #f1c40f;\n  background-color: #ffffff;\n}\n.has-warning .form-control-feedback {\n  color: #f1c40f;\n}\n.has-error .help-block,\n.has-error .control-label,\n.has-error .radio,\n.has-error .checkbox,\n.has-error .radio-inline,\n.has-error .checkbox-inline {\n  color: #e74c3c;\n}\n.has-error .form-control,\n.has-error .select2-search input[type=\"text\"] {\n  color: #e74c3c;\n  border-color: #e74c3c;\n  box-shadow: none;\n}\n.has-error .form-control::-moz-placeholder,\n.has-error .select2-search input[type=\"text\"]::-moz-placeholder {\n  color: #e74c3c;\n  opacity: 1;\n}\n.has-error .form-control:-ms-input-placeholder,\n.has-error .select2-search input[type=\"text\"]:-ms-input-placeholder {\n  color: #e74c3c;\n}\n.has-error .form-control::-webkit-input-placeholder,\n.has-error .select2-search input[type=\"text\"]::-webkit-input-placeholder {\n  color: #e74c3c;\n}\n.has-error .form-control:focus,\n.has-error .select2-search input[type=\"text\"]:focus {\n  border-color: #e74c3c;\n  box-shadow: none;\n}\n.has-error .input-group-addon {\n  color: #e74c3c;\n  border-color: #e74c3c;\n  background-color: #ffffff;\n}\n.has-error .form-control-feedback {\n  color: #e74c3c;\n}\n.form-control[disabled] + .form-control-feedback,\n.form-control[readonly] + .form-control-feedback,\nfieldset[disabled] .form-control + .form-control-feedback,\n.form-control.disabled + .form-control-feedback,\n.select2-search input[type=\"text\"][disabled] + .form-control-feedback,\n.select2-search input[type=\"text\"][readonly] + .form-control-feedback,\nfieldset[disabled] .select2-search input[type=\"text\"] + .form-control-feedback,\n.select2-search input[type=\"text\"].disabled + .form-control-feedback {\n  cursor: not-allowed;\n  color: #d5dbdb;\n  background-color: transparent;\n  opacity: 0.7;\n  filter: alpha(opacity=70);\n}\n.help-block {\n  font-size: 14px;\n  margin-bottom: 5px;\n  color: #6b7a88;\n}\n.form-group {\n  position: relative;\n  margin-bottom: 20px;\n}\n.form-horizontal .radio,\n.form-horizontal .checkbox,\n.form-horizontal .radio-inline,\n.form-horizontal .checkbox-inline {\n  margin-top: 0;\n  margin-bottom: 0;\n  padding-top: 0;\n}\n@media (min-width: 768px) {\n  .form-horizontal .control-label {\n    padding-top: 3px;\n    padding-bottom: 3px;\n  }\n}\n.form-horizontal .form-group {\n  margin-left: -15px;\n  margin-right: -15px;\n}\n.form-horizontal .form-control-static {\n  padding-top: 6px;\n  padding-bottom: 6px;\n}\n@media (min-width: 768px) {\n  .form-horizontal .form-group-hg .control-label {\n    font-size: 22px;\n    padding-top: 2px;\n    padding-bottom: 0;\n  }\n}\n@media (min-width: 768px) {\n  .form-horizontal .form-group-lg .control-label {\n    font-size: 17px;\n    padding-top: 3px;\n    padding-bottom: 2px;\n  }\n}\n@media (min-width: 768px) {\n  .form-horizontal .form-group-sm .control-label {\n    font-size: 13px;\n    padding-top: 2px;\n    padding-bottom: 2px;\n  }\n}\n.input-group .form-control,\n.input-group .select2-search input[type=\"text\"] {\n  position: static;\n}\n.input-group-hg > .form-control,\n.input-group-hg > .input-group-addon,\n.input-group-hg > .input-group-btn > .btn,\n.input-group-hg > .select2-search input[type=\"text\"] {\n  height: 53px;\n  padding: 10px 16px;\n  font-size: 22px;\n  line-height: 1.318;\n  border-radius: 6px;\n}\nselect.input-group-hg > .form-control,\nselect.input-group-hg > .input-group-addon,\nselect.input-group-hg > .input-group-btn > .btn,\nselect.input-group-hg > .select2-search input[type=\"text\"] {\n  height: 53px;\n  line-height: 53px;\n}\ntextarea.input-group-hg > .form-control,\ntextarea.input-group-hg > .input-group-addon,\ntextarea.input-group-hg > .input-group-btn > .btn,\nselect[multiple].input-group-hg > .form-control,\nselect[multiple].input-group-hg > .input-group-addon,\nselect[multiple].input-group-hg > .input-group-btn > .btn,\ntextarea.input-group-hg > .select2-search input[type=\"text\"],\nselect[multiple].input-group-hg > .select2-search input[type=\"text\"] {\n  height: auto;\n}\n.input-group-lg > .form-control,\n.input-group-lg > .input-group-addon,\n.input-group-lg > .input-group-btn > .btn,\n.input-group-lg > .select2-search input[type=\"text\"] {\n  height: 45px;\n  padding: 10px 15px;\n  font-size: 17px;\n  line-height: 1.235;\n  border-radius: 6px;\n}\nselect.input-group-lg > .form-control,\nselect.input-group-lg > .input-group-addon,\nselect.input-group-lg > .input-group-btn > .btn,\nselect.input-group-lg > .select2-search input[type=\"text\"] {\n  height: 45px;\n  line-height: 45px;\n}\ntextarea.input-group-lg > .form-control,\ntextarea.input-group-lg > .input-group-addon,\ntextarea.input-group-lg > .input-group-btn > .btn,\nselect[multiple].input-group-lg > .form-control,\nselect[multiple].input-group-lg > .input-group-addon,\nselect[multiple].input-group-lg > .input-group-btn > .btn,\ntextarea.input-group-lg > .select2-search input[type=\"text\"],\nselect[multiple].input-group-lg > .select2-search input[type=\"text\"] {\n  height: auto;\n}\n.input-group-sm > .form-control,\n.input-group-sm > .input-group-addon,\n.input-group-sm > .input-group-btn > .btn,\n.input-group-sm > .select2-search input[type=\"text\"] {\n  height: 35px;\n  padding: 6px 10px;\n  font-size: 13px;\n  line-height: 1.462;\n  border-radius: 6px;\n}\nselect.input-group-sm > .form-control,\nselect.input-group-sm > .input-group-addon,\nselect.input-group-sm > .input-group-btn > .btn,\nselect.input-group-sm > .select2-search input[type=\"text\"] {\n  height: 35px;\n  line-height: 35px;\n}\ntextarea.input-group-sm > .form-control,\ntextarea.input-group-sm > .input-group-addon,\ntextarea.input-group-sm > .input-group-btn > .btn,\nselect[multiple].input-group-sm > .form-control,\nselect[multiple].input-group-sm > .input-group-addon,\nselect[multiple].input-group-sm > .input-group-btn > .btn,\ntextarea.input-group-sm > .select2-search input[type=\"text\"],\nselect[multiple].input-group-sm > .select2-search input[type=\"text\"] {\n  height: auto;\n}\n.input-group-addon {\n  padding: 10px 12px;\n  font-size: 15px;\n  color: #ffffff;\n  text-align: center;\n  background-color: #bdc3c7;\n  border: 2px solid #bdc3c7;\n  border-radius: 6px;\n  transition: border 0.25s linear, color 0.25s linear, background-color 0.25s linear;\n}\n.input-group-hg .input-group-addon,\n.input-group-lg .input-group-addon,\n.input-group-sm .input-group-addon {\n  line-height: 1;\n}\n.input-group .form-control:first-child,\n.input-group-addon:first-child,\n.input-group-btn:first-child > .btn,\n.input-group-btn:first-child > .dropdown-toggle,\n.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group .select2-search input[type=\"text\"]:first-child {\n  border-bottom-right-radius: 0;\n  border-top-right-radius: 0;\n}\n.input-group .form-control:last-child,\n.input-group-addon:last-child,\n.input-group-btn:last-child > .btn,\n.input-group-btn:last-child > .dropdown-toggle,\n.input-group-btn:first-child > .btn:not(:first-child),\n.input-group .select2-search input[type=\"text\"]:last-child {\n  border-bottom-left-radius: 0;\n  border-top-left-radius: 0;\n}\n.form-group.focus .input-group-addon,\n.input-group.focus .input-group-addon {\n  background-color: #1abc9c;\n  border-color: #1abc9c;\n}\n.form-group.focus .input-group-btn > .btn-default + .btn-default,\n.input-group.focus .input-group-btn > .btn-default + .btn-default {\n  border-left-color: #16a085;\n}\n.form-group.focus .input-group-btn .btn,\n.input-group.focus .input-group-btn .btn {\n  border-color: #1abc9c;\n  background-color: #ffffff;\n  color: #1abc9c;\n}\n.form-group.focus .input-group-btn .btn-default,\n.input-group.focus .input-group-btn .btn-default {\n  color: #ffffff;\n  background-color: #1abc9c;\n}\n.form-group.focus .input-group-btn .btn-default:hover,\n.input-group.focus .input-group-btn .btn-default:hover,\n.form-group.focus .input-group-btn .btn-default.hover,\n.input-group.focus .input-group-btn .btn-default.hover,\n.form-group.focus .input-group-btn .btn-default:focus,\n.input-group.focus .input-group-btn .btn-default:focus,\n.form-group.focus .input-group-btn .btn-default:active,\n.input-group.focus .input-group-btn .btn-default:active,\n.form-group.focus .input-group-btn .btn-default.active,\n.input-group.focus .input-group-btn .btn-default.active,\n.open > .dropdown-toggle.form-group.focus .input-group-btn .btn-default,\n.open > .dropdown-toggle.input-group.focus .input-group-btn .btn-default {\n  color: #ffffff;\n  background-color: #48c9b0;\n  border-color: #48c9b0;\n}\n.form-group.focus .input-group-btn .btn-default:active,\n.input-group.focus .input-group-btn .btn-default:active,\n.form-group.focus .input-group-btn .btn-default.active,\n.input-group.focus .input-group-btn .btn-default.active,\n.open > .dropdown-toggle.form-group.focus .input-group-btn .btn-default,\n.open > .dropdown-toggle.input-group.focus .input-group-btn .btn-default {\n  background: #16a085;\n  border-color: #16a085;\n}\n.form-group.focus .input-group-btn .btn-default.disabled,\n.input-group.focus .input-group-btn .btn-default.disabled,\n.form-group.focus .input-group-btn .btn-default[disabled],\n.input-group.focus .input-group-btn .btn-default[disabled],\nfieldset[disabled] .form-group.focus .input-group-btn .btn-default,\nfieldset[disabled] .input-group.focus .input-group-btn .btn-default,\n.form-group.focus .input-group-btn .btn-default.disabled:hover,\n.input-group.focus .input-group-btn .btn-default.disabled:hover,\n.form-group.focus .input-group-btn .btn-default[disabled]:hover,\n.input-group.focus .input-group-btn .btn-default[disabled]:hover,\nfieldset[disabled] .form-group.focus .input-group-btn .btn-default:hover,\nfieldset[disabled] .input-group.focus .input-group-btn .btn-default:hover,\n.form-group.focus .input-group-btn .btn-default.disabled.hover,\n.input-group.focus .input-group-btn .btn-default.disabled.hover,\n.form-group.focus .input-group-btn .btn-default[disabled].hover,\n.input-group.focus .input-group-btn .btn-default[disabled].hover,\nfieldset[disabled] .form-group.focus .input-group-btn .btn-default.hover,\nfieldset[disabled] .input-group.focus .input-group-btn .btn-default.hover,\n.form-group.focus .input-group-btn .btn-default.disabled:focus,\n.input-group.focus .input-group-btn .btn-default.disabled:focus,\n.form-group.focus .input-group-btn .btn-default[disabled]:focus,\n.input-group.focus .input-group-btn .btn-default[disabled]:focus,\nfieldset[disabled] .form-group.focus .input-group-btn .btn-default:focus,\nfieldset[disabled] .input-group.focus .input-group-btn .btn-default:focus,\n.form-group.focus .input-group-btn .btn-default.disabled:active,\n.input-group.focus .input-group-btn .btn-default.disabled:active,\n.form-group.focus .input-group-btn .btn-default[disabled]:active,\n.input-group.focus .input-group-btn .btn-default[disabled]:active,\nfieldset[disabled] .form-group.focus .input-group-btn .btn-default:active,\nfieldset[disabled] .input-group.focus .input-group-btn .btn-default:active,\n.form-group.focus .input-group-btn .btn-default.disabled.active,\n.input-group.focus .input-group-btn .btn-default.disabled.active,\n.form-group.focus .input-group-btn .btn-default[disabled].active,\n.input-group.focus .input-group-btn .btn-default[disabled].active,\nfieldset[disabled] .form-group.focus .input-group-btn .btn-default.active,\nfieldset[disabled] .input-group.focus .input-group-btn .btn-default.active {\n  background-color: #bdc3c7;\n  border-color: #1abc9c;\n}\n.form-group.focus .input-group-btn .btn-default .badge,\n.input-group.focus .input-group-btn .btn-default .badge {\n  color: #1abc9c;\n  background-color: #ffffff;\n}\n.input-group-btn .btn {\n  background-color: #ffffff;\n  border: 2px solid #bdc3c7;\n  color: #bdc3c7;\n  line-height: 18px;\n  height: 42px;\n}\n.input-group-btn .btn-default {\n  color: #ffffff;\n  background-color: #bdc3c7;\n}\n.input-group-btn .btn-default:hover,\n.input-group-btn .btn-default.hover,\n.input-group-btn .btn-default:focus,\n.input-group-btn .btn-default:active,\n.input-group-btn .btn-default.active,\n.open > .dropdown-toggle.input-group-btn .btn-default {\n  color: #ffffff;\n  background-color: #cacfd2;\n  border-color: #cacfd2;\n}\n.input-group-btn .btn-default:active,\n.input-group-btn .btn-default.active,\n.open > .dropdown-toggle.input-group-btn .btn-default {\n  background: #a1a6a9;\n  border-color: #a1a6a9;\n}\n.input-group-btn .btn-default.disabled,\n.input-group-btn .btn-default[disabled],\nfieldset[disabled] .input-group-btn .btn-default,\n.input-group-btn .btn-default.disabled:hover,\n.input-group-btn .btn-default[disabled]:hover,\nfieldset[disabled] .input-group-btn .btn-default:hover,\n.input-group-btn .btn-default.disabled.hover,\n.input-group-btn .btn-default[disabled].hover,\nfieldset[disabled] .input-group-btn .btn-default.hover,\n.input-group-btn .btn-default.disabled:focus,\n.input-group-btn .btn-default[disabled]:focus,\nfieldset[disabled] .input-group-btn .btn-default:focus,\n.input-group-btn .btn-default.disabled:active,\n.input-group-btn .btn-default[disabled]:active,\nfieldset[disabled] .input-group-btn .btn-default:active,\n.input-group-btn .btn-default.disabled.active,\n.input-group-btn .btn-default[disabled].active,\nfieldset[disabled] .input-group-btn .btn-default.active {\n  background-color: #bdc3c7;\n  border-color: #bdc3c7;\n}\n.input-group-btn .btn-default .badge {\n  color: #bdc3c7;\n  background-color: #ffffff;\n}\n.input-group-hg .input-group-btn .btn {\n  line-height: 31px;\n}\n.input-group-lg .input-group-btn .btn {\n  line-height: 21px;\n}\n.input-group-sm .input-group-btn .btn {\n  line-height: 19px;\n}\n.input-group-btn:first-child > .btn {\n  border-right-width: 0;\n  margin-right: -3px;\n}\n.input-group-btn:last-child > .btn {\n  border-left-width: 0;\n  margin-left: -3px;\n}\n.input-group-btn > .btn-default + .btn-default {\n  border-left: 2px solid #bdc3c7;\n}\n.input-group-btn > .btn:first-child + .btn .caret {\n  margin-left: 0;\n}\n.input-group-rounded .input-group-btn + .form-control,\n.input-group-rounded .input-group-btn:last-child .btn,\n.input-group-rounded .input-group-btn + .select2-search input[type=\"text\"] {\n  border-bottom-right-radius: 20px;\n  border-top-right-radius: 20px;\n}\n.input-group-hg.input-group-rounded .input-group-btn + .form-control,\n.input-group-hg.input-group-rounded .input-group-btn:last-child .btn,\n.input-group-hg.input-group-rounded .input-group-btn + .select2-search input[type=\"text\"] {\n  border-bottom-right-radius: 27px;\n  border-top-right-radius: 27px;\n}\n.input-group-lg.input-group-rounded .input-group-btn + .form-control,\n.input-group-lg.input-group-rounded .input-group-btn:last-child .btn,\n.input-group-lg.input-group-rounded .input-group-btn + .select2-search input[type=\"text\"] {\n  border-bottom-right-radius: 25px;\n  border-top-right-radius: 25px;\n}\n.input-group-rounded .form-control:first-child,\n.input-group-rounded .input-group-btn:first-child .btn,\n.input-group-rounded .select2-search input[type=\"text\"]:first-child {\n  border-bottom-left-radius: 20px;\n  border-top-left-radius: 20px;\n}\n.input-group-hg.input-group-rounded .form-control:first-child,\n.input-group-hg.input-group-rounded .input-group-btn:first-child .btn,\n.input-group-hg.input-group-rounded .select2-search input[type=\"text\"]:first-child {\n  border-bottom-left-radius: 27px;\n  border-top-left-radius: 27px;\n}\n.input-group-lg.input-group-rounded .form-control:first-child,\n.input-group-lg.input-group-rounded .input-group-btn:first-child .btn,\n.input-group-lg.input-group-rounded .select2-search input[type=\"text\"]:first-child {\n  border-bottom-left-radius: 25px;\n  border-top-left-radius: 25px;\n}\n.input-group-rounded .input-group-btn + .form-control,\n.input-group-rounded .input-group-btn + .select2-search input[type=\"text\"] {\n  padding-left: 0;\n}\n.checkbox,\n.radio {\n  margin-bottom: 12px;\n  padding-left: 32px;\n  position: relative;\n  transition: color .25s linear;\n  font-size: 14px;\n  line-height: 1.5;\n}\n.checkbox .icons,\n.radio .icons {\n  color: #bdc3c7;\n  display: block;\n  height: 20px;\n  top: 0;\n  left: 0;\n  position: absolute;\n  width: 20px;\n  text-align: center;\n  line-height: 20px;\n  font-size: 20px;\n  cursor: pointer;\n}\n.checkbox .icons .icon-checked,\n.radio .icons .icon-checked {\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.checkbox .icon-checked,\n.radio .icon-checked,\n.checkbox .icon-unchecked,\n.radio .icon-unchecked {\n  display: inline-table;\n  position: absolute;\n  left: 0;\n  top: 0;\n  background-color: transparent;\n  margin: 0;\n  opacity: 1;\n  filter: none;\n  transition: color .25s linear;\n}\n.checkbox .icon-checked:before,\n.radio .icon-checked:before,\n.checkbox .icon-unchecked:before,\n.radio .icon-unchecked:before {\n  font-family: 'Flat-UI-Icons';\n  speak: none;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.checkbox .icon-checked:before {\n  content: \"\\e60e\";\n}\n.checkbox .icon-unchecked:before {\n  content: \"\\e60d\";\n}\n.radio .icon-checked:before {\n  content: \"\\e60c\";\n}\n.radio .icon-unchecked:before {\n  content: \"\\e60b\";\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox,\n.radio input[type=\"checkbox\"].custom-checkbox,\n.checkbox input[type=\"radio\"].custom-radio,\n.radio input[type=\"radio\"].custom-radio {\n  outline: none !important;\n  opacity: 0;\n  position: absolute;\n  margin: 0;\n  padding: 0;\n  left: 0;\n  top: 0;\n  width: 20px;\n  height: 20px;\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:hover:not(.nohover):not(:disabled) + .icons .icon-unchecked,\n.radio input[type=\"checkbox\"].custom-checkbox:hover:not(.nohover):not(:disabled) + .icons .icon-unchecked,\n.checkbox input[type=\"radio\"].custom-radio:hover:not(.nohover):not(:disabled) + .icons .icon-unchecked,\n.radio input[type=\"radio\"].custom-radio:hover:not(.nohover):not(:disabled) + .icons .icon-unchecked {\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:hover:not(.nohover):not(:disabled) + .icons .icon-checked,\n.radio input[type=\"checkbox\"].custom-checkbox:hover:not(.nohover):not(:disabled) + .icons .icon-checked,\n.checkbox input[type=\"radio\"].custom-radio:hover:not(.nohover):not(:disabled) + .icons .icon-checked,\n.radio input[type=\"radio\"].custom-radio:hover:not(.nohover):not(:disabled) + .icons .icon-checked {\n  opacity: 1;\n  filter: none;\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:checked + .icons,\n.radio input[type=\"checkbox\"].custom-checkbox:checked + .icons,\n.checkbox input[type=\"radio\"].custom-radio:checked + .icons,\n.radio input[type=\"radio\"].custom-radio:checked + .icons {\n  color: #1abc9c;\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:checked + .icons .icon-unchecked,\n.radio input[type=\"checkbox\"].custom-checkbox:checked + .icons .icon-unchecked,\n.checkbox input[type=\"radio\"].custom-radio:checked + .icons .icon-unchecked,\n.radio input[type=\"radio\"].custom-radio:checked + .icons .icon-unchecked {\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:checked + .icons .icon-checked,\n.radio input[type=\"checkbox\"].custom-checkbox:checked + .icons .icon-checked,\n.checkbox input[type=\"radio\"].custom-radio:checked + .icons .icon-checked,\n.radio input[type=\"radio\"].custom-radio:checked + .icons .icon-checked {\n  opacity: 1;\n  filter: none;\n  color: #1abc9c;\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:disabled + .icons,\n.radio input[type=\"checkbox\"].custom-checkbox:disabled + .icons,\n.checkbox input[type=\"radio\"].custom-radio:disabled + .icons,\n.radio input[type=\"radio\"].custom-radio:disabled + .icons {\n  cursor: default;\n  color: #e6e8ea;\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:disabled + .icons .icon-unchecked,\n.radio input[type=\"checkbox\"].custom-checkbox:disabled + .icons .icon-unchecked,\n.checkbox input[type=\"radio\"].custom-radio:disabled + .icons .icon-unchecked,\n.radio input[type=\"radio\"].custom-radio:disabled + .icons .icon-unchecked {\n  opacity: 1;\n  filter: none;\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:disabled + .icons .icon-checked,\n.radio input[type=\"checkbox\"].custom-checkbox:disabled + .icons .icon-checked,\n.checkbox input[type=\"radio\"].custom-radio:disabled + .icons .icon-checked,\n.radio input[type=\"radio\"].custom-radio:disabled + .icons .icon-checked {\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:disabled:checked + .icons,\n.radio input[type=\"checkbox\"].custom-checkbox:disabled:checked + .icons,\n.checkbox input[type=\"radio\"].custom-radio:disabled:checked + .icons,\n.radio input[type=\"radio\"].custom-radio:disabled:checked + .icons {\n  color: #e6e8ea;\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:disabled:checked + .icons .icon-unchecked,\n.radio input[type=\"checkbox\"].custom-checkbox:disabled:checked + .icons .icon-unchecked,\n.checkbox input[type=\"radio\"].custom-radio:disabled:checked + .icons .icon-unchecked,\n.radio input[type=\"radio\"].custom-radio:disabled:checked + .icons .icon-unchecked {\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:disabled:checked + .icons .icon-checked,\n.radio input[type=\"checkbox\"].custom-checkbox:disabled:checked + .icons .icon-checked,\n.checkbox input[type=\"radio\"].custom-radio:disabled:checked + .icons .icon-checked,\n.radio input[type=\"radio\"].custom-radio:disabled:checked + .icons .icon-checked {\n  opacity: 1;\n  filter: none;\n  color: #e6e8ea;\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:indeterminate + .icons,\n.radio input[type=\"checkbox\"].custom-checkbox:indeterminate + .icons,\n.checkbox input[type=\"radio\"].custom-radio:indeterminate + .icons,\n.radio input[type=\"radio\"].custom-radio:indeterminate + .icons {\n  color: #bdc3c7;\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:indeterminate + .icons .icon-unchecked,\n.radio input[type=\"checkbox\"].custom-checkbox:indeterminate + .icons .icon-unchecked,\n.checkbox input[type=\"radio\"].custom-radio:indeterminate + .icons .icon-unchecked,\n.radio input[type=\"radio\"].custom-radio:indeterminate + .icons .icon-unchecked {\n  opacity: 1;\n  filter: none;\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:indeterminate + .icons .icon-checked,\n.radio input[type=\"checkbox\"].custom-checkbox:indeterminate + .icons .icon-checked,\n.checkbox input[type=\"radio\"].custom-radio:indeterminate + .icons .icon-checked,\n.radio input[type=\"radio\"].custom-radio:indeterminate + .icons .icon-checked {\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.checkbox input[type=\"checkbox\"].custom-checkbox:indeterminate + .icons:before,\n.radio input[type=\"checkbox\"].custom-checkbox:indeterminate + .icons:before,\n.checkbox input[type=\"radio\"].custom-radio:indeterminate + .icons:before,\n.radio input[type=\"radio\"].custom-radio:indeterminate + .icons:before {\n  content: \"\\2013\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  line-height: 20px;\n  width: 20px;\n  text-align: center;\n  color: #ffffff;\n  font-size: 22px;\n  z-index: 10;\n}\n.checkbox.primary input[type=\"checkbox\"].custom-checkbox + .icons,\n.radio.primary input[type=\"checkbox\"].custom-checkbox + .icons,\n.checkbox.primary input[type=\"radio\"].custom-radio + .icons,\n.radio.primary input[type=\"radio\"].custom-radio + .icons {\n  color: #34495e;\n}\n.checkbox.primary input[type=\"checkbox\"].custom-checkbox:checked + .icons,\n.radio.primary input[type=\"checkbox\"].custom-checkbox:checked + .icons,\n.checkbox.primary input[type=\"radio\"].custom-radio:checked + .icons,\n.radio.primary input[type=\"radio\"].custom-radio:checked + .icons {\n  color: #1abc9c;\n}\n.checkbox.primary input[type=\"checkbox\"].custom-checkbox:disabled + .icons,\n.radio.primary input[type=\"checkbox\"].custom-checkbox:disabled + .icons,\n.checkbox.primary input[type=\"radio\"].custom-radio:disabled + .icons,\n.radio.primary input[type=\"radio\"].custom-radio:disabled + .icons {\n  cursor: default;\n  color: #bdc3c7;\n}\n.checkbox.primary input[type=\"checkbox\"].custom-checkbox:disabled + .icons.checked,\n.radio.primary input[type=\"checkbox\"].custom-checkbox:disabled + .icons.checked,\n.checkbox.primary input[type=\"radio\"].custom-radio:disabled + .icons.checked,\n.radio.primary input[type=\"radio\"].custom-radio:disabled + .icons.checked {\n  color: #bdc3c7;\n}\n.checkbox.primary input[type=\"checkbox\"].custom-checkbox:indeterminate + .icons,\n.radio.primary input[type=\"checkbox\"].custom-checkbox:indeterminate + .icons,\n.checkbox.primary input[type=\"radio\"].custom-radio:indeterminate + .icons,\n.radio.primary input[type=\"radio\"].custom-radio:indeterminate + .icons {\n  color: #34495e;\n}\n.input-group-addon .radio,\n.input-group-addon .checkbox {\n  margin: -2px 0;\n  padding-left: 20px;\n}\n.input-group-addon .radio .icons,\n.input-group-addon .checkbox .icons {\n  color: #e6e8ea;\n}\n.input-group-addon .radio input[type=\"checkbox\"].custom-checkbox:checked + .icons,\n.input-group-addon .checkbox input[type=\"checkbox\"].custom-checkbox:checked + .icons,\n.input-group-addon .radio input[type=\"radio\"].custom-radio:checked + .icons,\n.input-group-addon .checkbox input[type=\"radio\"].custom-radio:checked + .icons {\n  color: #ffffff;\n}\n.input-group-addon .radio input[type=\"checkbox\"].custom-checkbox:checked + .icons .icon-checked,\n.input-group-addon .checkbox input[type=\"checkbox\"].custom-checkbox:checked + .icons .icon-checked,\n.input-group-addon .radio input[type=\"radio\"].custom-radio:checked + .icons .icon-checked,\n.input-group-addon .checkbox input[type=\"radio\"].custom-radio:checked + .icons .icon-checked {\n  color: #ffffff;\n}\n.input-group-addon .radio input[type=\"checkbox\"].custom-checkbox:disabled + .icons,\n.input-group-addon .checkbox input[type=\"checkbox\"].custom-checkbox:disabled + .icons,\n.input-group-addon .radio input[type=\"radio\"].custom-radio:disabled + .icons,\n.input-group-addon .checkbox input[type=\"radio\"].custom-radio:disabled + .icons {\n  color: rgba(230, 232, 234, 0.6);\n}\n.input-group-addon .radio input[type=\"checkbox\"].custom-checkbox:disabled:checked + .icons,\n.input-group-addon .checkbox input[type=\"checkbox\"].custom-checkbox:disabled:checked + .icons,\n.input-group-addon .radio input[type=\"radio\"].custom-radio:disabled:checked + .icons,\n.input-group-addon .checkbox input[type=\"radio\"].custom-radio:disabled:checked + .icons {\n  color: rgba(230, 232, 234, 0.6);\n}\n.input-group-addon .radio input[type=\"checkbox\"].custom-checkbox:disabled:checked + .icons .icon-checked,\n.input-group-addon .checkbox input[type=\"checkbox\"].custom-checkbox:disabled:checked + .icons .icon-checked,\n.input-group-addon .radio input[type=\"radio\"].custom-radio:disabled:checked + .icons .icon-checked,\n.input-group-addon .checkbox input[type=\"radio\"].custom-radio:disabled:checked + .icons .icon-checked {\n  color: rgba(230, 232, 234, 0.6);\n}\n.radio + .radio,\n.checkbox + .checkbox {\n  margin-top: 10px;\n}\n.form-inline .checkbox,\n.form-inline .radio {\n  padding-left: 32px;\n}\n.bootstrap-tagsinput {\n  background-color: #ffffff;\n  border: 2px solid #ebedef;\n  border-radius: 6px;\n  margin-bottom: 18px;\n  padding: 6px 1px 1px 6px;\n  text-align: left;\n  font-size: 0;\n}\n.bootstrap-tagsinput .tag {\n  border-radius: 4px;\n  background-color: #ebedef;\n  color: #7b8996;\n  font-size: 13px;\n  cursor: pointer;\n  display: inline-block;\n  position: relative;\n  vertical-align: middle;\n  overflow: hidden;\n  margin: 0 7px 7px 0;\n  line-height: 15px;\n  height: 27px;\n  padding: 6px 21px;\n  transition: .25s linear;\n}\n.bootstrap-tagsinput .tag > span {\n  color: #ffffff;\n  cursor: pointer;\n  font-size: 12px;\n  position: absolute;\n  right: 0;\n  text-align: right;\n  text-decoration: none;\n  top: 0;\n  width: 100%;\n  bottom: 0;\n  padding: 0 10px 0 0;\n  z-index: 2;\n  opacity: 0;\n  filter: alpha(opacity=0);\n  transition: opacity .25s linear;\n}\n.bootstrap-tagsinput .tag > span:after {\n  content: \"\\e609\";\n  font-family: \"Flat-UI-Icons\";\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  line-height: 27px;\n}\n.bootstrap-tagsinput .tag:hover {\n  background-color: #16a085;\n  color: #ffffff;\n  padding-right: 28px;\n  padding-left: 14px;\n}\n.bootstrap-tagsinput .tag:hover > span {\n  opacity: 1;\n  filter: none;\n}\n.bootstrap-tagsinput input[type=\"text\"] {\n  font-size: 14px;\n  border: none;\n  box-shadow: none;\n  outline: none;\n  background-color: transparent;\n  padding: 0;\n  margin: 0;\n  width: auto !important;\n  max-width: inherit;\n  min-width: 80px;\n  vertical-align: top;\n  height: 29px;\n  color: #34495e;\n}\n.bootstrap-tagsinput input[type=\"text\"]:first-child {\n  height: 23px;\n  margin: 3px 0 8px;\n}\n.tags_clear {\n  clear: both;\n  width: 100%;\n  height: 0;\n}\n.not_valid {\n  background: #fbd8db !important;\n  color: #90111a !important;\n  margin-left: 5px !important;\n}\n.tagsinput-primary {\n  margin-bottom: 18px;\n}\n.tagsinput-primary .bootstrap-tagsinput {\n  border-color: #1abc9c;\n  margin-bottom: 0;\n}\n.tagsinput-primary .tag {\n  background-color: #1abc9c;\n  color: #ffffff;\n}\n.tagsinput-primary .tag:hover {\n  background-color: #16a085;\n  color: #ffffff;\n}\n.bootstrap-tagsinput .twitter-typeahead {\n  width: auto;\n  vertical-align: top;\n}\n.bootstrap-tagsinput .twitter-typeahead .tt-input {\n  min-width: 200px;\n}\n.bootstrap-tagsinput .twitter-typeahead .tt-dropdown-menu {\n  width: auto;\n  min-width: 120px;\n  margin-top: 11px;\n}\n.twitter-typeahead {\n  width: 100%;\n}\n.twitter-typeahead .tt-dropdown-menu {\n  width: 100%;\n  margin-top: 5px;\n  border: 2px solid #1abc9c;\n  padding: 5px 0;\n  background-color: #ffffff;\n  border-radius: 6px;\n}\n.twitter-typeahead .tt-suggestion p {\n  padding: 6px 14px;\n  font-size: 14px;\n  line-height: 1.429;\n  margin: 0;\n}\n.twitter-typeahead .tt-suggestion:first-child p,\n.twitter-typeahead .tt-suggestion:last-child p {\n  padding: 6px 14px;\n}\n.twitter-typeahead .tt-suggestion.tt-is-under-cursor,\n.twitter-typeahead .tt-suggestion.tt-cursor {\n  cursor: pointer;\n  color: #fff;\n  background-color: #16a085;\n}\n.progress {\n  background: #ebedef;\n  border-radius: 32px;\n  height: 12px;\n  box-shadow: none;\n}\n.progress-bar {\n  background: #1abc9c;\n  line-height: 12px;\n  box-shadow: none;\n}\n.progress-bar-success {\n  background-color: #2ecc71;\n}\n.progress-bar-warning {\n  background-color: #f1c40f;\n}\n.progress-bar-danger {\n  background-color: #e74c3c;\n}\n.progress-bar-info {\n  background-color: #3498db;\n}\n.ui-slider {\n  background: #ebedef;\n  border-radius: 32px;\n  height: 12px;\n  box-shadow: none;\n  margin-bottom: 20px;\n  position: relative;\n  cursor: pointer;\n}\n.ui-slider-handle {\n  background-color: #16a085;\n  border-radius: 50%;\n  cursor: pointer;\n  height: 18px;\n  position: absolute;\n  width: 18px;\n  z-index: 2;\n  transition: background .25s;\n}\n.ui-slider-handle:hover,\n.ui-slider-handle:focus {\n  background-color: #48c9b0;\n  outline: none;\n}\n.ui-slider-handle:active {\n  background-color: #16a085;\n}\n.ui-slider-range {\n  background-color: #1abc9c;\n  display: block;\n  height: 100%;\n  position: absolute;\n  z-index: 1;\n}\n.ui-slider-segment {\n  background-color: #d9dbdd;\n  border-radius: 50%;\n  height: 6px;\n  width: 6px;\n}\n.ui-slider-value {\n  float: right;\n  font-size: 13px;\n  margin-top: 12px;\n}\n.ui-slider-value.first {\n  clear: left;\n  float: left;\n}\n.ui-slider-horizontal .ui-slider-handle {\n  margin-left: -9px;\n  top: -3px;\n}\n.ui-slider-horizontal .ui-slider-handle[style*=\"100\"] {\n  margin-left: -15px;\n}\n.ui-slider-horizontal .ui-slider-range {\n  border-radius: 30px 0 0 30px;\n}\n.ui-slider-horizontal .ui-slider-segment {\n  float: left;\n  margin: 3px -6px 0 0;\n}\n.ui-slider-vertical {\n  width: 12px;\n}\n.ui-slider-vertical .ui-slider-handle {\n  margin-left: -3px;\n  margin-bottom: -11px;\n  top: auto;\n}\n.ui-slider-vertical .ui-slider-range {\n  width: 100%;\n  bottom: 0;\n  border-radius: 0 0 30px 30px;\n}\n.ui-slider-vertical .ui-slider-segment {\n  position: absolute;\n  right: 3px;\n}\n.pager {\n  background-color: #34495e;\n  border-radius: 6px;\n  color: #ffffff;\n  font-size: 16px;\n  font-weight: 700;\n  display: inline-block;\n}\n.pager li:first-child > a,\n.pager li:first-child > span {\n  border-left: none;\n  border-radius: 6px 0 0 6px;\n}\n.pager li > a,\n.pager li > span {\n  background: none;\n  border: none;\n  border-left: 2px solid #2c3e50;\n  color: #ffffff;\n  padding: 9px 15px 10px;\n  text-decoration: none;\n  white-space: nowrap;\n  border-radius: 0 6px 6px 0;\n  line-height: 1.313;\n}\n.pager li > a:hover,\n.pager li > span:hover,\n.pager li > a:focus,\n.pager li > span:focus {\n  background-color: #2c3e50;\n}\n.pager li > a:active,\n.pager li > span:active {\n  background-color: #2c3e50;\n}\n.pager li > a [class*=\"fui-\"] + span,\n.pager li > span [class*=\"fui-\"] + span {\n  margin-left: 8px;\n}\n.pager li > a span + [class*=\"fui-\"],\n.pager li > span span + [class*=\"fui-\"] {\n  margin-left: 8px;\n}\n.pagination {\n  position: relative;\n  display: block;\n  background: #d6dbdf;\n  color: #ffffff;\n  padding: 0;\n  display: inline-block;\n  border-radius: 6px;\n  word-spacing: -0.5px;\n}\n@media (min-width: 768px) {\n  .pagination {\n    display: inline-block;\n  }\n}\n@media (max-width: 767px) {\n  .pagination {\n    height: 41px;\n    padding: 0 55px 0 52px;\n    overflow: auto;\n    white-space: nowrap;\n    border-radius: 6px;\n  }\n}\n.pagination li {\n  display: inline-block;\n  margin-right: -2px;\n  vertical-align: middle;\n  word-spacing: normal;\n}\n.pagination li a {\n  position: static;\n}\n.pagination li.active > a,\n.pagination li.active > span {\n  background-color: #1abc9c;\n  color: #ffffff;\n  border-color: #dfe2e5;\n}\n.pagination li.active > a,\n.pagination li.active > span,\n.pagination li.active > a:hover,\n.pagination li.active > span:hover,\n.pagination li.active > a:focus,\n.pagination li.active > span:focus {\n  background-color: #1abc9c;\n  color: #ffffff;\n  border-color: #dfe2e5;\n}\n.pagination li.active.previous > a,\n.pagination li.active.next > a,\n.pagination li.active.previous > span,\n.pagination li.active.next > span {\n  margin: 0;\n}\n.pagination li.active.previous > a,\n.pagination li.active.next > a,\n.pagination li.active.previous > span,\n.pagination li.active.next > span,\n.pagination li.active.previous > a:hover,\n.pagination li.active.next > a:hover,\n.pagination li.active.previous > span:hover,\n.pagination li.active.next > span:hover,\n.pagination li.active.previous > a:focus,\n.pagination li.active.next > a:focus,\n.pagination li.active.previous > span:focus,\n.pagination li.active.next > span:focus {\n  background-color: #1abc9c;\n  color: #ffffff;\n}\n.pagination li:first-child > a,\n.pagination li:first-child > span {\n  border-radius: 6px 0 0 6px;\n  border-left: none;\n}\n.pagination li:first-child.previous + li > a,\n.pagination li:first-child.previous + li > span {\n  border-left-width: 0;\n}\n.pagination li:last-child {\n  margin-right: 0;\n}\n.pagination li:last-child > a,\n.pagination li:last-child > span,\n.pagination li:last-child > a:hover,\n.pagination li:last-child > span:hover,\n.pagination li:last-child > a:focus,\n.pagination li:last-child > span:focus {\n  border-radius: 0 6px 6px 0;\n}\n.pagination li.previous > a,\n.pagination li.next > a,\n.pagination li.previous > span,\n.pagination li.next > span {\n  border-right: 2px solid #e4e7ea;\n  font-size: 16px;\n  min-width: auto;\n  padding: 12px 17px;\n  background-color: transparent;\n}\n.pagination li.next > a,\n.pagination li.next > span {\n  border-right: none;\n}\n.pagination li.disabled > a,\n.pagination li.disabled > span {\n  color: #ffffff;\n  background-color: rgba(255, 255, 255, 0.3);\n  border-right-color: #dfe2e5;\n  cursor: not-allowed;\n}\n.pagination li.disabled > a:hover,\n.pagination li.disabled > span:hover,\n.pagination li.disabled > a:focus,\n.pagination li.disabled > span:focus,\n.pagination li.disabled > a:active,\n.pagination li.disabled > span:active {\n  background-color: rgba(255, 255, 255, 0.4);\n  color: #ffffff;\n}\n@media (max-width: 767px) {\n  .pagination li.next,\n  .pagination li.previous {\n    background-color: #d6dbdf;\n    position: absolute;\n    right: 0;\n    top: 0;\n    z-index: 10;\n    border-radius: 0 6px 6px 0;\n  }\n  .pagination li.previous {\n    left: 0;\n    right: auto;\n    border-radius: 6px 0 0 6px;\n  }\n}\n.pagination li > a,\n.pagination li > span {\n  display: inline-block;\n  background: transparent;\n  border: none;\n  border-left: 2px solid #e4e7ea;\n  color: #ffffff;\n  font-size: 14px;\n  line-height: 16px;\n  min-height: 41px;\n  min-width: 41px;\n  outline: none;\n  padding: 12px 10px;\n  text-align: center;\n  transition: 0.25s ease-out;\n}\n.pagination li > a:hover,\n.pagination li > span:hover,\n.pagination li > a:focus,\n.pagination li > span:focus {\n  background-color: #1abc9c;\n  color: #ffffff;\n}\n.pagination li > a:active,\n.pagination li > span:active {\n  background-color: #1abc9c;\n  color: #ffffff;\n}\n.pagination > .btn.previous,\n.pagination > .btn.next {\n  margin-right: 8px;\n  font-size: 14px;\n  line-height: 1.429;\n  padding-left: 23px;\n  padding-right: 23px;\n}\n.pagination > .btn.previous [class*=\"fui-\"],\n.pagination > .btn.next [class*=\"fui-\"] {\n  font-size: 16px;\n  margin-left: -2px;\n  margin-top: -2px;\n}\n.pagination > .btn.next {\n  margin-left: 8px;\n  margin-right: 0;\n}\n.pagination > .btn.next [class*=\"fui-\"] {\n  margin-right: -2px;\n  margin-left: 4px;\n}\n@media (max-width: 767px) {\n  .pagination > .btn {\n    display: block;\n    margin: 0;\n    width: 50%;\n  }\n  .pagination > .btn:first-child {\n    border-bottom: 2px solid #dfe2e5;\n    border-radius: 6px 0 0;\n  }\n  .pagination > .btn:first-child.btn-primary {\n    border-bottom-color: #48c9b0;\n  }\n  .pagination > .btn:first-child.btn-danger {\n    border-bottom-color: #ec7063;\n  }\n  .pagination > .btn:first-child.btn-warning {\n    border-bottom-color: #f4d03f;\n  }\n  .pagination > .btn:first-child.btn-success {\n    border-bottom-color: #58d68d;\n  }\n  .pagination > .btn:first-child.btn-info {\n    border-bottom-color: #5dade2;\n  }\n  .pagination > .btn:first-child.btn-inverse {\n    border-bottom-color: #5d6d7e;\n  }\n  .pagination > .btn:first-child > [class*=\"fui\"] {\n    margin-left: -20px;\n  }\n  .pagination > .btn + ul {\n    padding: 0;\n    text-align: center;\n    border-radius: 0 0 6px 6px;\n  }\n  .pagination > .btn + ul + .btn {\n    border-bottom: 2px solid #dfe2e5;\n    position: absolute;\n    right: 0;\n    top: 0;\n    border-radius: 0 6px 0 0;\n  }\n  .pagination > .btn + ul + .btn.btn-primary {\n    border-bottom-color: #48c9b0;\n  }\n  .pagination > .btn + ul + .btn.btn-danger {\n    border-bottom-color: #ec7063;\n  }\n  .pagination > .btn + ul + .btn.btn-warning {\n    border-bottom-color: #f4d03f;\n  }\n  .pagination > .btn + ul + .btn.btn-success {\n    border-bottom-color: #58d68d;\n  }\n  .pagination > .btn + ul + .btn.btn-info {\n    border-bottom-color: #5dade2;\n  }\n  .pagination > .btn + ul + .btn.btn-inverse {\n    border-bottom-color: #5d6d7e;\n  }\n  .pagination > .btn + ul + .btn > [class*=\"fui\"] {\n    margin-right: -20px;\n  }\n  .pagination ul {\n    display: block;\n  }\n  .pagination ul > li > a {\n    border-radius: 0;\n  }\n}\n.pagination-danger {\n  background-color: #e74c3c;\n}\n.pagination-danger li.previous > a {\n  border-right-color: #ef897e;\n}\n.pagination-danger li > a,\n.pagination-danger li > span {\n  border-left-color: #ef897e;\n}\n.pagination-danger li > a:hover,\n.pagination-danger li > span:hover,\n.pagination-danger li > a:focus,\n.pagination-danger li > span:focus {\n  border-left-color: #ef897e;\n  background-color: #ec7063;\n}\n.pagination-danger li > a:active,\n.pagination-danger li > span:active {\n  background-color: #c44133;\n}\n.pagination-danger li.active > a,\n.pagination-danger li.active > span {\n  border-left-color: #ef897e;\n  background-color: #c44133;\n}\n.pagination-danger li.active > a:hover,\n.pagination-danger li.active > span:hover,\n.pagination-danger li.active > a:focus,\n.pagination-danger li.active > span:focus {\n  border-left-color: #ef897e;\n  background-color: #ec7063;\n}\n.pagination-success {\n  background-color: #2ecc71;\n}\n.pagination-success li.previous > a {\n  border-right-color: #75dda1;\n}\n.pagination-success li > a,\n.pagination-success li > span {\n  border-left-color: #75dda1;\n}\n.pagination-success li > a:hover,\n.pagination-success li > span:hover,\n.pagination-success li > a:focus,\n.pagination-success li > span:focus {\n  border-left-color: #75dda1;\n  background-color: #58d68d;\n}\n.pagination-success li > a:active,\n.pagination-success li > span:active {\n  background-color: #27ad60;\n}\n.pagination-success li.active > a,\n.pagination-success li.active > span {\n  border-left-color: #75dda1;\n  background-color: #27ad60;\n}\n.pagination-success li.active > a:hover,\n.pagination-success li.active > span:hover,\n.pagination-success li.active > a:focus,\n.pagination-success li.active > span:focus {\n  border-left-color: #75dda1;\n  background-color: #58d68d;\n}\n.pagination-warning {\n  background-color: #f1c40f;\n}\n.pagination-warning li.previous > a {\n  border-right-color: #f6d861;\n}\n.pagination-warning li > a,\n.pagination-warning li > span {\n  border-left-color: #f6d861;\n}\n.pagination-warning li > a:hover,\n.pagination-warning li > span:hover,\n.pagination-warning li > a:focus,\n.pagination-warning li > span:focus {\n  border-left-color: #f6d861;\n  background-color: #f4d313;\n}\n.pagination-warning li > a:active,\n.pagination-warning li > span:active {\n  background-color: #cda70d;\n}\n.pagination-warning li.active > a,\n.pagination-warning li.active > span {\n  border-left-color: #f6d861;\n  background-color: #cda70d;\n}\n.pagination-warning li.active > a:hover,\n.pagination-warning li.active > span:hover,\n.pagination-warning li.active > a:focus,\n.pagination-warning li.active > span:focus {\n  border-left-color: #f6d861;\n  background-color: #f4d313;\n}\n.pagination-info {\n  background-color: #3498db;\n}\n.pagination-info li.previous > a {\n  border-right-color: #79bbe7;\n}\n.pagination-info li > a,\n.pagination-info li > span {\n  border-left-color: #79bbe7;\n}\n.pagination-info li > a:hover,\n.pagination-info li > span:hover,\n.pagination-info li > a:focus,\n.pagination-info li > span:focus {\n  border-left-color: #79bbe7;\n  background-color: #5dade2;\n}\n.pagination-info li > a:active,\n.pagination-info li > span:active {\n  background-color: #2c81ba;\n}\n.pagination-info li.active > a,\n.pagination-info li.active > span {\n  border-left-color: #79bbe7;\n  background-color: #2c81ba;\n}\n.pagination-info li.active > a:hover,\n.pagination-info li.active > span:hover,\n.pagination-info li.active > a:focus,\n.pagination-info li.active > span:focus {\n  border-left-color: #79bbe7;\n  background-color: #5dade2;\n}\n.pagination-inverse {\n  background-color: #34495e;\n}\n.pagination-inverse li.previous > a {\n  border-right-color: #798795;\n}\n.pagination-inverse li > a,\n.pagination-inverse li > span {\n  border-left-color: #798795;\n}\n.pagination-inverse li > a:hover,\n.pagination-inverse li > span:hover,\n.pagination-inverse li > a:focus,\n.pagination-inverse li > span:focus {\n  border-left-color: #798795;\n  background-color: #415b76;\n}\n.pagination-inverse li > a:active,\n.pagination-inverse li > span:active {\n  background-color: #2c3e50;\n}\n.pagination-inverse li.active > a,\n.pagination-inverse li.active > span {\n  border-left-color: #798795;\n  background-color: #2c3e50;\n}\n.pagination-inverse li.active > a:hover,\n.pagination-inverse li.active > span:hover,\n.pagination-inverse li.active > a:focus,\n.pagination-inverse li.active > span:focus {\n  border-left-color: #798795;\n  background-color: #415b76;\n}\n.pagination-minimal > li:first-child {\n  border-radius: 6px 0 0 6px;\n}\n.pagination-minimal > li:first-child.previous + li > a,\n.pagination-minimal > li:first-child.previous + li > span {\n  border-left-width: 5px;\n}\n.pagination-minimal > li:last-child {\n  border-radius: 0 6px 6px 0;\n}\n.pagination-minimal > li.previous > a,\n.pagination-minimal > li.next > a,\n.pagination-minimal > li.previous > span,\n.pagination-minimal > li.next > span {\n  background: transparent;\n  border: none;\n  border-right: 2px solid #e4e7ea;\n  margin: 0 9px 0 0;\n  padding: 12px 17px;\n  border-radius: 6px 0 0 6px;\n}\n.pagination-minimal > li.previous > a,\n.pagination-minimal > li.next > a,\n.pagination-minimal > li.previous > span,\n.pagination-minimal > li.next > span,\n.pagination-minimal > li.previous > a:hover,\n.pagination-minimal > li.next > a:hover,\n.pagination-minimal > li.previous > span:hover,\n.pagination-minimal > li.next > span:hover,\n.pagination-minimal > li.previous > a:focus,\n.pagination-minimal > li.next > a:focus,\n.pagination-minimal > li.previous > span:focus,\n.pagination-minimal > li.next > span:focus {\n  border-color: #e4e7ea !important;\n}\n@media (max-width: 767px) {\n  .pagination-minimal > li.previous > a,\n  .pagination-minimal > li.next > a,\n  .pagination-minimal > li.previous > span,\n  .pagination-minimal > li.next > span {\n    margin-right: 0;\n  }\n}\n.pagination-minimal > li.next {\n  margin-left: 9px;\n}\n.pagination-minimal > li.next > a,\n.pagination-minimal > li.next > span {\n  border-left: 2px solid #e4e7ea;\n  border-right: none;\n  margin: 0;\n  border-radius: 0 6px 6px 0;\n}\n.pagination-minimal > li.active > a,\n.pagination-minimal > li.active > span {\n  background-color: #ffffff;\n  border-color: #ffffff;\n  border-width: 2px !important;\n  color: #d6dbdf;\n  margin: 10px 5px 9px;\n}\n.pagination-minimal > li.active > a:hover,\n.pagination-minimal > li.active > span:hover,\n.pagination-minimal > li.active > a:focus,\n.pagination-minimal > li.active > span:focus {\n  background-color: #ffffff;\n  border-color: #ffffff;\n  color: #d6dbdf;\n}\n.pagination-minimal > li.active.previous,\n.pagination-minimal > li.active.next {\n  border-color: #e4e7ea;\n}\n.pagination-minimal > li.active.previous {\n  margin-right: 6px;\n}\n.pagination-minimal > li > a,\n.pagination-minimal > li > span {\n  background: #ffffff;\n  border: 5px solid #d6dbdf;\n  color: #ffffff;\n  line-height: 16px;\n  margin: 7px 2px 6px;\n  min-width: 0;\n  min-height: 16px;\n  padding: 0 4px;\n  border-radius: 50px;\n  background-clip: padding-box;\n  transition: background 0.2s ease-out, border-color 0s ease-out, color 0.2s ease-out;\n}\n.pagination-minimal > li > a:hover,\n.pagination-minimal > li > span:hover,\n.pagination-minimal > li > a:focus,\n.pagination-minimal > li > span:focus {\n  background-color: #1abc9c;\n  border-color: #1abc9c;\n  color: #ffffff;\n  transition: background 0.2s ease-out, border-color 0.2s ease-out, color 0.2s ease-out;\n}\n.pagination-minimal > li > a:active,\n.pagination-minimal > li > span:active {\n  background-color: #16a085;\n  border-color: #16a085;\n}\n.pagination-plain {\n  font-size: 16px;\n  font-weight: 700;\n  list-style-type: none;\n  margin: 0 0 20px;\n  padding: 0;\n  height: 57px;\n}\n.pagination-plain > li {\n  display: inline;\n}\n.pagination-plain > li.previous {\n  padding-right: 23px;\n}\n.pagination-plain > li.next {\n  padding-left: 20px;\n}\n.pagination-plain > li.active > a {\n  color: #d3d7da;\n}\n.pagination-plain > li > a {\n  padding: 0 5px;\n}\n@media (max-width: 480px) {\n  .pagination-plain {\n    overflow: hidden;\n    text-align: center;\n  }\n  .pagination-plain > li.previous {\n    display: block;\n    margin-bottom: 10px;\n    text-align: left;\n    width: 50%;\n  }\n  .pagination-plain > li.next {\n    float: right;\n    margin-top: -64px;\n    text-align: right;\n    width: 50%;\n  }\n}\n@media (min-width: 768px) {\n  .pagination-plain {\n    height: auto;\n  }\n}\n.pagination-dropdown ul {\n  min-width: 67px;\n  width: auto;\n  left: 50%;\n  margin-left: -34px;\n}\n.pagination-dropdown ul li {\n  display: block;\n  margin-right: 0;\n}\n.pagination-dropdown ul li:first-child > a,\n.pagination-dropdown ul li:first-child > span {\n  border-radius: 6px 6px 0 0;\n}\n.pagination-dropdown ul li:last-child > a,\n.pagination-dropdown ul li:last-child > span {\n  border-radius: 0 0 6px 6px !important;\n}\n.pagination-dropdown ul li > a,\n.pagination-dropdown ul li > span {\n  border-left: none;\n  display: block;\n  float: none;\n  padding: 8px 10px 7px;\n  text-align: center;\n  min-height: 0;\n}\n.pagination-dropdown.dropup {\n  position: relative;\n}\n.tooltip {\n  font-size: 14px;\n  line-height: 1.286;\n  z-index: 1070;\n}\n.tooltip.in {\n  opacity: 1;\n  filter: alpha(opacity=100);\n}\n.tooltip.top {\n  margin-top: -5px;\n  padding: 9px 0;\n}\n.tooltip.right {\n  margin-left: 5px;\n  padding: 0 9px;\n}\n.tooltip.bottom {\n  margin-top: 5px;\n  padding: 9px 0;\n}\n.tooltip.left {\n  margin-left: -5px;\n  padding: 0 9px;\n}\n.tooltip-inner {\n  max-width: 183px;\n  line-height: 1.286;\n  padding: 12px 12px;\n  color: #ffffff;\n  background-color: #34495e;\n  border-radius: 6px;\n}\n.tooltip.top .tooltip-arrow {\n  margin-left: -9px;\n  border-width: 9px 9px 0;\n  border-top-color: #34495e;\n}\n.tooltip.right .tooltip-arrow {\n  margin-top: -9px;\n  border-width: 9px 9px 9px 0;\n  border-right-color: #34495e;\n}\n.tooltip.left .tooltip-arrow {\n  margin-top: -9px;\n  border-width: 9px 0 9px 9px;\n  border-left-color: #34495e;\n}\n.tooltip.bottom .tooltip-arrow {\n  margin-left: -9px;\n  border-width: 0 9px 9px;\n  border-bottom-color: #34495e;\n}\n.caret {\n  display: inline-block;\n  width: 0;\n  height: 0;\n  margin-left: 5px;\n  vertical-align: middle;\n  border-top: 8px solid;\n  border-right: 6px solid transparent;\n  border-left: 6px solid transparent;\n  transition: border-color 0.25s, color 0.25s;\n}\n.dropdown-menu,\n.select2-drop {\n  z-index: 1000;\n  background-color: #f3f4f5;\n  min-width: 220px;\n  border: none;\n  margin-top: 9px;\n  padding: 0;\n  font-size: 14px;\n  border-radius: 4px;\n  box-shadow: none;\n}\n.dropdown-menu .divider {\n  height: 2px;\n  margin: 3px 0;\n  overflow: hidden;\n  background-color: rgba(202, 206, 209, 0.5);\n}\n.dropdown-menu > li > a {\n  padding: 8px 16px;\n  line-height: 1.429;\n  color: #606d7a;\n}\n.dropdown-menu > li:first-child > a:first-child {\n  border-top-right-radius: 4px;\n  border-top-left-radius: 4px;\n}\n.dropdown-menu > li:last-child > a:first-child {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.dropdown-menu.typeahead {\n  display: none;\n  width: auto;\n  margin-top: 5px;\n  border: 2px solid #1abc9c;\n  padding: 5px 0;\n  background-color: #ffffff;\n  border-radius: 6px;\n}\n.dropdown-menu.typeahead li a {\n  padding: 6px 14px;\n}\n.dropdown-menu.typeahead li:first-child a,\n.dropdown-menu.typeahead li:last-child a {\n  padding: 6px 14px;\n  border-radius: 0;\n}\n.dropdown-menu > li > a:hover,\n.dropdown-menu > li > a:focus {\n  color: #55606c;\n  background-color: rgba(202, 206, 209, 0.5);\n}\n.dropdown-menu > .active > a,\n.dropdown-menu > .active > a:hover,\n.dropdown-menu > .active > a:focus {\n  color: #ffffff;\n  background-color: #1abc9c;\n}\n.dropdown-menu > .disabled > a,\n.dropdown-menu > .disabled > a:hover,\n.dropdown-menu > .disabled > a:focus {\n  color: #bdc3c7;\n  background-color: transparent;\n  cursor: not-allowed;\n}\n.dropdown-menu-right {\n  left: auto;\n  right: 0;\n}\n.dropdown-menu-left {\n  left: 0;\n  right: auto;\n}\n.dropdown-header {\n  padding: 8px 16px;\n  line-height: 1.538;\n  font-size: 13px;\n  text-transform: uppercase;\n  color: rgba(52, 73, 94, 0.6);\n}\n.dropdown-header:first-child {\n  margin-top: 3px;\n}\n.dropdown-backdrop {\n  z-index: 990;\n}\n.dropup .caret,\n.navbar-fixed-bottom .dropdown .caret {\n  border-bottom: 8px solid;\n  margin-bottom: .25em;\n}\n.dropup .dropdown-menu,\n.navbar-fixed-bottom .dropdown .dropdown-menu {\n  margin-top: 0;\n  margin-bottom: 9px;\n}\n.dropdown-menu-inverse {\n  background-color: #34495e;\n}\n.dropdown-menu-inverse .divider {\n  height: 2px;\n  margin: 3px 0;\n  overflow: hidden;\n  background-color: rgba(43, 60, 78, 0.5);\n}\n.dropdown-menu-inverse > li > a {\n  color: rgba(255, 255, 255, 0.85);\n}\n.dropdown-menu-inverse > li > a:hover,\n.dropdown-menu-inverse > li > a:focus {\n  color: rgba(255, 255, 255, 0.85);\n  background-color: rgba(43, 60, 78, 0.5);\n}\n.dropdown-menu-inverse > .active > a,\n.dropdown-menu-inverse > .active > a:hover,\n.dropdown-menu-inverse > .active > a:focus {\n  color: rgba(255, 255, 255, 0.85);\n  background-color: #1abc9c;\n}\n.dropdown-menu-inverse > .disabled > a,\n.dropdown-menu-inverse > .disabled > a:hover,\n.dropdown-menu-inverse > .disabled > a:focus {\n  color: rgba(255, 255, 255, 0.5);\n}\n.dropdown-menu-inverse > .disabled > a:hover,\n.dropdown-menu-inverse > .disabled > a:focus {\n  background-color: transparent;\n}\n.dropdown-menu-inverse .dropdown-header {\n  color: rgba(255, 255, 255, 0.4);\n}\n@media (min-width: 768px) {\n  .navbar-right .dropdown-menu {\n    left: auto;\n    right: 0;\n  }\n  .navbar-right .dropdown-menu-left {\n    left: 0;\n    right: auto;\n  }\n}\n.select {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  min-width: 220px;\n  width: auto;\n}\n.form-group .select {\n  width: 100%;\n}\n.form-group .select > .select2-choice {\n  width: 100%;\n}\n.select.form-control,\n.select.select2-search input[type=\"text\"] {\n  border: none;\n  padding: 0;\n  height: auto;\n}\n.select2-choice {\n  width: 100%;\n  display: inline-block;\n  position: relative;\n  border: none;\n  font-size: 15px;\n  font-weight: normal;\n  line-height: 1.4;\n  border-radius: 4px;\n  padding: 10px 39px 10px 15px;\n  transition: border 0.25s linear, color 0.25s linear, background-color 0.25s linear;\n}\n.select2-choice:hover,\n.select2-choice:focus {\n  outline: none;\n}\n.select2-choice:active {\n  outline: none;\n  box-shadow: none;\n}\n.select2-container-disabled .select2-choice {\n  opacity: 0.7;\n  filter: alpha(opacity=70);\n}\n.select2-chosen {\n  overflow: hidden;\n  text-align: left;\n}\n.select2-arrow {\n  display: inline-block;\n  border-width: 8px 6px;\n  border-color: #34495e transparent;\n  border-style: solid;\n  border-bottom-style: none;\n  position: absolute;\n  right: 16px;\n  top: 42%;\n  transform: scale(1.001);\n}\n.select2-arrow b {\n  display: none;\n}\n.btn-lg .select2-arrow {\n  border-top-width: 8px;\n  border-right-width: 6px;\n  border-left-width: 6px;\n}\n.select-default .select2-choice {\n  color: #ffffff;\n  background-color: #bdc3c7;\n}\n.select-default .select2-choice:hover,\n.select-default .select2-choice.hover,\n.select-default .select2-choice:focus,\n.select-default .select2-choice:active {\n  color: #ffffff;\n  background-color: #cacfd2;\n  border-color: #cacfd2;\n}\n.select-default .select2-choice:active {\n  background: #a1a6a9;\n  border-color: #a1a6a9;\n}\n.select2-container-disabled.select-default .select2-choice,\n.select2-container-disabled.select-default .select2-choice:hover,\n.select2-container-disabled.select-default .select2-choice:focus,\n.select2-container-disabled.select-default .select2-choice:active {\n  background-color: #bdc3c7;\n  border-color: #bdc3c7;\n}\n.select-default .select2-choice .select2-arrow {\n  border-top-color: #ffffff;\n}\n.select-primary .select2-choice {\n  color: #ffffff;\n  background-color: #1abc9c;\n}\n.select-primary .select2-choice:hover,\n.select-primary .select2-choice.hover,\n.select-primary .select2-choice:focus,\n.select-primary .select2-choice:active {\n  color: #ffffff;\n  background-color: #48c9b0;\n  border-color: #48c9b0;\n}\n.select-primary .select2-choice:active {\n  background: #16a085;\n  border-color: #16a085;\n}\n.select2-container-disabled.select-primary .select2-choice,\n.select2-container-disabled.select-primary .select2-choice:hover,\n.select2-container-disabled.select-primary .select2-choice:focus,\n.select2-container-disabled.select-primary .select2-choice:active {\n  background-color: #bdc3c7;\n  border-color: #1abc9c;\n}\n.select-primary .select2-choice .select2-arrow {\n  border-top-color: #ffffff;\n}\n.select-info .select2-choice {\n  color: #ffffff;\n  background-color: #3498db;\n}\n.select-info .select2-choice:hover,\n.select-info .select2-choice.hover,\n.select-info .select2-choice:focus,\n.select-info .select2-choice:active {\n  color: #ffffff;\n  background-color: #5dade2;\n  border-color: #5dade2;\n}\n.select-info .select2-choice:active {\n  background: #2c81ba;\n  border-color: #2c81ba;\n}\n.select2-container-disabled.select-info .select2-choice,\n.select2-container-disabled.select-info .select2-choice:hover,\n.select2-container-disabled.select-info .select2-choice:focus,\n.select2-container-disabled.select-info .select2-choice:active {\n  background-color: #bdc3c7;\n  border-color: #3498db;\n}\n.select-info .select2-choice .select2-arrow {\n  border-top-color: #ffffff;\n}\n.select-danger .select2-choice {\n  color: #ffffff;\n  background-color: #e74c3c;\n}\n.select-danger .select2-choice:hover,\n.select-danger .select2-choice.hover,\n.select-danger .select2-choice:focus,\n.select-danger .select2-choice:active {\n  color: #ffffff;\n  background-color: #ec7063;\n  border-color: #ec7063;\n}\n.select-danger .select2-choice:active {\n  background: #c44133;\n  border-color: #c44133;\n}\n.select2-container-disabled.select-danger .select2-choice,\n.select2-container-disabled.select-danger .select2-choice:hover,\n.select2-container-disabled.select-danger .select2-choice:focus,\n.select2-container-disabled.select-danger .select2-choice:active {\n  background-color: #bdc3c7;\n  border-color: #e74c3c;\n}\n.select-danger .select2-choice .select2-arrow {\n  border-top-color: #ffffff;\n}\n.select-success .select2-choice {\n  color: #ffffff;\n  background-color: #2ecc71;\n}\n.select-success .select2-choice:hover,\n.select-success .select2-choice.hover,\n.select-success .select2-choice:focus,\n.select-success .select2-choice:active {\n  color: #ffffff;\n  background-color: #58d68d;\n  border-color: #58d68d;\n}\n.select-success .select2-choice:active {\n  background: #27ad60;\n  border-color: #27ad60;\n}\n.select2-container-disabled.select-success .select2-choice,\n.select2-container-disabled.select-success .select2-choice:hover,\n.select2-container-disabled.select-success .select2-choice:focus,\n.select2-container-disabled.select-success .select2-choice:active {\n  background-color: #bdc3c7;\n  border-color: #2ecc71;\n}\n.select-success .select2-choice .select2-arrow {\n  border-top-color: #ffffff;\n}\n.select-warning .select2-choice {\n  color: #ffffff;\n  background-color: #f1c40f;\n}\n.select-warning .select2-choice:hover,\n.select-warning .select2-choice.hover,\n.select-warning .select2-choice:focus,\n.select-warning .select2-choice:active {\n  color: #ffffff;\n  background-color: #f4d313;\n  border-color: #f4d313;\n}\n.select-warning .select2-choice:active {\n  background: #cda70d;\n  border-color: #cda70d;\n}\n.select2-container-disabled.select-warning .select2-choice,\n.select2-container-disabled.select-warning .select2-choice:hover,\n.select2-container-disabled.select-warning .select2-choice:focus,\n.select2-container-disabled.select-warning .select2-choice:active {\n  background-color: #bdc3c7;\n  border-color: #f1c40f;\n}\n.select-warning .select2-choice .select2-arrow {\n  border-top-color: #ffffff;\n}\n.select-inverse .select2-choice {\n  color: #ffffff;\n  background-color: #34495e;\n}\n.select-inverse .select2-choice:hover,\n.select-inverse .select2-choice.hover,\n.select-inverse .select2-choice:focus,\n.select-inverse .select2-choice:active {\n  color: #ffffff;\n  background-color: #415b76;\n  border-color: #415b76;\n}\n.select-inverse .select2-choice:active {\n  background: #2c3e50;\n  border-color: #2c3e50;\n}\n.select2-container-disabled.select-inverse .select2-choice,\n.select2-container-disabled.select-inverse .select2-choice:hover,\n.select2-container-disabled.select-inverse .select2-choice:focus,\n.select2-container-disabled.select-inverse .select2-choice:active {\n  background-color: #bdc3c7;\n  border-color: #34495e;\n}\n.select-inverse .select2-choice .select2-arrow {\n  border-top-color: #ffffff;\n}\n.select2-container.select-hg > .select2-choice {\n  padding: 13px 20px;\n  font-size: 22px;\n  line-height: 1.227;\n  border-radius: 6px;\n  padding-right: 49px;\n  min-height: 53px;\n}\n.select2-container.select-hg > .select2-choice .filter-option {\n  left: 20px;\n  right: 40px;\n  top: 13px;\n}\n.select2-container.select-hg > .select2-choice .select2-arrow {\n  right: 20px;\n}\n.select2-container.select-hg > .select2-choice > [class^=\"fui-\"] {\n  top: 2px;\n}\n.select2-container.select-lg > .select2-choice {\n  padding: 10px 19px;\n  font-size: 17px;\n  line-height: 1.471;\n  border-radius: 6px;\n  padding-right: 47px;\n  min-height: 45px;\n}\n.select2-container.select-lg > .select2-choice .filter-option {\n  left: 18px;\n  right: 38px;\n}\n.select2-container.select-sm > .select2-choice {\n  padding: 9px 13px;\n  font-size: 13px;\n  line-height: 1.385;\n  border-radius: 4px;\n  padding-right: 35px;\n  min-height: 36px;\n}\n.select2-container.select-sm > .select2-choice .filter-option {\n  left: 13px;\n  right: 33px;\n}\n.select2-container.select-sm > .select2-choice .select2-arrow {\n  right: 13px;\n}\n.multiselect {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  min-width: 220px;\n  background-color: #ffffff;\n  border-radius: 6px;\n  text-align: left;\n  font-size: 0;\n  width: auto;\n  max-width: none;\n}\n.form-group .multiselect {\n  width: 100%;\n}\n.form-group .multiselect > .select2-choice {\n  width: 100%;\n}\n.multiselect.form-control,\n.multiselect.select2-search input[type=\"text\"] {\n  height: auto;\n  padding: 6px 1px 1px 6px;\n  border: 2px solid #ebedef;\n}\n.select2-choices {\n  margin: 0;\n  padding: 0;\n  position: relative;\n  cursor: text;\n  overflow: hidden;\n  min-height: 26px;\n}\n.select2-choices li {\n  float: left;\n  list-style: none;\n}\n.select2-search-choice {\n  border-radius: 4px;\n  color: #ffffff;\n  font-size: 13px;\n  cursor: pointer;\n  display: inline-block;\n  position: relative;\n  vertical-align: middle;\n  overflow: hidden;\n  margin: 0 5px 4px 0;\n  line-height: 15px;\n  height: 27px;\n  padding: 6px 21px;\n  transition: .25s linear;\n}\n.select2-search-choice:hover {\n  padding-right: 28px;\n  padding-left: 14px;\n  color: #ffffff;\n}\n.select2-search-choice:hover .select2-search-choice-close {\n  opacity: 1;\n  filter: none;\n  color: inherit;\n}\n.select2-search-choice .select2-search-choice-close {\n  color: #ffffff;\n  cursor: pointer;\n  font-size: 12px;\n  position: absolute;\n  right: 0;\n  text-align: right;\n  text-decoration: none;\n  top: 0;\n  width: 100%;\n  bottom: 0;\n  padding-right: 10px;\n  z-index: 2;\n  opacity: 0;\n  filter: alpha(opacity=0);\n  transition: opacity .25s linear;\n}\n.select2-search-choice .select2-search-choice-close:after {\n  content: \"\\e609\";\n  font-family: \"Flat-UI-Icons\";\n  line-height: 27px;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.select2-search-field input[type=\"text\"] {\n  color: #34495e;\n  font-size: 14px;\n  border: none;\n  box-shadow: none;\n  outline: none;\n  background-color: transparent;\n  padding: 0;\n  margin: 0;\n  width: auto;\n  max-width: inherit;\n  min-width: 80px;\n  vertical-align: top;\n  height: 29px;\n}\n.select2-search-field:first-child input[type=\"text\"] {\n  height: 23px;\n  margin: 3px 0 5px;\n}\n.select2-container-multi.multiselect-default {\n  border-color: #bdc3c7;\n}\n.select2-container-multi.multiselect-default .select2-search-choice {\n  background-color: #bdc3c7;\n}\n.select2-container-multi.multiselect-default .select2-search-choice:hover {\n  background-color: #cacfd2;\n}\n.select2-container-multi.multiselect-primary {\n  border-color: #1abc9c;\n}\n.select2-container-multi.multiselect-primary .select2-search-choice {\n  background-color: #1abc9c;\n}\n.select2-container-multi.multiselect-primary .select2-search-choice:hover {\n  background-color: #48c9b0;\n}\n.select2-container-multi.multiselect-info {\n  border-color: #3498db;\n}\n.select2-container-multi.multiselect-info .select2-search-choice {\n  background-color: #3498db;\n}\n.select2-container-multi.multiselect-info .select2-search-choice:hover {\n  background-color: #5dade2;\n}\n.select2-container-multi.multiselect-danger {\n  border-color: #e74c3c;\n}\n.select2-container-multi.multiselect-danger .select2-search-choice {\n  background-color: #e74c3c;\n}\n.select2-container-multi.multiselect-danger .select2-search-choice:hover {\n  background-color: #ec7063;\n}\n.select2-container-multi.multiselect-success {\n  border-color: #2ecc71;\n}\n.select2-container-multi.multiselect-success .select2-search-choice {\n  background-color: #2ecc71;\n}\n.select2-container-multi.multiselect-success .select2-search-choice:hover {\n  background-color: #58d68d;\n}\n.select2-container-multi.multiselect-warning {\n  border-color: #f1c40f;\n}\n.select2-container-multi.multiselect-warning .select2-search-choice {\n  background-color: #f1c40f;\n}\n.select2-container-multi.multiselect-warning .select2-search-choice:hover {\n  background-color: #f4d313;\n}\n.select2-container-multi.multiselect-inverse {\n  border-color: #34495e;\n}\n.select2-container-multi.multiselect-inverse .select2-search-choice {\n  background-color: #34495e;\n}\n.select2-container-multi.multiselect-inverse .select2-search-choice:hover {\n  background-color: #415b76;\n}\n.select2-drop {\n  min-width: 220px;\n  margin-top: 9px;\n  visibility: visible;\n  opacity: 1;\n  filter: none;\n  border-radius: 4px;\n  font-size: 14px;\n  position: absolute;\n  z-index: 9999;\n  top: 100%;\n  transition: none;\n}\n.select2-drop.select2-drop-above {\n  margin-top: -9px;\n}\n.select2-drop.select2-drop-auto-width {\n  width: auto;\n}\n.select2-drop.show-select-search .select2-search {\n  display: block;\n}\n.select2-drop.show-select-search .select2-search + .select2-results > li:first-child .select2-result-label {\n  border-radius: 0;\n}\n.select2-drop .select2-results {\n  padding: 0;\n  margin: 0;\n  list-style: none;\n}\n.select2-drop .select2-results > li:first-child > .select2-result-label {\n  border-top-right-radius: 4px;\n  border-top-left-radius: 4px;\n}\n.select2-drop .select2-results > li:last-child > .select2-result-label {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.select2-drop .select2-result-sub {\n  padding: 0;\n  margin: 0;\n  list-style: none;\n}\n.select2-drop .select2-result-sub > li:last-child > .select2-result-label {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.select2-drop .select2-no-results {\n  padding: 8px 15px;\n}\n.select2-drop .select2-result-label {\n  line-height: 1.429;\n  padding: 8px 16px;\n  user-select: none;\n  transition: background-color 0.25s, color 0.25s;\n}\n.select2-drop .select2-result-selectable .select2-result-label {\n  color: rgba(52, 73, 94, 0.85);\n  cursor: pointer;\n}\n.select2-drop .select2-result-selectable .select2-result-label:focus,\n.select2-drop .select2-result-selectable .select2-result-label:hover,\n.select2-drop .select2-result-selectable .select2-result-label:active {\n  background-color: #e1e4e7;\n  color: inherit;\n  outline: none;\n}\n.select2-drop .select2-disabled {\n  cursor: default;\n  color: rgba(52, 73, 94, 0.95);\n  opacity: 0.4;\n  filter: alpha(opacity=40);\n}\n.select2-drop .select2-disabled:focus,\n.select2-drop .select2-disabled:hover,\n.select2-drop .select2-disabled:active {\n  background: none !important;\n}\n.select2-drop .select2-highlighted > .select2-result-label {\n  background: #1abc9c;\n  color: #ffffff;\n}\n.select2-drop .select2-result-with-children > .select2-result-label {\n  font-size: 13px;\n  text-transform: uppercase;\n  color: rgba(52, 73, 94, 0.6);\n  margin-top: 5px;\n}\n.select2-drop .select2-result-with-children + .select2-result-with-children > .select2-result-label {\n  margin-top: 11px;\n}\n.select2-results {\n  max-height: 200px;\n  position: relative;\n  overflow-x: hidden;\n  overflow-y: auto;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n}\n.select2-search {\n  padding: 8px 6px;\n  width: 100%;\n  display: none;\n}\n.select2-search input[type=\"text\"] {\n  width: 100%;\n  height: auto !important;\n}\n.select-inverse-dropdown {\n  background-color: #34495e;\n  color: rgba(255, 255, 255, 0.75);\n}\n.select-inverse-dropdown .select2-results .select2-result-label {\n  color: #ffffff;\n}\n.select-inverse-dropdown .select2-results .select2-result-label:focus,\n.select-inverse-dropdown .select2-results .select2-result-label:hover,\n.select-inverse-dropdown .select2-results .select2-result-label:active {\n  background: #2c3e50;\n}\n.select-inverse-dropdown .select2-results.select2-disabled .select2-result-label:hover {\n  color: #ffffff;\n}\n.select-inverse-dropdown .select2-result-with-children > .select2-result-label {\n  color: rgba(255, 255, 255, 0.6);\n}\n.select-inverse-dropdown .select2-result-with-children > .select2-result-label:hover {\n  color: #ffffff;\n  background: none !important;\n}\n.select2-drop-multi {\n  border-radius: 6px;\n}\n.select2-drop-multi .select2-results {\n  padding: 2px 0;\n}\n.select2-drop-multi .select2-result {\n  padding: 2px 4px;\n}\n.select2-drop-multi .select2-result-label {\n  border-radius: 4px;\n}\n.select2-drop-multi .select2-selected {\n  display: none;\n}\n.select2-offscreen,\n.select2-offscreen:focus {\n  clip: rect(0 0 0 0) !important;\n  width: 1px !important;\n  height: 1px !important;\n  border: 0 !important;\n  margin: 0 !important;\n  padding: 0 !important;\n  overflow: hidden !important;\n  position: absolute !important;\n  outline: 0 !important;\n  left: 0 !important;\n  top: 0 !important;\n}\n.select2-hidden-accessible {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n.select2-offscreen,\n.select2-offscreen:focus {\n  clip: rect(0 0 0 0) !important;\n  width: 1px !important;\n  height: 1px !important;\n  border: 0 !important;\n  margin: 0 !important;\n  padding: 0 !important;\n  overflow: hidden !important;\n  position: absolute !important;\n  outline: 0 !important;\n  left: 0 !important;\n  top: 0 !important;\n}\n.select2-display-none {\n  display: none;\n}\n.select2-measure-scrollbar {\n  position: absolute;\n  top: -10000px;\n  left: -10000px;\n  width: 100px;\n  height: 100px;\n  overflow: scroll;\n}\n.select2-drop-mask {\n  border: 0;\n  margin: 0;\n  padding: 0;\n  position: fixed;\n  left: 0;\n  top: 0;\n  min-height: 100%;\n  min-width: 100%;\n  height: auto;\n  width: auto;\n  z-index: 9998;\n  /* styles required for IE to work */\n  background-color: #fff;\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.tile {\n  background-color: #eff0f2;\n  border-radius: 6px;\n  padding: 14px;\n  margin-bottom: 20px;\n  position: relative;\n  text-align: center;\n}\n.tile .tile-hot-ribbon {\n  display: block;\n  position: absolute;\n  right: -4px;\n  top: -4px;\n  width: 82px;\n}\n.tile p {\n  font-size: 15px;\n  margin-bottom: 33px;\n}\n.tile-image {\n  height: 100px;\n  margin: 31px 0 27px;\n  vertical-align: bottom;\n}\n.tile-image.big-illustration {\n  height: 111px;\n  margin-top: 20px;\n  width: 112px;\n}\n.tile-title {\n  font-size: 20px;\n  margin: 0;\n}\n.navbar {\n  font-size: 16px;\n  min-height: 53px;\n  margin-bottom: 30px;\n  border: none;\n  border-radius: 6px;\n}\n@media (min-width: 768px) {\n  .navbar-header {\n    float: left;\n  }\n}\n.navbar-collapse {\n  box-shadow: none;\n  padding-right: 21px;\n  padding-left: 21px;\n}\n.navbar-collapse .navbar-form:first-child {\n  border: none;\n}\n@media (min-width: 768px) {\n  .navbar-collapse .navbar-nav.navbar-left:first-child {\n    margin-left: -21px;\n  }\n  .navbar-collapse .navbar-nav.navbar-left:first-child > li:first-child a {\n    border-bottom-left-radius: 6px;\n    border-top-left-radius: 6px;\n  }\n  .navbar-collapse .navbar-nav.navbar-right:last-child {\n    margin-right: -21px;\n  }\n  .navbar-collapse .navbar-nav.navbar-right:last-child > .dropdown:last-child > a {\n    border-radius: 0 6px 6px 0;\n  }\n  .navbar-fixed-top .navbar-collapse .navbar-form.navbar-right:last-child,\n  .navbar-fixed-bottom .navbar-collapse .navbar-form.navbar-right:last-child {\n    margin-right: 0;\n  }\n}\n@media (max-width: 767px) {\n  .navbar-collapse .navbar-nav.navbar-right:last-child {\n    margin-bottom: 3px;\n  }\n}\n.navbar .container,\n.navbar .container-fluid {\n  padding-left: 21px;\n  padding-right: 21px;\n}\n.navbar .container > .navbar-header,\n.navbar .container-fluid > .navbar-header,\n.navbar .container > .navbar-collapse,\n.navbar .container-fluid > .navbar-collapse {\n  margin-right: -21px;\n  margin-left: -21px;\n}\n@media (min-width: 768px) {\n  .navbar .container > .navbar-header,\n  .navbar .container-fluid > .navbar-header,\n  .navbar .container > .navbar-collapse,\n  .navbar .container-fluid > .navbar-collapse {\n    margin-right: 0;\n    margin-left: 0;\n  }\n}\n.navbar-static-top {\n  z-index: 1000;\n  border-width: 0;\n  border-radius: 0;\n}\n.navbar-fixed-top,\n.navbar-fixed-bottom {\n  z-index: 1030;\n  border-radius: 0;\n}\n.navbar-fixed-top {\n  border-width: 0;\n}\n.navbar-fixed-bottom {\n  margin-bottom: 0;\n  border-width: 0;\n}\n.navbar-brand {\n  font-size: 24px;\n  line-height: 1.042;\n  height: 53px;\n  font-weight: 700;\n  padding: 14px 21px;\n}\n.navbar-brand > [class*=\"fui-\"] {\n  font-size: 19px;\n  line-height: 1.263;\n  vertical-align: top;\n}\n@media (min-width: 768px) {\n  .navbar > .container .navbar-brand,\n  .navbar > .container-fluid .navbar-brand {\n    margin-left: -21px;\n  }\n}\n.navbar-toggle {\n  border: none;\n  color: #34495e;\n  margin: 0 0 0 21px;\n  padding: 0 21px;\n  height: 53px;\n  line-height: 53px;\n}\n.navbar-toggle:before {\n  color: #16a085;\n  content: \"\\e61a\";\n  font-family: \"Flat-UI-Icons\";\n  font-size: 22px;\n  font-style: normal;\n  font-weight: normal;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  transition: color .25s linear;\n}\n.navbar-toggle:hover,\n.navbar-toggle:focus {\n  outline: none;\n}\n.navbar-toggle:hover:before,\n.navbar-toggle:focus:before {\n  color: #1abc9c;\n}\n.navbar-toggle .icon-bar {\n  display: none;\n}\n@media (min-width: 768px) {\n  .navbar-toggle {\n    display: none;\n  }\n}\n.navbar-nav {\n  margin: 0;\n}\n.navbar-nav > li > a {\n  font-size: 16px;\n  padding: 15px 21px;\n  line-height: 23px;\n  font-weight: 700;\n}\n.navbar-nav > li > a:hover,\n.navbar-nav > li > a:focus,\n.navbar-nav .open > a:focus,\n.navbar-nav .open > a:hover {\n  background-color: transparent;\n}\n.navbar-nav [class^=\"fui-\"] {\n  line-height: 20px;\n  position: relative;\n  top: 1px;\n}\n.navbar-nav .visible-sm > [class^=\"fui-\"],\n.navbar-nav .visible-xs > [class^=\"fui-\"] {\n  margin-left: 12px;\n}\n@media (max-width: 767px) {\n  .navbar-nav {\n    margin: 0 -21px;\n  }\n  .navbar-nav .open .dropdown-menu > li > a,\n  .navbar-nav .open .dropdown-menu .dropdown-header {\n    padding: 7px 15px 7px 31px !important;\n  }\n  .navbar-nav .open .dropdown-menu > li > a {\n    line-height: 23px;\n  }\n  .navbar-nav > li > a {\n    padding-top: 7px;\n    padding-bottom: 7px;\n  }\n}\n.navbar-input {\n  height: 35px;\n  padding: 5px 10px;\n  font-size: 13px;\n  line-height: 1.4;\n  border-radius: 6px;\n}\nselect.navbar-input {\n  height: 35px;\n  line-height: 35px;\n}\ntextarea.navbar-input,\nselect[multiple].navbar-input {\n  height: auto;\n}\n.navbar-form {\n  box-shadow: none;\n  margin-top: 0;\n  margin-bottom: 0;\n  padding-right: 19px;\n  padding-left: 19px;\n  padding-top: 9px;\n  padding-bottom: 9px;\n}\n@media (max-width: 767px) {\n  .navbar-form {\n    margin: 3px -21px;\n    width: auto;\n  }\n}\n.navbar-form .form-control,\n.navbar-form .input-group-addon,\n.navbar-form .btn,\n.navbar-form .select2-search input[type=\"text\"] {\n  height: 35px;\n  padding: 5px 10px;\n  font-size: 13px;\n  line-height: 1.4;\n  border-radius: 6px;\n}\nselect.navbar-form .form-control,\nselect.navbar-form .input-group-addon,\nselect.navbar-form .btn,\nselect.navbar-form .select2-search input[type=\"text\"] {\n  height: 35px;\n  line-height: 35px;\n}\ntextarea.navbar-form .form-control,\ntextarea.navbar-form .input-group-addon,\ntextarea.navbar-form .btn,\nselect[multiple].navbar-form .form-control,\nselect[multiple].navbar-form .input-group-addon,\nselect[multiple].navbar-form .btn,\ntextarea.navbar-form .select2-search input[type=\"text\"],\nselect[multiple].navbar-form .select2-search input[type=\"text\"] {\n  height: auto;\n}\n.navbar-form .btn {\n  margin: 0;\n}\n.navbar-form .input-group .form-control:first-child,\n.navbar-form .input-group-addon:first-child,\n.navbar-form .input-group-btn:first-child > .btn,\n.navbar-form .input-group-btn:first-child > .dropdown-toggle,\n.navbar-form .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.navbar-form .input-group .select2-search input[type=\"text\"]:first-child {\n  border-bottom-right-radius: 0;\n  border-top-right-radius: 0;\n}\n.navbar-form .input-group .form-control:last-child,\n.navbar-form .input-group-addon:last-child,\n.navbar-form .input-group-btn:last-child > .btn,\n.navbar-form .input-group-btn:last-child > .dropdown-toggle,\n.navbar-form .input-group-btn:first-child > .btn:not(:first-child),\n.navbar-form .input-group .select2-search input[type=\"text\"]:last-child {\n  border-bottom-left-radius: 0;\n  border-top-left-radius: 0;\n}\n.navbar-form .form-control,\n.navbar-form .select2-search input[type=\"text\"] {\n  font-size: 15px;\n  border-radius: 5px;\n  display: table-cell;\n}\n.navbar-form .form-group ~ .btn {\n  font-size: 15px;\n  border-radius: 5px;\n  margin-left: 5px;\n}\n.navbar-form .form-group + .btn {\n  margin-right: 5px;\n}\n@media (min-width: 768px) {\n  .navbar-form .input-group {\n    width: 195px;\n  }\n}\n@media (max-width: 767px) {\n  .navbar-form .form-group {\n    margin-bottom: 7px;\n  }\n  .navbar-form .form-group:last-child {\n    margin-bottom: 0;\n  }\n  .navbar-form .form-group + .btn {\n    margin-left: 0;\n  }\n}\n.navbar-nav > li > .dropdown-menu {\n  min-width: 100%;\n  margin-top: 9px;\n  border-radius: 4px;\n}\n@media (max-width: 767px) {\n  .navbar-nav > li.open > .dropdown-menu {\n    margin-top: 0 !important;\n  }\n}\n.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.navbar-nav > .open > .dropdown-toggle,\n.navbar-nav > .open > .dropdown-toggle:focus,\n.navbar-nav > .open > .dropdown-toggle:hover {\n  background-color: transparent;\n}\n.navbar-text {\n  font-size: 16px;\n  line-height: 1.438;\n  color: #34495e;\n  margin-top: 0;\n  margin-bottom: 0;\n  padding-top: 15px;\n  padding-bottom: 15px;\n}\n@media (min-width: 768px) {\n  .navbar-text {\n    margin-left: 21px;\n    margin-right: 21px;\n  }\n  .navbar-text.navbar-right:last-child {\n    margin-right: 0;\n  }\n}\n.navbar-btn {\n  margin-top: 6px;\n  margin-bottom: 6px;\n}\n.navbar-btn.btn-sm {\n  margin-top: 9px;\n  margin-bottom: 8px;\n}\n.navbar-btn.btn-xs {\n  margin-top: 14px;\n  margin-bottom: 14px;\n}\n.navbar-unread,\n.navbar-new {\n  font-family: \"Lato\", Helvetica, Arial, sans-serif;\n  background-color: #1abc9c;\n  border-radius: 50%;\n  color: #ffffff;\n  font-size: 0;\n  font-weight: 700;\n  height: 6px;\n  line-height: 1;\n  position: absolute;\n  right: 12px;\n  text-align: center;\n  top: 35%;\n  width: 6px;\n  z-index: 10;\n}\n@media (max-width: 768px) {\n  .navbar-unread,\n  .navbar-new {\n    position: static;\n    float: right;\n    margin: 0 0 0 10px;\n  }\n}\n.active .navbar-unread,\n.active .navbar-new {\n  background-color: #ffffff;\n  display: none;\n}\n.navbar-new {\n  background-color: #e74c3c;\n  font-size: 12px;\n  height: 18px;\n  line-height: 17px;\n  margin: -6px -10px;\n  min-width: 18px;\n  padding: 0 1px;\n  width: auto;\n  -webkit-font-smoothing: subpixel-antialiased;\n}\n.navbar-default {\n  background-color: #ecf0f1;\n}\n.navbar-default .navbar-brand {\n  color: #34495e;\n}\n.navbar-default .navbar-brand:hover,\n.navbar-default .navbar-brand:focus {\n  color: #1abc9c;\n  background-color: transparent;\n}\n.navbar-default .navbar-toggle:before {\n  color: #34495e;\n}\n.navbar-default .navbar-toggle:hover,\n.navbar-default .navbar-toggle:focus {\n  background-color: transparent;\n}\n.navbar-default .navbar-toggle:hover:before,\n.navbar-default .navbar-toggle:focus:before {\n  color: #1abc9c;\n}\n.navbar-default .navbar-collapse,\n.navbar-default .navbar-form {\n  border-color: #e5e9ea;\n  border-width: 2px;\n}\n.navbar-default .navbar-nav > li > a {\n  color: #34495e;\n}\n.navbar-default .navbar-nav > li > a:hover,\n.navbar-default .navbar-nav > li > a:focus {\n  color: #1abc9c;\n  background-color: transparent;\n}\n.navbar-default .navbar-nav > .active > a,\n.navbar-default .navbar-nav > .active > a:hover,\n.navbar-default .navbar-nav > .active > a:focus {\n  color: #1abc9c;\n  background-color: transparent;\n}\n.navbar-default .navbar-nav > .disabled > a,\n.navbar-default .navbar-nav > .disabled > a:hover,\n.navbar-default .navbar-nav > .disabled > a:focus {\n  color: #cccccc;\n  background-color: transparent;\n}\n.navbar-default .navbar-nav > .dropdown > a .caret {\n  border-top-color: #34495e;\n  border-bottom-color: #34495e;\n}\n.navbar-default .navbar-nav > .active > a .caret {\n  border-top-color: #1abc9c;\n  border-bottom-color: #1abc9c;\n}\n.navbar-default .navbar-nav > .dropdown > a:hover .caret,\n.navbar-default .navbar-nav > .dropdown > a:focus .caret {\n  border-top-color: #1abc9c;\n  border-bottom-color: #1abc9c;\n}\n.navbar-default .navbar-nav > .open > a,\n.navbar-default .navbar-nav > .open > a:hover,\n.navbar-default .navbar-nav > .open > a:focus {\n  background-color: transparent;\n  color: #1abc9c;\n}\n.navbar-default .navbar-nav > .open > a .caret,\n.navbar-default .navbar-nav > .open > a:hover .caret,\n.navbar-default .navbar-nav > .open > a:focus .caret {\n  border-top-color: #1abc9c;\n  border-bottom-color: #1abc9c;\n}\n@media (max-width: 767px) {\n  .navbar-default .navbar-nav .open .dropdown-menu > li > a {\n    color: #34495e;\n  }\n  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,\n  .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {\n    color: #1abc9c;\n    background-color: transparent;\n  }\n  .navbar-default .navbar-nav .open .dropdown-menu > .active > a,\n  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover,\n  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {\n    color: #1abc9c;\n    background-color: transparent;\n  }\n  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a,\n  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover,\n  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {\n    color: #cccccc;\n    background-color: transparent;\n  }\n}\n.navbar-default .navbar-form .form-control,\n.navbar-default .navbar-form .select2-search input[type=\"text\"] {\n  border-color: transparent;\n}\n.navbar-default .navbar-form .form-control::-moz-placeholder,\n.navbar-default .navbar-form .select2-search input[type=\"text\"]::-moz-placeholder {\n  color: #aeb6bf;\n  opacity: 1;\n}\n.navbar-default .navbar-form .form-control:-ms-input-placeholder,\n.navbar-default .navbar-form .select2-search input[type=\"text\"]:-ms-input-placeholder {\n  color: #aeb6bf;\n}\n.navbar-default .navbar-form .form-control::-webkit-input-placeholder,\n.navbar-default .navbar-form .select2-search input[type=\"text\"]::-webkit-input-placeholder {\n  color: #aeb6bf;\n}\n.navbar-default .navbar-form .form-control:focus,\n.navbar-default .navbar-form .select2-search input[type=\"text\"]:focus {\n  border-color: #1abc9c;\n  color: #1abc9c;\n}\n.navbar-default .navbar-form .input-group-btn .btn {\n  border-color: transparent;\n  color: #919ba4;\n}\n.navbar-default .navbar-form .input-group.focus .form-control,\n.navbar-default .navbar-form .input-group.focus .input-group-btn .btn,\n.navbar-default .navbar-form .input-group.focus .select2-search input[type=\"text\"] {\n  border-color: #1abc9c;\n  color: #1abc9c;\n}\n.navbar-default .navbar-text {\n  color: #34495e;\n}\n.navbar-default .navbar-link {\n  color: #34495e;\n}\n.navbar-default .navbar-link:hover {\n  color: #1abc9c;\n}\n.navbar-default .btn-link {\n  color: #34495e;\n}\n.navbar-default .btn-link:hover,\n.navbar-default .btn-link:focus {\n  color: #1abc9c;\n}\n.navbar-default .btn-link[disabled]:hover,\nfieldset[disabled] .navbar-default .btn-link:hover,\n.navbar-default .btn-link[disabled]:focus,\nfieldset[disabled] .navbar-default .btn-link:focus {\n  color: #cccccc;\n}\n.navbar-inverse {\n  background-color: #34495e;\n}\n.navbar-inverse .navbar-brand {\n  color: #ffffff;\n}\n.navbar-inverse .navbar-brand:hover,\n.navbar-inverse .navbar-brand:focus {\n  color: #1abc9c;\n  background-color: transparent;\n}\n.navbar-inverse .navbar-toggle:before {\n  color: #ffffff;\n}\n.navbar-inverse .navbar-toggle:hover,\n.navbar-inverse .navbar-toggle:focus {\n  background-color: transparent;\n}\n.navbar-inverse .navbar-toggle:hover:before,\n.navbar-inverse .navbar-toggle:focus:before {\n  color: #1abc9c;\n}\n.navbar-inverse .navbar-collapse {\n  border-color: #2f4154;\n  border-width: 2px;\n}\n.navbar-inverse .navbar-nav > li > a {\n  color: #ffffff;\n}\n.navbar-inverse .navbar-nav > li > a:hover,\n.navbar-inverse .navbar-nav > li > a:focus {\n  color: #1abc9c;\n  background-color: transparent;\n}\n.navbar-inverse .navbar-nav > .active > a,\n.navbar-inverse .navbar-nav > .active > a:hover,\n.navbar-inverse .navbar-nav > .active > a:focus {\n  color: #ffffff;\n  background-color: #1abc9c;\n}\n.navbar-inverse .navbar-nav > .disabled > a,\n.navbar-inverse .navbar-nav > .disabled > a:hover,\n.navbar-inverse .navbar-nav > .disabled > a:focus {\n  color: #444444;\n  background-color: transparent;\n}\n.navbar-inverse .navbar-nav > .dropdown > a:hover .caret,\n.navbar-inverse .navbar-nav > .dropdown > a:focus .caret {\n  border-top-color: #1abc9c;\n  border-bottom-color: #1abc9c;\n}\n.navbar-inverse .navbar-nav > .open > a,\n.navbar-inverse .navbar-nav > .open > a:hover,\n.navbar-inverse .navbar-nav > .open > a:focus {\n  background-color: #1abc9c;\n  color: #ffffff;\n  border-left-color: transparent;\n}\n.navbar-inverse .navbar-nav > .open > a .caret,\n.navbar-inverse .navbar-nav > .open > a:hover .caret,\n.navbar-inverse .navbar-nav > .open > a:focus .caret {\n  border-top-color: #ffffff;\n  border-bottom-color: #ffffff;\n}\n.navbar-inverse .navbar-nav > .dropdown > a .caret {\n  border-top-color: #4b6075;\n  border-bottom-color: #4b6075;\n}\n.navbar-inverse .navbar-nav > .open > .dropdown-menu {\n  background-color: #34495e;\n  padding: 3px 4px;\n}\n.navbar-inverse .navbar-nav > .open > .dropdown-menu > li > a {\n  color: #e1e4e7;\n  border-radius: 4px;\n  padding: 6px 9px;\n}\n.navbar-inverse .navbar-nav > .open > .dropdown-menu > li > a:hover,\n.navbar-inverse .navbar-nav > .open > .dropdown-menu > li > a:focus {\n  color: #ffffff;\n  background-color: #1abc9c;\n}\n.navbar-inverse .navbar-nav > .open > .dropdown-menu > .divider {\n  background-color: #2f4154;\n  height: 2px;\n  margin-left: -4px;\n  margin-right: -4px;\n}\n@media (max-width: 767px) {\n  .navbar-inverse .navbar-nav > li > a {\n    border-left-width: 0;\n  }\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {\n    color: #ffffff;\n  }\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover,\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {\n    color: #1abc9c;\n    background-color: transparent;\n  }\n  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a,\n  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover,\n  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {\n    color: #ffffff;\n    background-color: #1abc9c;\n  }\n  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a,\n  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover,\n  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {\n    color: #444444;\n    background-color: transparent;\n  }\n  .navbar-inverse .navbar-nav .dropdown-menu .divider {\n    background-color: #2f4154;\n  }\n}\n.navbar-inverse .navbar-form .form-control,\n.navbar-inverse .navbar-form .select2-search input[type=\"text\"] {\n  color: #536a81;\n  border-color: transparent;\n  background-color: #293a4a;\n}\n.navbar-inverse .navbar-form .form-control::-moz-placeholder,\n.navbar-inverse .navbar-form .select2-search input[type=\"text\"]::-moz-placeholder {\n  color: #536a81;\n  opacity: 1;\n}\n.navbar-inverse .navbar-form .form-control:-ms-input-placeholder,\n.navbar-inverse .navbar-form .select2-search input[type=\"text\"]:-ms-input-placeholder {\n  color: #536a81;\n}\n.navbar-inverse .navbar-form .form-control::-webkit-input-placeholder,\n.navbar-inverse .navbar-form .select2-search input[type=\"text\"]::-webkit-input-placeholder {\n  color: #536a81;\n}\n.navbar-inverse .navbar-form .form-control:focus,\n.navbar-inverse .navbar-form .select2-search input[type=\"text\"]:focus {\n  border-color: #1abc9c;\n  color: #1abc9c;\n}\n.navbar-inverse .navbar-form .btn {\n  color: #ffffff;\n  background-color: #1abc9c;\n}\n.navbar-inverse .navbar-form .btn:hover,\n.navbar-inverse .navbar-form .btn.hover,\n.navbar-inverse .navbar-form .btn:focus,\n.navbar-inverse .navbar-form .btn:active,\n.navbar-inverse .navbar-form .btn.active,\n.open > .dropdown-toggle.navbar-inverse .navbar-form .btn {\n  color: #ffffff;\n  background-color: #48c9b0;\n  border-color: #48c9b0;\n}\n.navbar-inverse .navbar-form .btn:active,\n.navbar-inverse .navbar-form .btn.active,\n.open > .dropdown-toggle.navbar-inverse .navbar-form .btn {\n  background: #16a085;\n  border-color: #16a085;\n}\n.navbar-inverse .navbar-form .btn.disabled,\n.navbar-inverse .navbar-form .btn[disabled],\nfieldset[disabled] .navbar-inverse .navbar-form .btn,\n.navbar-inverse .navbar-form .btn.disabled:hover,\n.navbar-inverse .navbar-form .btn[disabled]:hover,\nfieldset[disabled] .navbar-inverse .navbar-form .btn:hover,\n.navbar-inverse .navbar-form .btn.disabled.hover,\n.navbar-inverse .navbar-form .btn[disabled].hover,\nfieldset[disabled] .navbar-inverse .navbar-form .btn.hover,\n.navbar-inverse .navbar-form .btn.disabled:focus,\n.navbar-inverse .navbar-form .btn[disabled]:focus,\nfieldset[disabled] .navbar-inverse .navbar-form .btn:focus,\n.navbar-inverse .navbar-form .btn.disabled:active,\n.navbar-inverse .navbar-form .btn[disabled]:active,\nfieldset[disabled] .navbar-inverse .navbar-form .btn:active,\n.navbar-inverse .navbar-form .btn.disabled.active,\n.navbar-inverse .navbar-form .btn[disabled].active,\nfieldset[disabled] .navbar-inverse .navbar-form .btn.active {\n  background-color: #bdc3c7;\n  border-color: #1abc9c;\n}\n.navbar-inverse .navbar-form .btn .badge {\n  color: #1abc9c;\n  background-color: #ffffff;\n}\n.navbar-inverse .navbar-form .input-group-btn .btn {\n  border-color: transparent;\n  background-color: #293a4a;\n  color: #526a82;\n}\n.navbar-inverse .navbar-form .input-group.focus .form-control,\n.navbar-inverse .navbar-form .input-group.focus .input-group-btn .btn,\n.navbar-inverse .navbar-form .input-group.focus .select2-search input[type=\"text\"] {\n  border-color: #1abc9c;\n  color: #1abc9c;\n}\n@media (max-width: 767px) {\n  .navbar-inverse .navbar-form {\n    border-color: #2f4154;\n    border-width: 2px 0;\n  }\n}\n.navbar-inverse .navbar-text {\n  color: #ffffff;\n}\n.navbar-inverse .navbar-text a {\n  color: #ffffff;\n}\n.navbar-inverse .navbar-text a:hover,\n.navbar-inverse .navbar-text a:focus {\n  color: #1abc9c;\n}\n.navbar-inverse .navbar-btn {\n  color: #ffffff;\n  background-color: #1abc9c;\n}\n.navbar-inverse .navbar-btn:hover,\n.navbar-inverse .navbar-btn.hover,\n.navbar-inverse .navbar-btn:focus,\n.navbar-inverse .navbar-btn:active,\n.navbar-inverse .navbar-btn.active,\n.open > .dropdown-toggle.navbar-inverse .navbar-btn {\n  color: #ffffff;\n  background-color: #48c9b0;\n  border-color: #48c9b0;\n}\n.navbar-inverse .navbar-btn:active,\n.navbar-inverse .navbar-btn.active,\n.open > .dropdown-toggle.navbar-inverse .navbar-btn {\n  background: #16a085;\n  border-color: #16a085;\n}\n.navbar-inverse .navbar-btn.disabled,\n.navbar-inverse .navbar-btn[disabled],\nfieldset[disabled] .navbar-inverse .navbar-btn,\n.navbar-inverse .navbar-btn.disabled:hover,\n.navbar-inverse .navbar-btn[disabled]:hover,\nfieldset[disabled] .navbar-inverse .navbar-btn:hover,\n.navbar-inverse .navbar-btn.disabled.hover,\n.navbar-inverse .navbar-btn[disabled].hover,\nfieldset[disabled] .navbar-inverse .navbar-btn.hover,\n.navbar-inverse .navbar-btn.disabled:focus,\n.navbar-inverse .navbar-btn[disabled]:focus,\nfieldset[disabled] .navbar-inverse .navbar-btn:focus,\n.navbar-inverse .navbar-btn.disabled:active,\n.navbar-inverse .navbar-btn[disabled]:active,\nfieldset[disabled] .navbar-inverse .navbar-btn:active,\n.navbar-inverse .navbar-btn.disabled.active,\n.navbar-inverse .navbar-btn[disabled].active,\nfieldset[disabled] .navbar-inverse .navbar-btn.active {\n  background-color: #bdc3c7;\n  border-color: #1abc9c;\n}\n.navbar-inverse .navbar-btn .badge {\n  color: #1abc9c;\n  background-color: #ffffff;\n}\n@media (min-width: 768px) {\n  .navbar-embossed > .navbar-collapse {\n    border-radius: 6px;\n    box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.15);\n  }\n  .navbar-embossed.navbar-inverse .navbar-nav .active > a,\n  .navbar-embossed.navbar-inverse .navbar-nav .open > a {\n    box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.15);\n  }\n}\n.navbar-lg {\n  min-height: 76px;\n}\n.navbar-lg .navbar-brand {\n  line-height: 1;\n  height: 76px;\n  padding-top: 26px;\n  padding-bottom: 26px;\n}\n.navbar-lg .navbar-brand > [class*=\"fui-\"] {\n  font-size: 24px;\n  line-height: 1;\n}\n.navbar-lg .navbar-nav > li > a {\n  font-size: 15px;\n  line-height: 1.6;\n}\n@media (min-width: 768px) {\n  .navbar-lg .navbar-nav > li > a {\n    padding-top: 26px;\n    padding-bottom: 26px;\n  }\n}\n.navbar-lg .navbar-toggle {\n  height: 76px;\n  line-height: 76px;\n}\n.navbar-lg .navbar-form {\n  padding-top: 20.5px;\n  padding-bottom: 20.5px;\n}\n.navbar-lg .navbar-text {\n  padding-top: 26.5px;\n  padding-bottom: 26.5px;\n}\n.navbar-lg .navbar-btn {\n  margin-top: 17.5px;\n  margin-bottom: 17.5px;\n}\n.navbar-lg .navbar-btn.btn-sm {\n  margin-top: 20.5px;\n  margin-bottom: 20.5px;\n}\n.navbar-lg .navbar-btn.btn-xs {\n  margin-top: 25.5px;\n  margin-bottom: 25.5px;\n}\n.bootstrap-switch {\n  font-size: 15px;\n  line-height: 29px;\n  display: inline-block;\n  cursor: pointer;\n  border-radius: 30px;\n  position: relative;\n  text-align: left;\n  overflow: hidden;\n  vertical-align: middle;\n  width: 80px;\n  height: 29px;\n  -webkit-mask-box-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNy4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSIwIDAgODAgMjkiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDgwIDI5IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxwYXRoIGQ9Ik04MCwxNC41YzAsOC02LjUsMTQuNS0xNC41LDE0LjVoLTUxQzYuNSwyOSwwLDIyLjUsMCwxNC41bDAsMEMwLDYuNSw2LjUsMCwxNC41LDBoNTFDNzMuNSwwLDgwLDYuNSw4MCwxNC41TDgwLDE0LjV6Ii8+DQo8L3N2Zz4NCg==) 0 0 stretch;\n  user-select: none;\n}\n.bootstrap-switch > div {\n  display: inline-block;\n  width: 132px;\n  border-radius: 30px;\n  transform: translate3d(0, 0, 0);\n}\n.bootstrap-switch > div > span {\n  font-weight: 700;\n  line-height: 19px;\n  cursor: pointer;\n  display: inline-block;\n  height: 100%;\n  padding-bottom: 5px;\n  padding-top: 5px;\n  text-align: center;\n  z-index: 1;\n  width: 66px;\n  transition: box-shadow 0.25s ease-out;\n}\n.bootstrap-switch > div > span > [class^=\"fui-\"] {\n  text-indent: 0;\n}\n.bootstrap-switch > div > label {\n  cursor: pointer;\n  display: block;\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  text-indent: -9999px;\n  font-size: 0;\n  top: 0;\n  left: 0;\n  margin: 0;\n  z-index: 200;\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.bootstrap-switch input[type=\"radio\"],\n.bootstrap-switch input[type=\"checkbox\"] {\n  position: absolute !important;\n  margin: 0;\n  top: 0;\n  left: 0;\n  z-index: -1;\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.bootstrap-switch-handle-on {\n  border-bottom-left-radius: 30px;\n  border-top-left-radius: 30px;\n}\n.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-default {\n  box-shadow: inset 0 0 transparent, -16px 0 0 #bdc3c7;\n}\n.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-default:before {\n  border-color: #bdc3c7;\n  background-color: #7f8c9a;\n}\n.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-primary {\n  box-shadow: inset 0 0 transparent, -16px 0 0 #34495e;\n}\n.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-primary:before {\n  border-color: #34495e;\n  background-color: #1abc9c;\n}\n.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-success {\n  box-shadow: inset 0 0 transparent, -16px 0 0 #2ecc71;\n}\n.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-success:before {\n  border-color: #2ecc71;\n  background-color: #ffffff;\n}\n.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-warning {\n  box-shadow: inset 0 0 transparent, -16px 0 0 #f1c40f;\n}\n.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-warning:before {\n  border-color: #f1c40f;\n  background-color: #ffffff;\n}\n.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-info {\n  box-shadow: inset 0 0 transparent, -16px 0 0 #3498db;\n}\n.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-info:before {\n  border-color: #3498db;\n  background-color: #ffffff;\n}\n.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-danger {\n  box-shadow: inset 0 0 transparent, -16px 0 0 #e74c3c;\n}\n.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-danger:before {\n  border-color: #e74c3c;\n  background-color: #ffffff;\n}\n.bootstrap-switch-handle-off {\n  border-bottom-right-radius: 30px;\n  border-top-right-radius: 30px;\n}\n.bootstrap-switch-handle-off:before {\n  display: inline-block;\n  content: \" \";\n  border: 4px solid transparent;\n  border-radius: 50%;\n  text-align: center;\n  vertical-align: top;\n  padding: 0;\n  height: 29px;\n  width: 29px;\n  position: absolute;\n  top: 0;\n  left: 51px;\n  z-index: 100;\n  background-clip: padding-box;\n  transition: border-color 0.25s ease-out, background-color 0.25s ease-out;\n}\n.bootstrap-switch-animate > div {\n  transition: margin-left 0.25s ease-out;\n}\n.bootstrap-switch-on > div {\n  margin-left: 0;\n}\n.bootstrap-switch-off > div {\n  margin-left: -51px;\n}\n.bootstrap-switch-disabled,\n.bootstrap-switch-readonly {\n  opacity: 0.5;\n  filter: alpha(opacity=50);\n  cursor: default;\n}\n.bootstrap-switch-disabled > div > span,\n.bootstrap-switch-readonly > div > span,\n.bootstrap-switch-disabled > div > label,\n.bootstrap-switch-readonly > div > label {\n  cursor: default !important;\n}\n.bootstrap-switch-focused {\n  outline: 0;\n}\n.bootstrap-switch-default {\n  color: #ffffff;\n  background-color: #bdc3c7;\n}\n.bootstrap-switch-default ~ .bootstrap-switch-handle-off:before {\n  background-color: #7f8c9a;\n  border-color: #bdc3c7;\n}\n.bootstrap-switch-on .bootstrap-switch-default ~ .bootstrap-switch-handle-off {\n  box-shadow: inset 16px 0 0 #bdc3c7;\n}\n.bootstrap-switch-primary {\n  color: #1abc9c;\n  background-color: #34495e;\n}\n.bootstrap-switch-primary ~ .bootstrap-switch-handle-off:before {\n  background-color: #1abc9c;\n  border-color: #34495e;\n}\n.bootstrap-switch-on .bootstrap-switch-primary ~ .bootstrap-switch-handle-off {\n  box-shadow: inset 16px 0 0 #34495e;\n}\n.bootstrap-switch-info {\n  color: #ffffff;\n  background-color: #3498db;\n}\n.bootstrap-switch-info ~ .bootstrap-switch-handle-off:before {\n  background-color: #ffffff;\n  border-color: #3498db;\n}\n.bootstrap-switch-on .bootstrap-switch-info ~ .bootstrap-switch-handle-off {\n  box-shadow: inset 16px 0 0 #3498db;\n}\n.bootstrap-switch-success {\n  color: #ffffff;\n  background-color: #2ecc71;\n}\n.bootstrap-switch-success ~ .bootstrap-switch-handle-off:before {\n  background-color: #ffffff;\n  border-color: #2ecc71;\n}\n.bootstrap-switch-on .bootstrap-switch-success ~ .bootstrap-switch-handle-off {\n  box-shadow: inset 16px 0 0 #2ecc71;\n}\n.bootstrap-switch-warning {\n  color: #ffffff;\n  background-color: #f1c40f;\n}\n.bootstrap-switch-warning ~ .bootstrap-switch-handle-off:before {\n  background-color: #ffffff;\n  border-color: #f1c40f;\n}\n.bootstrap-switch-on .bootstrap-switch-warning ~ .bootstrap-switch-handle-off {\n  box-shadow: inset 16px 0 0 #f1c40f;\n}\n.bootstrap-switch-danger {\n  color: #ffffff;\n  background-color: #e74c3c;\n}\n.bootstrap-switch-danger ~ .bootstrap-switch-handle-off:before {\n  background-color: #ffffff;\n  border-color: #e74c3c;\n}\n.bootstrap-switch-on .bootstrap-switch-danger ~ .bootstrap-switch-handle-off {\n  box-shadow: inset 16px 0 0 #e74c3c;\n}\n.bootstrap-switch-square .bootstrap-switch {\n  -webkit-mask-box-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNy4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSIwIDAgODAgMjkiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDgwIDI5IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxwYXRoIGQ9Ik04MCwyNWMwLDIuMi0xLjgsNC00LDRINGMtMi4yLDAtNC0xLjgtNC00VjRjMC0yLjIsMS44LTQsNC00aDcyYzIuMiwwLDQsMS44LDQsNFYyNXoiLz4NCjwvc3ZnPg0K) 0 0 stretch;\n  border-radius: 4px;\n}\n.bootstrap-switch-square .bootstrap-switch > div {\n  border-radius: 4px;\n}\n.bootstrap-switch-square .bootstrap-switch .bootstrap-switch-handle-on {\n  text-indent: -15px;\n  border-bottom-left-radius: 4px;\n  border-top-left-radius: 4px;\n}\n.bootstrap-switch-square .bootstrap-switch .bootstrap-switch-handle-off {\n  text-indent: 15px;\n  border-bottom-right-radius: 4px;\n  border-top-right-radius: 4px;\n}\n.bootstrap-switch-square .bootstrap-switch .bootstrap-switch-handle-off:before {\n  border: none;\n  border-bottom-left-radius: 0;\n  border-top-left-radius: 0;\n  border-bottom-right-radius: 2px;\n  border-top-right-radius: 2px;\n}\n.bootstrap-switch-square .bootstrap-switch-off .bootstrap-switch-handle-off:before {\n  border-bottom-left-radius: 2px;\n  border-top-left-radius: 2px;\n  border-bottom-right-radius: 0;\n  border-top-right-radius: 0;\n}\n.share {\n  background-color: #eff0f2;\n  position: relative;\n  border-radius: 6px;\n}\n.share ul {\n  list-style-type: none;\n  margin: 0;\n  padding: 15px;\n}\n.share li {\n  font-size: 15px;\n  line-height: 1.4;\n  padding-top: 11px;\n}\n.share li:before,\n.share li:after {\n  content: \" \";\n  display: table;\n}\n.share li:after {\n  clear: both;\n}\n.share li:first-child {\n  padding-top: 0;\n}\n.share .toggle {\n  float: right;\n  margin: 0;\n}\n.share .btn {\n  border-top-right-radius: 0;\n  border-top-left-radius: 0;\n}\n.share-label {\n  float: left;\n  font-size: 15px;\n  line-height: 1.4;\n  padding-top: 5px;\n  width: 50%;\n}\n.video-js {\n  background-color: transparent;\n  position: relative;\n  padding-bottom: 47px;\n  font-size: 0;\n  vertical-align: middle;\n  overflow: hidden;\n  backface-visibility: hidden;\n  border-top-radius: 6px;\n  width: 100% !important;\n  height: auto !important;\n}\n.video-js .vjs-tech {\n  height: 100%;\n  width: 100%;\n  display: block;\n}\n.video-js::-moz-full-screen {\n  position: absolute;\n}\n.video-js::-webkit-full-screen {\n  width: 100% !important;\n  height: 100% !important;\n}\n.vjs-fullscreen {\n  position: fixed;\n  overflow: hidden;\n  z-index: 10000;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: 100% !important;\n  height: 100% !important;\n  border-top-radius: 0;\n}\n.vjs-fullscreen .vjs-control-bar {\n  margin-top: 0;\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.vjs-fullscreen .vjs-tech {\n  background-color: #000000;\n}\n.vjs-poster {\n  margin: 0 auto;\n  padding: 0;\n  cursor: pointer;\n  position: relative;\n  width: 100%;\n  max-height: 100%;\n  border-top-radius: 6px;\n}\n.vjs-control-bar {\n  position: relative;\n  height: 47px;\n  color: #ffffff;\n  background: #2c3e50;\n  margin-top: -1px;\n  border-bottom-right-radius: 6px;\n  border-bottom-left-radius: 6px;\n  user-select: none;\n}\n.vjs-control-bar.vjs-fade-out {\n  visibility: visible !important;\n  opacity: 1 !important;\n}\n.vjs-text-track-display {\n  text-align: center;\n  position: absolute;\n  bottom: 4em;\n  left: 1em;\n  right: 1em;\n  font-family: \"Lato\", Helvetica, Arial, sans-serif;\n}\n.vjs-text-track {\n  display: none;\n  color: #ffffff;\n  font-size: 1.4em;\n  text-align: center;\n  margin-bottom: .1em;\n  background-color: rgba(0, 0, 0, 0.5);\n}\n.vjs-subtitles {\n  color: #ffffff;\n}\n.vjs-captions {\n  color: #fc6;\n}\n.vjs-tt-cue {\n  display: block;\n}\n.vjs-fade-in {\n  visibility: visible !important;\n  opacity: 1 !important;\n  transition: visibility 0s linear 0s, opacity .3s linear;\n}\n.vjs-fade-out {\n  visibility: hidden !important;\n  opacity: 0 !important;\n  transition: visibility 0s linear 1.5s, opacity 1.5s linear;\n}\n.vjs-control {\n  background-position: center;\n  background-repeat: no-repeat;\n  position: relative;\n  text-align: center;\n  display: inline-block;\n  height: 18px;\n  width: 18px;\n  vertical-align: middle;\n}\n.vjs-control:focus {\n  outline: 0;\n}\n.vjs-control > div {\n  background-position: center;\n  background-repeat: no-repeat;\n}\n.vjs-control-text {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n.vjs-play-control {\n  cursor: pointer;\n  height: 47px;\n  width: 58px;\n}\n.vjs-play-control > div {\n  position: relative;\n  height: 47px;\n}\n.vjs-play-control > div:before,\n.vjs-play-control > div:after {\n  position: absolute;\n  font-family: \"Flat-UI-Icons\";\n  color: #1abc9c;\n  font-size: 16px;\n  top: 38%;\n  left: 50%;\n  margin: -0.5em 0 0 -0.5em;\n  -webkit-font-smoothing: antialiased;\n  transition: color .25s, opacity .25s;\n}\n.vjs-play-control > div:after {\n  content: \"\\e615\";\n}\n.vjs-play-control > div:before {\n  content: \"\\e616\";\n}\n.vjs-paused .vjs-play-control:hover > div:before {\n  color: #16a085;\n}\n.vjs-paused .vjs-play-control > div:after {\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.vjs-paused .vjs-play-control > div:before {\n  opacity: 1;\n  filter: none;\n}\n.vjs-playing .vjs-play-control:hover > div:after {\n  color: #16a085;\n}\n.vjs-playing .vjs-play-control > div:after {\n  opacity: 1;\n  filter: none;\n}\n.vjs-playing .vjs-play-control > div:before {\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.vjs-rewind-control {\n  width: 5em;\n  cursor: pointer !important;\n}\n.vjs-rewind-control > div {\n  width: 19px;\n  height: 16px;\n  background: none transparent;\n  margin: .5em auto 0;\n}\n.vjs-mute-control {\n  float: right;\n  margin: 14px 0;\n  cursor: pointer !important;\n}\n.vjs-mute-control:hover > div,\n.vjs-mute-control:focus > div {\n  color: #57718b;\n}\n.vjs-mute-control > div {\n  height: 18px;\n  color: #475d72;\n}\n.vjs-mute-control > div:after,\n.vjs-mute-control > div:before {\n  font-family: \"Flat-UI-Icons\";\n  font-size: 16px;\n  line-height: 18px;\n  position: absolute;\n  left: 50%;\n  margin: 0 0 0 -0.5em;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  transition: color .25s, opacity .25s;\n}\n.vjs-mute-control > div:after {\n  content: \"\\e617\";\n}\n.vjs-mute-control > div:before {\n  content: \"\\e618\";\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.vjs-mute-control.vjs-vol-0 > div:after {\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n.vjs-mute-control.vjs-vol-0 > div:before {\n  opacity: 1;\n  filter: none;\n}\n.vjs-volume-control,\n.vjs-volume-level,\n.vjs-volume-handle,\n.vjs-volume-bar {\n  display: none;\n}\n.vjs-progress-control {\n  height: 12px;\n  position: absolute;\n  left: 60px;\n  right: 160px;\n  width: auto;\n  top: 18px;\n  background: #425669;\n  border-radius: 32px;\n}\n.vjs-progress-holder {\n  position: relative;\n  cursor: pointer !important;\n  padding: 0;\n  margin: 0;\n  height: 12px;\n}\n.vjs-play-progress,\n.vjs-load-progress {\n  display: block;\n  height: 12px;\n  margin: 0;\n  padding: 0;\n  border-radius: 32px;\n}\n.vjs-play-progress {\n  background: #1abc9c;\n  left: -1px;\n  position: absolute;\n  top: 0;\n  border-bottom-right-radius: 0;\n  border-top-right-radius: 0;\n}\n.vjs-load-progress {\n  background: #d6dbdf;\n}\n.vjs-load-progress[style*=\"100%\"],\n.vjs-load-progress[style*=\"99%\"] {\n  border-radius: 32px;\n}\n.vjs-seek-handle {\n  background-color: #16a085;\n  width: 18px;\n  height: 18px;\n  top: 0;\n  position: absolute;\n  margin: -3px 0 0 -3px;\n  border-radius: 50%;\n  transition: background-color 0.25s;\n}\n.vjs-seek-handle[style*=\"95.\"] {\n  margin-left: 3px;\n}\n.vjs-seek-handle[style=\"left: 0%;\"] {\n  margin-left: -2px;\n}\n.vjs-seek-handle:hover,\n.vjs-seek-handle:focus {\n  background-color: #148d75;\n}\n.vjs-seek-handle:active {\n  background-color: #117a65;\n}\n.vjs-time-controls {\n  font-family: \"Lato\", Helvetica, Arial, sans-serif;\n  font-weight: 300;\n  font-size: 13px;\n  line-height: normal;\n  width: auto;\n  height: auto;\n  position: absolute;\n}\n.vjs-time-divider {\n  color: #5d6d7e;\n  font-size: 14px;\n  position: absolute;\n  right: 114px;\n  top: 11px;\n}\n.vjs-remaining-time {\n  display: none;\n}\n.vjs-current-time {\n  right: 122px;\n  top: 16px;\n}\n.vjs-duration {\n  color: #5d6d7e;\n  right: 85px;\n  top: 16px;\n}\n.vjs-fullscreen-control {\n  cursor: pointer;\n  float: right;\n  margin: 14px 15px;\n}\n.vjs-fullscreen-control:hover > div,\n.vjs-fullscreen-control:focus > div {\n  color: #57718b;\n}\n.vjs-fullscreen-control > div {\n  height: 18px;\n  color: #475d72;\n}\n.vjs-fullscreen-control > div:before {\n  font-family: \"Flat-UI-Icons\";\n  content: \"\\e619\";\n  font-size: 16px;\n  line-height: 18px;\n  position: absolute;\n  left: 50%;\n  margin: 0 0 0 -0.5em;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  transition: color .25s, opacity .25s;\n}\n.vjs-menu-button {\n  display: none !important;\n}\n.vjs-loading-spinner {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  background: #ebedee;\n  display: none;\n  height: 16px;\n  width: 16px;\n  border-radius: 10px;\n  margin: -8px 0 0 -8px;\n  animation: sharp 2s ease infinite;\n}\n@-webkit-keyframes sharp {\n  0% {\n    background-color: #e74c3c;\n    border-radius: 10px;\n    transform: rotate(0deg);\n  }\n  50% {\n    background-color: #ebedee;\n    border-radius: 0;\n    transform: rotate(180deg);\n  }\n  100% {\n    background-color: #e74c3c;\n    border-radius: 10px;\n    transform: rotate(360deg);\n  }\n}\n@-moz-keyframes sharp {\n  0% {\n    background-color: #e74c3c;\n    border-radius: 10px;\n    transform: rotate(0deg);\n  }\n  50% {\n    background-color: #ebedee;\n    border-radius: 0;\n    transform: rotate(180deg);\n  }\n  100% {\n    background-color: #e74c3c;\n    border-radius: 10px;\n    transform: rotate(360deg);\n  }\n}\n@-o-keyframes sharp {\n  0% {\n    background-color: #e74c3c;\n    border-radius: 10px;\n    transform: rotate(0deg);\n  }\n  50% {\n    background-color: #ebedee;\n    border-radius: 0;\n    transform: rotate(180deg);\n  }\n  100% {\n    background-color: #e74c3c;\n    border-radius: 10px;\n    transform: rotate(360deg);\n  }\n}\n@keyframes sharp {\n  0% {\n    background-color: #e74c3c;\n    border-radius: 10px;\n    transform: rotate(0deg);\n  }\n  50% {\n    background-color: #ebedee;\n    border-radius: 0;\n    transform: rotate(180deg);\n  }\n  100% {\n    background-color: #e74c3c;\n    border-radius: 10px;\n    transform: rotate(360deg);\n  }\n}\n.todo {\n  color: #798795;\n  margin-bottom: 20px;\n  border-radius: 6px;\n}\n.todo ul {\n  background-color: #2c3e50;\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n  border-radius: 0 0 6px 6px;\n}\n.todo li {\n  background: #34495e;\n  background-size: 20px 20px;\n  cursor: pointer;\n  font-size: 14px;\n  line-height: 1.214;\n  margin-top: 2px;\n  padding: 18px 42px 21px 25px;\n  position: relative;\n  transition: .25s;\n}\n.todo li:first-child {\n  margin-top: 0;\n}\n.todo li:last-child {\n  border-radius: 0 0 6px 6px;\n  padding-bottom: 21px;\n}\n.todo li.todo-done {\n  background: transparent;\n  color: #1abc9c;\n}\n.todo li.todo-done .todo-name {\n  color: #1abc9c;\n}\n.todo li:after {\n  content: \" \";\n  display: block;\n  width: 20px;\n  height: 20px;\n  position: absolute;\n  top: 50%;\n  right: 22px;\n  margin-top: -10px;\n  background: #ffffff;\n  border-radius: 50%;\n}\n.todo li.todo-done:after {\n  content: \"\\e60a\";\n  font-family: 'Flat-UI-Icons';\n  text-align: center;\n  font-size: 12px;\n  line-height: 21px;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background: #1abc9c;\n  color: #2c3e50;\n}\n.todo-search {\n  position: relative;\n  background: #1abc9c;\n  background-size: 16px 16px;\n  border-radius: 6px 6px 0 0;\n  color: #34495e;\n  padding: 19px 25px 20px;\n}\n.todo-search:before {\n  position: absolute;\n  font-family: 'Flat-UI-Icons';\n  content: \"\\e630\";\n  font-size: 16px;\n  line-height: 17px;\n  display: inline-block;\n  top: 50%;\n  left: 92%;\n  margin: -0.5em 0 0 -1em;\n}\n.todo-search-field {\n  background: none;\n  border: none;\n  color: #34495e;\n  font-size: 19px;\n  font-weight: 700;\n  margin: 0;\n  line-height: 23px;\n  padding: 5px 0;\n  text-indent: 0;\n  box-shadow: none;\n  outline: none;\n}\n.todo-search-field::-moz-placeholder {\n  color: #34495e;\n  opacity: 1;\n}\n.todo-search-field:-ms-input-placeholder {\n  color: #34495e;\n}\n.todo-search-field::-webkit-input-placeholder {\n  color: #34495e;\n}\n.todo-icon {\n  float: left;\n  font-size: 24px;\n  padding: 11px 22px 0 0;\n}\n.todo-content {\n  padding-top: 1px;\n  overflow: hidden;\n}\n.todo-name {\n  color: #ffffff;\n  font-size: 17px;\n  margin: 1px 0 3px;\n}\n.pallete-item {\n  width: 140px;\n  float: left;\n  margin: 0 0 20px 20px;\n}\n.palette {\n  font-size: 14px;\n  line-height: 1.214;\n  color: #ffffff;\n  margin: 0;\n  padding: 15px;\n  text-transform: uppercase;\n}\n.palette dt,\n.palette dd {\n  line-height: 1.429;\n}\n.palette dt {\n  display: block;\n  font-weight: bold;\n  opacity: .8;\n}\n.palette dd {\n  font-weight: 300;\n  margin-left: 0;\n  opacity: .8;\n  -webkit-font-smoothing: subpixel-antialiased;\n}\n.palette-turquoise {\n  background-color: #1abc9c;\n}\n.palette-green-sea {\n  background-color: #16a085;\n}\n.palette-emerald {\n  background-color: #2ecc71;\n}\n.palette-nephritis {\n  background-color: #27ae60;\n}\n.palette-peter-river {\n  background-color: #3498db;\n}\n.palette-belize-hole {\n  background-color: #2980b9;\n}\n.palette-amethyst {\n  background-color: #9b59b6;\n}\n.palette-wisteria {\n  background-color: #8e44ad;\n}\n.palette-wet-asphalt {\n  background-color: #34495e;\n}\n.palette-midnight-blue {\n  background-color: #2c3e50;\n}\n.palette-sun-flower {\n  background-color: #f1c40f;\n}\n.palette-orange {\n  background-color: #f39c12;\n}\n.palette-carrot {\n  background-color: #e67e22;\n}\n.palette-pumpkin {\n  background-color: #d35400;\n}\n.palette-alizarin {\n  background-color: #e74c3c;\n}\n.palette-pomegranate {\n  background-color: #c0392b;\n}\n.palette-clouds {\n  background-color: #ecf0f1;\n}\n.palette-silver {\n  background-color: #bdc3c7;\n}\n.palette-concrete {\n  background-color: #95a5a6;\n}\n.palette-asbestos {\n  background-color: #7f8c8d;\n}\n.palette-clouds {\n  color: #bdc3c7;\n}\n.palette-paragraph {\n  color: #7f8c8d;\n  font-size: 12px;\n  line-height: 17px;\n}\n.palette-paragraph span {\n  color: #bdc3c7;\n}\n.palette-headline {\n  color: #7f8c8d;\n  font-size: 13px;\n  font-weight: 700;\n  margin-top: -3px;\n}\n.login {\n  background: url(../img/login/imac.png) 0 0 no-repeat;\n  background-size: 940px 778px;\n  color: #ffffff;\n  margin-bottom: 77px;\n  padding: 38px 38px 267px;\n  position: relative;\n}\n.login-screen {\n  background-color: #1abc9c;\n  min-height: 473px;\n  padding: 123px 199px 33px 306px;\n}\n.login-icon {\n  left: 200px;\n  position: absolute;\n  top: 160px;\n  width: 96px;\n}\n.login-icon > img {\n  display: block;\n  margin-bottom: 6px;\n  width: 100%;\n}\n.login-icon > h4 {\n  font-size: 17px;\n  font-weight: 300;\n  line-height: 34px;\n  opacity: .95;\n}\n.login-icon > h4 small {\n  color: inherit;\n  display: block;\n  font-size: inherit;\n  font-weight: 700;\n}\n.login-form {\n  background-color: #edeff1;\n  padding: 24px 23px 20px;\n  position: relative;\n  border-radius: 6px;\n}\n.login-form .control-group {\n  margin-bottom: 6px;\n  position: relative;\n}\n.login-form .login-field {\n  border-color: transparent;\n  font-size: 17px;\n  text-indent: 3px;\n}\n.login-form .login-field:focus {\n  border-color: #1abc9c;\n}\n.login-form .login-field:focus + .login-field-icon {\n  color: #1abc9c;\n}\n.login-form .login-field-icon {\n  color: #bfc9ca;\n  font-size: 16px;\n  position: absolute;\n  right: 15px;\n  top: 3px;\n  transition: all .25s;\n}\n.login-link {\n  color: #bfc9ca;\n  display: block;\n  font-size: 13px;\n  margin-top: 15px;\n  text-align: center;\n}\n@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (-moz-min-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 3/2), only screen and (-o-min-device-pixel-ratio: 2/1), only screen and (-moz-min-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 2) {\n  .login {\n    background-image: url(../img/login/imac-2x.png);\n  }\n}\nfooter {\n  background-color: #edeff1;\n  color: #bac1c8;\n  font-size: 15px;\n  padding: 0;\n}\nfooter a {\n  color: #9aa4af;\n  font-weight: 700;\n}\nfooter p {\n  font-size: 15px;\n  line-height: 20px;\n  margin-bottom: 10px;\n}\n.footer-title {\n  margin: 0 0 22px;\n  padding-top: 21px;\n  font-size: 24px;\n  line-height: 40px;\n}\n.footer-brand {\n  display: block;\n  margin-bottom: 26px;\n  width: 220px;\n}\n.footer-brand img {\n  width: 216px;\n}\n.footer-banner {\n  background-color: #1abc9c;\n  color: #d1f2eb;\n  margin-left: 42px;\n  min-height: 316px;\n  padding: 0 30px 30px;\n}\n.footer-banner .footer-title {\n  color: #ffffff;\n}\n.footer-banner a {\n  color: #b7f5e9;\n  text-decoration: underline;\n}\n.footer-banner a:hover {\n  text-decoration: none;\n}\n.footer-banner ul {\n  list-style-type: none;\n  margin: 0 0 26px;\n  padding: 0;\n}\n.footer-banner ul li {\n  border-top: 1px solid #1bc5a3;\n  line-height: 19px;\n  padding: 6px 0;\n}\n.footer-banner ul li:first-child {\n  border-top: none;\n  padding-top: 1px;\n}\n.last-col {\n  overflow: hidden;\n}\n.ptn,\n.pvn,\n.pan {\n  padding-top: 0;\n}\n.ptx,\n.pvx,\n.pax {\n  padding-top: 3px;\n}\n.pts,\n.pvs,\n.pas {\n  padding-top: 5px;\n}\n.ptm,\n.pvm,\n.pam {\n  padding-top: 10px;\n}\n.ptl,\n.pvl,\n.pal {\n  padding-top: 20px;\n}\n.prn,\n.phn,\n.pan {\n  padding-right: 0;\n}\n.prx,\n.phx,\n.pax {\n  padding-right: 3px;\n}\n.prs,\n.phs,\n.pas {\n  padding-right: 5px;\n}\n.prm,\n.phm,\n.pam {\n  padding-right: 10px;\n}\n.prl,\n.phl,\n.pal {\n  padding-right: 20px;\n}\n.pbn,\n.pvn,\n.pan {\n  padding-bottom: 0;\n}\n.pbx,\n.pvx,\n.pax {\n  padding-bottom: 3px;\n}\n.pbs,\n.pvs,\n.pas {\n  padding-bottom: 5px;\n}\n.pbm,\n.pvm,\n.pam {\n  padding-bottom: 10px;\n}\n.pbl,\n.pvl,\n.pal {\n  padding-bottom: 20px;\n}\n.pln,\n.phn,\n.pan {\n  padding-left: 0;\n}\n.plx,\n.phx,\n.pax {\n  padding-left: 3px;\n}\n.pls,\n.phs,\n.pas {\n  padding-left: 5px;\n}\n.plm,\n.phm,\n.pam {\n  padding-left: 10px;\n}\n.pll,\n.phl,\n.pal {\n  padding-left: 20px;\n}\n.mtn,\n.mvn,\n.man {\n  margin-top: 0px;\n}\n.mtx,\n.mvx,\n.max {\n  margin-top: 3px;\n}\n.mts,\n.mvs,\n.mas {\n  margin-top: 5px;\n}\n.mtm,\n.mvm,\n.mam {\n  margin-top: 10px;\n}\n.mtl,\n.mvl,\n.mal {\n  margin-top: 20px;\n}\n.mrn,\n.mhn,\n.man {\n  margin-right: 0px;\n}\n.mrx,\n.mhx,\n.max {\n  margin-right: 3px;\n}\n.mrs,\n.mhs,\n.mas {\n  margin-right: 5px;\n}\n.mrm,\n.mhm,\n.mam {\n  margin-right: 10px;\n}\n.mrl,\n.mhl,\n.mal {\n  margin-right: 20px;\n}\n.mbn,\n.mvn,\n.man {\n  margin-bottom: 0px;\n}\n.mbx,\n.mvx,\n.max {\n  margin-bottom: 3px;\n}\n.mbs,\n.mvs,\n.mas {\n  margin-bottom: 5px;\n}\n.mbm,\n.mvm,\n.mam {\n  margin-bottom: 10px;\n}\n.mbl,\n.mvl,\n.mal {\n  margin-bottom: 20px;\n}\n.mln,\n.mhn,\n.man {\n  margin-left: 0px;\n}\n.mlx,\n.mhx,\n.max {\n  margin-left: 3px;\n}\n.mls,\n.mhs,\n.mas {\n  margin-left: 5px;\n}\n.mlm,\n.mhm,\n.mam {\n  margin-left: 10px;\n}\n.mll,\n.mhl,\n.mal {\n  margin-left: 20px;\n}\n/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */\n@media print {\n  .btn {\n    border-style: solid;\n    border-width: 2px;\n  }\n  .dropdown-menu,\n  .select2-drop {\n    background: #fff !important;\n    border: 2px solid #ddd;\n  }\n  .input-group-rounded .input-group-btn + .form-control,\n  .input-group-rounded .input-group-btn + .select2-search input[type=\"text\"],\n  .input-group-rounded .input-group-btn + .select2-search input[type=\"text\"] {\n    padding-left: 10px;\n  }\n  .form-control,\n  .select2-search input[type=\"text\"] {\n    border: 2px solid #ddd !important;\n  }\n  .bootstrap-switch {\n    height: 33px;\n    width: 84px;\n    border: 2px solid #bdc3c7;\n  }\n  .tooltip {\n    border: 2px solid #bdc3c7;\n  }\n  .progress,\n  .ui-slider {\n    background: #ddd !important;\n  }\n  .progress-bar,\n  .ui-slider-range,\n  .ui-slider-handle {\n    background: #bdc3c7 !important;\n  }\n}\n/*# sourceMappingURL=flat-ui.css.map */", "//\n// Glyphicons for Flat UI\n// --------------------------------------------------\n\n@font-face {\n  font-family: 'Flat-UI-Icons';\n  src: url('@{icon-font-path}@{icon-font-name}.eot');\n  src: url('@{icon-font-path}@{icon-font-name}.eot?#iefix') format('embedded-opentype'),\n       url('@{icon-font-path}@{icon-font-name}.woff') format('woff'),\n       url('@{icon-font-path}@{icon-font-name}.ttf') format('truetype'),\n       url('@{icon-font-path}@{icon-font-name}.svg#@{icon-font-svg-id}') format('svg');\n}\n\n[class^=\"fui-\"],\n[class*=\"fui-\"] {\n  font-family: 'Flat-UI-Icons';\n  speak: none;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.fui-triangle-up:before {\n  content: \"\\e600\";\n}\n.fui-triangle-down:before {\n  content: \"\\e601\";\n}\n.fui-triangle-up-small:before {\n  content: \"\\e602\";\n}\n.fui-triangle-down-small:before {\n  content: \"\\e603\";\n}\n.fui-triangle-left-large:before {\n  content: \"\\e604\";\n}\n.fui-triangle-right-large:before {\n  content: \"\\e605\";\n}\n.fui-arrow-left:before {\n  content: \"\\e606\";\n}\n.fui-arrow-right:before {\n  content: \"\\e607\";\n}\n.fui-plus:before {\n  content: \"\\e608\";\n}\n.fui-cross:before {\n  content: \"\\e609\";\n}\n.fui-check:before {\n  content: \"\\e60a\";\n}\n.fui-radio-unchecked:before {\n  content: \"\\e60b\";\n}\n.fui-radio-checked:before {\n  content: \"\\e60c\";\n}\n.fui-checkbox-unchecked:before {\n  content: \"\\e60d\";\n}\n.fui-checkbox-checked:before {\n  content: \"\\e60e\";\n}\n.fui-info-circle:before {\n  content: \"\\e60f\";\n}\n.fui-alert-circle:before {\n  content: \"\\e610\";\n}\n.fui-question-circle:before {\n  content: \"\\e611\";\n}\n.fui-check-circle:before {\n  content: \"\\e612\";\n}\n.fui-cross-circle:before {\n  content: \"\\e613\";\n}\n.fui-plus-circle:before {\n  content: \"\\e614\";\n}\n.fui-pause:before {\n  content: \"\\e615\";\n}\n.fui-play:before {\n  content: \"\\e616\";\n}\n.fui-volume:before {\n  content: \"\\e617\";\n}\n.fui-mute:before {\n  content: \"\\e618\";\n}\n.fui-resize:before {\n  content: \"\\e619\";\n}\n.fui-list:before {\n  content: \"\\e61a\";\n}\n.fui-list-thumbnailed:before {\n  content: \"\\e61b\";\n}\n.fui-list-small-thumbnails:before {\n  content: \"\\e61c\";\n}\n.fui-list-large-thumbnails:before {\n  content: \"\\e61d\";\n}\n.fui-list-numbered:before {\n  content: \"\\e61e\";\n}\n.fui-list-columned:before {\n  content: \"\\e61f\";\n}\n.fui-list-bulleted:before {\n  content: \"\\e620\";\n}\n.fui-window:before {\n  content: \"\\e621\";\n}\n.fui-windows:before {\n  content: \"\\e622\";\n}\n.fui-loop:before {\n  content: \"\\e623\";\n}\n.fui-cmd:before {\n  content: \"\\e624\";\n}\n.fui-mic:before {\n  content: \"\\e625\";\n}\n.fui-heart:before {\n  content: \"\\e626\";\n}\n.fui-location:before {\n  content: \"\\e627\";\n}\n.fui-new:before {\n  content: \"\\e628\";\n}\n.fui-video:before {\n  content: \"\\e629\";\n}\n.fui-photo:before {\n  content: \"\\e62a\";\n}\n.fui-time:before {\n  content: \"\\e62b\";\n}\n.fui-eye:before {\n  content: \"\\e62c\";\n}\n.fui-chat:before {\n  content: \"\\e62d\";\n}\n.fui-home:before {\n  content: \"\\e62e\";\n}\n.fui-upload:before {\n  content: \"\\e62f\";\n}\n.fui-search:before {\n  content: \"\\e630\";\n}\n.fui-user:before {\n  content: \"\\e631\";\n}\n.fui-mail:before {\n  content: \"\\e632\";\n}\n.fui-lock:before {\n  content: \"\\e633\";\n}\n.fui-power:before {\n  content: \"\\e634\";\n}\n.fui-calendar:before {\n  content: \"\\e635\";\n}\n.fui-gear:before {\n  content: \"\\e636\";\n}\n.fui-bookmark:before {\n  content: \"\\e637\";\n}\n.fui-exit:before {\n  content: \"\\e638\";\n}\n.fui-trash:before {\n  content: \"\\e639\";\n}\n.fui-folder:before {\n  content: \"\\e63a\";\n}\n.fui-bubble:before {\n  content: \"\\e63b\";\n}\n.fui-export:before {\n  content: \"\\e63c\";\n}\n.fui-calendar-solid:before {\n  content: \"\\e63d\";\n}\n.fui-star:before {\n  content: \"\\e63e\";\n}\n.fui-star-2:before {\n  content: \"\\e63f\";\n}\n.fui-credit-card:before {\n  content: \"\\e640\";\n}\n.fui-clip:before {\n  content: \"\\e641\";\n}\n.fui-link:before {\n  content: \"\\e642\";\n}\n.fui-tag:before {\n  content: \"\\e643\";\n}\n.fui-document:before {\n  content: \"\\e644\";\n}\n.fui-image:before {\n  content: \"\\e645\";\n}\n.fui-facebook:before {\n  content: \"\\e646\";\n}\n.fui-youtube:before {\n  content: \"\\e647\";\n}\n.fui-vimeo:before {\n  content: \"\\e648\";\n}\n.fui-twitter:before {\n  content: \"\\e649\";\n}\n.fui-spotify:before {\n  content: \"\\e64a\";\n}\n.fui-skype:before {\n  content: \"\\e64b\";\n}\n.fui-pinterest:before {\n  content: \"\\e64c\";\n}\n.fui-path:before {\n  content: \"\\e64d\";\n}\n.fui-linkedin:before {\n  content: \"\\e64e\";\n}\n.fui-google-plus:before {\n  content: \"\\e64f\";\n}\n.fui-dribbble:before {\n  content: \"\\e650\";\n}\n.fui-behance:before {\n  content: \"\\e651\";\n}\n.fui-stumbleupon:before {\n  content: \"\\e652\";\n}\n.fui-yelp:before {\n  content: \"\\e653\";\n}\n.fui-wordpress:before {\n  content: \"\\e654\";\n}\n.fui-windows-8:before {\n  content: \"\\e655\";\n}\n.fui-vine:before {\n  content: \"\\e656\";\n}\n.fui-tumblr:before {\n  content: \"\\e657\";\n}\n.fui-paypal:before {\n  content: \"\\e658\";\n}\n.fui-lastfm:before {\n  content: \"\\e659\";\n}\n.fui-instagram:before {\n  content: \"\\e65a\";\n}\n.fui-html5:before {\n  content: \"\\e65b\";\n}\n.fui-github:before {\n  content: \"\\e65c\";\n}\n.fui-foursquare:before {\n  content: \"\\e65d\";\n}\n.fui-dropbox:before {\n  content: \"\\e65e\";\n}\n.fui-android:before {\n  content: \"\\e65f\";\n}\n.fui-apple:before {\n  content: \"\\e660\";\n}\n", "//\n// Scaffolding\n// --------------------------------------------------\n\n\n// Body reset\n// -------------------------\n\nbody {\n  font-family: @font-family-base;\n  font-size: @font-size-base;\n  line-height: @line-height-base;\n  color: @text-color;\n  background-color: @body-bg;\n}\n\n// Links\n// -------------------------\n\na {\n  color: @link-color;\n  text-decoration: none;\n  transition: .25s;\n    \n  &:hover,\n  &:focus {\n    color: @link-hover-color;\n    text-decoration: @link-hover-decoration;\n  }\n  &:focus {\n    outline: none;\n  }\n}\n\n// Images\n// -------------------------\n\n// Rounded corners\n.img-rounded {\n  border-radius: @border-radius-large;\n}\n\n// Image thumbnails\n//\n// Heads up! This is mixin-ed into thumbnails.less for `.thumbnail`.\n.img-thumbnail {\n  padding: @thumbnail-padding;\n  line-height: @line-height-base;\n  background-color: @thumbnail-bg;\n  border: 2px solid @thumbnail-border;\n  border-radius: @thumbnail-border-radius;\n  transition: all .25s ease-in-out;\n\n  // Keep them at most 100% wide\n  .img-responsive(inline-block);\n}\n\n// Description text under image\n.img-comment {\n  font-size: ceil((@font-size-base * 0.8333)); // ~15px\n  line-height: 1.2;\n  font-style: italic;\n  margin: 24px 0;\n}", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n.img-responsive(@display: block) {\n  display: @display;\n  max-width: 100%; // Part 1: Set a maximum relative to the parent\n  height: auto; // Part 2: Scale the height according to the width, otherwise you get stretching\n}\n\n\n// Retina image\n//\n// Short retina mixin for setting background-image and -size. Note that the\n// spelling of `min--moz-device-pixel-ratio` is intentional.\n.img-retina(@file-1x; @file-2x; @width-1x; @height-1x) {\n  background-image: url(\"@{file-1x}\");\n\n  @media\n  only screen and (-webkit-min-device-pixel-ratio: 2),\n  only screen and (   min--moz-device-pixel-ratio: 2),\n  only screen and (     -o-min-device-pixel-ratio: 2/1),\n  only screen and (        min-device-pixel-ratio: 2),\n  only screen and (                min-resolution: 192dpi),\n  only screen and (                min-resolution: 2dppx) {\n    background-image: url(\"@{file-2x}\");\n    background-size: @width-1x @height-1x;\n  }\n}\n", "//\n// Typography\n// --------------------------------------------------\n\n\n// Headings\n// -------------------------\n\nh1, h2, h3, h4, h5, h6,\n.h1, .h2, .h3, .h4, .h5, .h6 {\n  font-family: @headings-font-family;\n  font-weight: @headings-font-weight;\n  line-height: @headings-line-height;\n  color: @headings-color;\n\n  small {\n    color: @headings-small-color;\n  }\n}\n\nh1,\nh2,\nh3 {\n  margin-top: @line-height-computed;\n  margin-bottom: (@line-height-computed / 2);\n}\nh4,\nh5,\nh6 {\n  margin-top: (@line-height-computed / 2);\n  margin-bottom: (@line-height-computed / 2);\n}\n\nh6 {\n  font-weight: normal;\n}\n\nh1, .h1 { font-size: @font-size-h1; } // ~62px\nh2, .h2 { font-size: @font-size-h2; } // ~52px\nh3, .h3 { font-size: @font-size-h3; } // ~40px\nh4, .h4 { font-size: @font-size-h4; } // ~29px\nh5, .h5 { font-size: @font-size-h5; } // ~28px\nh6, .h6 { font-size: @font-size-h6; } // ~24px\n\n\n// Body text\n// -------------------------\n\np {\n  font-size:   @font-size-base;\n  line-height: @line-height-base;\n  margin: 0 0 (@line-height-computed / 2);\n}\n\n.lead {\n  margin-bottom: @line-height-computed;\n  font-size:   floor((@font-size-base * 1.556));  // ~28px\n  line-height: 1.46428571;                      // ~41px\n  font-weight: 300;\n\n  @media (min-width: @screen-sm-min) {\n    font-size: (@font-size-base * 1.667);       // ~30px\n  }\n}\n\n// Emphasis & misc\n// -------------------------\n\n// Ex: 18px base font * 83% = about 15px\nsmall,\n.small {\n  font-size: 83%;     // ~15px\n  line-height: 2.067; // ~31px\n}\n\n// Contextual emphasis\n.text-muted {\n  color: @text-muted;\n}\n.text-inverse {\n  color: @inverse;\n}\n.text-primary {\n  .text-emphasis-variant(@brand-secondary);\n}\n.text-warning {\n  .text-emphasis-variant(@state-warning-text);\n}\n.text-danger {\n  .text-emphasis-variant(@state-danger-text);\n}\n.text-success {\n  .text-emphasis-variant(@state-success-text);\n}\n.text-info {\n  .text-emphasis-variant(@state-info-text);\n}\n\n// Contextual backgrounds\n.bg-primary {\n  // Given the contrast here, this is the only class to have its color inverted\n  // automatically.\n  color: @inverse;\n  .bg-variant(@brand-primary);\n}\n.bg-success {\n  .bg-variant(@state-success-bg);\n}\n.bg-info {\n  .bg-variant(@state-info-bg);\n}\n.bg-warning {\n  .bg-variant(@state-warning-bg);\n}\n.bg-danger {\n  .bg-variant(@state-danger-bg);\n}\n\n\n// Page header\n// -------------------------\n\n.page-header {\n  padding-bottom: ((@line-height-computed / 2) - 1);\n  margin: (@line-height-computed * 2) 0 @line-height-computed;\n  border-bottom: 2px solid @page-header-border-color;\n}\n\n\n// Lists\n// --------------------------------------------------\n\n// Unordered and Ordered lists\nul,\nol {\n  margin-bottom: (@line-height-computed / 2);\n}\n\n// Description Lists\ndl {\n  margin-bottom: @line-height-computed;\n}\ndt,\ndd {\n  line-height: @line-height-base;\n}\n\n// Horizontal description lists\n//\n// Defaults to being stacked without any of the below styles applied, until the\n// grid breakpoint is reached (default of ~768px).\n\n@media (min-width: @grid-float-breakpoint) {\n  .dl-horizontal {\n    dt {\n      width: (@dl-horizontal-offset - 20);\n    }\n    dd {\n      margin-left: @dl-horizontal-offset;\n    }\n  }\n}\n\n// MISC\n// ----\n\n// Abbreviations and acronyms\nabbr[title],\nabbr[data-original-title] {\n  border-bottom: 1px dotted @abbr-border-color;\n}\n\n// Blockquotes\nblockquote {\n  border-left: 3px solid @blockquote-border-color;\n  padding: 0 0 0 16px;\n  margin: 0 0 @line-height-computed;\n\n  p {\n    font-size: ceil((@font-size-base * 1.111)); // ~20px\n    line-height: 1.55;                        // ~31px\n    font-weight: normal;\n    margin-bottom: .4em;\n  }\n  small,\n  .small {\n    font-size: @font-size-base;\n    line-height: @line-height-base;\n    font-style: italic;\n    color: @blockquote-small-color;\n\n    &:before {\n      content: \"\";\n    }\n  }\n\n  // Float right with text-align: right\n  &.pull-right {\n    padding-right: 16px;\n    padding-left: 0;\n    border-right: 3px solid @blockquote-border-color;\n    border-left: 0;\n\n    small:after {\n      content: \"\";\n    }\n  }\n}\n\n// Addresses\naddress {\n  margin-bottom: @line-height-computed;\n  line-height: @line-height-base;\n}\n\n// Sup and Sub\nsub,\nsup {\n  font-size: 70%;\n}\n", "// Typography\n\n.text-emphasis-variant(@color) {\n  color: @color;\n  a&:hover {\n    color: darken(@color, 10%);\n  }\n}\n", "// Contextual backgrounds\n\n.bg-variant(@color) {\n  background-color: @color;\n  a&:hover {\n    background-color: darken(@color, 10%);\n  }\n}", "//\n// Code (inline and blocK)\n// --------------------------------------------------\n\n\n// Inline and block code styles\ncode,\nkbd,\npre,\nsamp {\n  font-family: @font-family-monospace;\n}\n\n// Inline code\ncode {\n  padding: 2px 6px;\n  font-size: 85%;\n  color: @code-color;\n  background-color: @code-bg;\n  border-radius: @border-radius-base;\n}\n\n// User input typically entered via keyboard\nkbd {\n  padding: 2px 6px;\n  font-size: 85%;\n  color: @kbd-color;\n  background-color: @kbd-bg;\n  border-radius: @border-radius-base;\n  box-shadow: none;\n}\n\n// Blocks of code\npre {\n  padding: ((@line-height-computed - 6) / 3);\n  margin: 0 0 (@line-height-computed / 2);\n  font-size: (@font-size-base - 5); // 18px to 13px\n  line-height: @line-height-base;\n  color: @pre-color;\n  background-color: @pre-bg;\n  border: 2px solid @pre-border-color;\n  border-radius: @pre-border-radius;\n  white-space: pre;\n}\n\n// Enable scrollable blocks of code\n.pre-scrollable {\n  max-height: @pre-scrollable-max-height;\n}", "//\n// Thumbnails\n// --------------------------------------------------\n\n\n// Mixin and adjust the regular image class\n.thumbnail {\n  display: block;\n  padding: @thumbnail-padding;\n  margin-bottom: 5px;\n  line-height: @line-height-base;\n  background-color: @thumbnail-bg;\n  border: 2px solid @thumbnail-border;\n  border-radius: @thumbnail-border-radius;\n  transition: border .25s ease-in-out;\n\n  > img,\n  a > img {\n    .img-responsive();\n    margin-left: auto;\n    margin-right: auto;\n  }\n\n  // Add a hover state for linked versions only\n  a&:hover,\n  a&:focus,\n  a&.active {\n    border-color: @link-color;\n  }\n\n  // Image captions\n  .caption {\n    padding: @thumbnail-caption-padding;\n    color: @thumbnail-caption-color;\n  }\n}\n", "//\n// Buttons\n// --------------------------------------------------\n\n// Base styles\n// --------------------------------------------------\n\n.btn {\n  border: none;\n  font-size: @btn-font-size-base;\n  font-weight: @btn-font-weight;\n  line-height: @btn-line-height-base;\n  border-radius: @border-radius-base;\n  padding: 10px 15px;\n  -webkit-font-smoothing: subpixel-antialiased;\n  transition: border .25s linear, color .25s linear, background-color .25s linear;\n  \n\n  &:hover,\n  &:focus {\n    outline: none;\n    color: @btn-default-color;    \n  }\n\n  &:active,\n  &.active {\n    outline: none;\n    box-shadow: none;\n  }\n\n  &:focus:active {\n    outline: none;\n  }\n\n  &.disabled,\n  &[disabled],\n  fieldset[disabled] & {\n    background-color: @btn-link-disabled-color;\n    color: fade(@btn-default-color, 75%);\n    .opacity(.7);\n    cursor: @cursor-disabled;\n  }\n\n  // Button icons\n  [class^=\"fui-\"] {\n    margin: 0 1px;\n    position: relative;\n    line-height: 1;\n    top: 1px;\n    \n    .btn-xs& {\n      font-size: 11px;\n      top: 0;\n    }\n    .btn-hg& {\n      top: 2px;\n    } \n  }  \n}\n\n// Alternate buttons\n// --------------------------------------------------\n\n.btn-default {\n  .button-variant(@btn-default-color, @btn-default-bg, @btn-hover-bg, @btn-active-bg);\n}\n.btn-primary {\n  .button-variant(@btn-default-color, @brand-secondary, @btn-primary-hover-bg, @btn-primary-active-bg);\n}\n.btn-info    {\n  .button-variant(@btn-default-color, @brand-info, @btn-info-hover-bg, @btn-info-active-bg);\n}\n.btn-danger  {\n  .button-variant(@btn-default-color, @brand-danger, @btn-danger-hover-bg, @btn-danger-active-bg);\n}\n.btn-success {\n  .button-variant(@btn-default-color, @brand-success, @btn-success-hover-bg, @btn-success-active-bg);\n}\n.btn-warning {\n  .button-variant(@btn-default-color, @brand-warning, @btn-warning-hover-bg, @btn-warning-active-bg);\n}\n.btn-inverse {\n  .button-variant(@btn-default-color, @brand-primary, @btn-inverse-hover-bg, @btn-inverse-active-bg);    \n}\n.btn-embossed {\n  box-shadow: inset 0 -2px 0 fade(black, 15%);\n  \n  &.active,\n  &:active {\n    box-shadow: inset 0 2px 0 fade(black, 15%);\n  }\n}\n.btn-wide {\n  min-width: 140px;\n  padding-left: 30px;\n  padding-right: 30px;\n}\n  \n \n// Link buttons\n// -------------------------\n\n// Make a button look and behave like a link\n.btn-link {\n  color: @link-color;\n\n  &:hover,\n  &:focus {\n    color: @link-hover-color;\n    text-decoration: underline;\n    background-color: transparent;\n  }\n  &[disabled],\n  fieldset[disabled] & {\n    &:hover,\n    &:focus {\n      color: @btn-link-disabled-color;\n      text-decoration: none;\n    }\n  }\n} \n  \n\n// Button sizes\n// --------------------------------------------------\n\n.btn-hg {\n  .button-size(13px, 20px, @btn-font-size-hg, @btn-line-height-hg, @border-radius-large);\n}\n.btn-lg {\n  .button-size(10px, 19px, @btn-font-size-lg, @btn-line-height-lg, @border-radius-large);\n}\n.btn-sm {\n  .button-size(9px, 13px, @btn-font-size-sm, @btn-line-height-sm, @border-radius-base);\n}\n.btn-xs {\n  .button-size(6px, 9px, @btn-font-size-xs, @btn-line-height-xs, @border-radius-small);\n}  \n  \n\n// Button tip\n.btn-tip {\n  font-weight: 300;\n  padding-left: 10px;\n  font-size: 92%;\n}\n\n// Block button\n// --------------------------------------------------\n\n.btn-block {\n  white-space: normal;\n}\n\n// Social Buttons\n// --------------------------------------------------\n\n[class*=\"btn-social-\"] {\n  .button-size(10px, 15px, @btn-social-font-size-base, @btn-social-line-height-base, @border-radius-base);\n}\n\n// Set the backgrounds\n// -------------------------\n.btn-social-pinterest {\n  .social-button-variant(@btn-default-color, @social-pinterest);\n}\n.btn-social-linkedin {\n  .social-button-variant(@btn-default-color, @social-linkedin)\n}\n.btn-social-stumbleupon {\n  .social-button-variant(@btn-default-color, @social-stumbleupon);\n}\n.btn-social-googleplus {\n  .social-button-variant(@btn-default-color, @social-googleplus);\n}\n.btn-social-facebook {\n  .social-button-variant(@btn-default-color, @social-facebook);\n}\n.btn-social-twitter {\n  .social-button-variant(@btn-default-color, @social-twitter);\n}", "// Opacity\n\n.opacity(@opacity) {\n  opacity: @opacity;\n  // IE8 filter\n  @opacity-ie: (@opacity * 100);\n  filter: ~\"alpha(opacity=@{opacity-ie})\";\n}\n", "// Button variants\n//\n.button-variant(@color; @background; @hover-background; @active-background; @disabled-background: @gray-light) {\n  color: @color;\n  background-color: @background;\n\n  &:hover,\n  &.hover,\n  &:focus,\n  &:active,\n  &.active,\n  .open > .dropdown-toggle& {\n    color: @color;\n    background-color: @hover-background;\n    border-color: @hover-background;\n  }\n  &:active,\n  &.active,\n  .open > .dropdown-toggle& {\n    background: @active-background;\n    border-color: @active-background;\n  }\n  &.disabled,\n  &[disabled],\n  fieldset[disabled] & {\n    &,\n    &:hover,\n    &.hover,\n    &:focus,\n    &:active,\n    &.active {\n      background-color: @disabled-background;\n      border-color: @background;\n    }\n  }\n\n  .badge {\n    color: @background;\n    background-color: @inverse;\n  }\n}\n\n// Button sizes\n.button-size(@padding-vertical; @padding-horizontal; @font-size; @line-height; @border-radius) {\n  padding: @padding-vertical @padding-horizontal;\n  font-size: @font-size;\n  line-height: @line-height;\n  border-radius: @border-radius;\n}\n\n// Social button variants\n.social-button-variant(@color; @background) {\n  color: @color;\n  background-color: @background;\n\n  &:hover,\n  &:focus {\n    background-color: mix(@background, white, 80%);\n  }\n  &:active,\n  &.active {\n    background-color: mix(@background, black, 85%);\n  }\n}\n", "//\n// Button groups\n// --------------------------------------------------\n\n\n.btn-group {\n  > .btn {\n    & + .btn {\n      margin-left: 0;\n    }\n    & + .dropdown-toggle {\n      border-left: 2px solid fade(@brand-primary, 15%);\n      padding: 10px 12px;\n\n      .caret {\n        margin-left: 3px;\n        margin-right: 3px;\n      }\n    }\n    &.btn-gh + .dropdown-toggle {\n      .caret {\n        margin-left: 7px;\n        margin-right: 7px;\n      }\n    }\n    &.btn-sm + .dropdown-toggle {\n      .caret {\n        margin-left: 0;\n        margin-right: 0;\n      }\n    }\n  }\n}\n\n.dropdown-toggle {\n  .caret {\n    margin-left: 8px;\n  }\n}\n\n// Sizing\n//\n// Remix the default button sizing classes into new ones for easier manipulation.\n\n.btn-group-xs > .btn {\n  &:extend(.btn-xs);\n\n  & + .dropdown-toggle {\n    padding: 6px 9px;\n  }\n}\n\n.btn-group-sm > .btn {\n  &:extend(.btn-sm);\n\n  & + .dropdown-toggle {\n    padding: 9px 13px;\n  }\n}\n\n.btn-group-lg > .btn {\n  &:extend(.btn-lg);\n\n  & + .dropdown-toggle {\n    padding: 10px 19px;\n  }\n}\n\n.btn-group-hg > .btn {\n  &:extend(.btn-hg);\n\n  & + .dropdown-toggle {\n    padding: 13px 20px;\n  }\n}\n\n\n\n// Carets in other button sizes\n.btn-xs .caret {\n  border-width: @caret-width-xs-vertical @caret-width-xs 0;\n  border-bottom-width: 0;\n}\n.btn-lg .caret {\n  border-width: @caret-width-base-vertical @caret-width-base 0;\n  border-bottom-width: 0;\n}\n// Upside down carets for .dropup\n.dropup .btn-lg .caret {\n  border-width: 0 @caret-width-base @caret-width-base-vertical;\n}\n.dropup .btn-xs .caret {\n  border-width: 0 @caret-width-xs @caret-width-xs-vertical;\n}\n\n.btn-group > .btn,\n.btn-group > .dropdown-menu,\n.btn-group > .popover {\n  font-weight: 400;\n}\n\n.btn-group:focus .dropdown-toggle {\n  outline: none;\n  transition: .25s;\n}\n\n// The clickable button for toggling the menu\n// Remove the gradient and set the same inset shadow as the :active state\n.btn-group.open .dropdown-toggle {\n  color: fade(@btn-default-color, 75%);\n  box-shadow: none;\n}\n\n// Other button locations\n// Button with icon inside\n.btn-toolbar .btn {\n  &.active {\n    color: @btn-default-color;\n  }\n  > [class^=\"fui-\"] {\n    font-size: @icon-normal;\n    margin: 0 1px;\n  }\n}\n", "//\n// Forms\n// --------------------------------------------------\n\n\n// Normalize non-controls\n//\n// Restyle and baseline non-control form elements.\n\nlegend {\n  display: block;\n  width: 100%;\n  padding: 0;\n  margin-bottom: @line-height-computed / 2;\n  font-size: (@component-font-size-base * 1.6); // ~24px\n  line-height: inherit;\n  color: @legend-color;\n  border-bottom: none;\n}\n\n// Normalize form controls\n\n// Textarea\ntextarea {\n  font-size: ceil((@font-size-base * 1.071)); // ~15px\n  line-height: 24px;\n  padding: 5px 11px;\n}\n\n// Search\ninput[type=\"search\"] {\n  -webkit-appearance: none !important; // removes rounded corners for searchfields on iOS\n}\n\n\n// Label\nlabel {\n  font-weight: normal;\n  font-size: @component-font-size-base;\n  line-height: 2.3; // ~36px\n}\n\n// Placeholder\n//\n// Placeholder text gets special styles because when browsers invalidate entire\n// lines if it doesn't understand a selector/\n.form-control {\n  .placeholder(desaturate(lighten(@brand-primary, 45%), 15%));\n}\n\n// Common form controls\n//\n// Shared size and type resets for form controls. Apply `.form-control` to any\n// of the following form controls:\n//\n// select\n// textarea\n// input[type=\"text\"]\n// input[type=\"password\"]\n// input[type=\"datetime\"]\n// input[type=\"datetime-local\"]\n// input[type=\"date\"]\n// input[type=\"month\"]\n// input[type=\"time\"]\n// input[type=\"week\"]\n// input[type=\"number\"]\n// input[type=\"email\"]\n// input[type=\"url\"]\n// input[type=\"search\"]\n// input[type=\"tel\"]\n// input[type=\"color\"]\n\n.form-control {\n  border: 2px solid @gray-light;\n  color: @brand-primary;\n  font-family: @font-family-base;\n  font-size: @input-font-size-base;\n  line-height: @input-line-height-base;\n  padding: 8px 12px;\n  height: 42px;\n  border-radius: @input-border-radius;\n  box-shadow: none;\n  transition: border .25s linear, color .25s linear, background-color .25s linear;\n\n  // Customize the `:focus` state\n  .form-control-focus();\n\n  // Disabled and read-only inputs\n  // Note: HTML5 says that controls under a fieldset > legend:first-child won't\n  // be disabled if the fieldset is disabled. Due to implementation difficulty,\n  // we don't honor that edge case; we style them as disabled anyway.\n  &[disabled],\n  &[readonly],\n  fieldset[disabled] & {\n    background-color: @input-bg-disabled;\n    border-color: mix(@gray, white, 40%);\n    color: mix(@gray, white, 40%);\n    cursor: default;\n    .opacity(.7);\n  }\n\n  // Flat (without border)\n  &.flat {\n    border-color: transparent;\n\n    &:hover {\n      border-color: @gray-light;\n    }\n    &:focus {\n      border-color: @brand-secondary;\n    }\n  }\n}\n\n// Form control sizing\n.input-sm,\n.form-group-sm .form-control {  \n  .input-size(@input-height-sm; 6px; 10px; @input-font-size-sm; @input-line-height-sm);\n}\n\n.input-lg,\n.form-group-lg .form-control {  \n  .input-size(@input-height-lg; 10px; 15px; @input-font-size-lg; @input-line-height-lg);\n}\n\n.input-hg,\n.form-group-hg .form-control {  \n  .input-size(@input-height-hg; 10px; 16px; @input-font-size-hg; @input-line-height-hg);\n}\n\n// Form control feedback states\n//\n// Apply contextual and semantic states to individual form controls.\n\n.form-control-feedback {\n  position: absolute;\n  top: 2px;\n  right: 2px;\n  margin-top: 1px;\n  line-height: 36px;\n  font-size: @input-icon-font-size;\n  color: desaturate(lighten(@brand-primary, 45%), 15%);\n  background-color: transparent;\n  padding: 0 12px 0 0;\n  border-radius: @input-border-radius;\n  pointer-events: none;\n}\n\n.input-hg + .form-control-feedback, \n.control-feedback-hg {\n  font-size: @input-icon-font-size-hg;\n  line-height: 48px;\n  padding-right: 16px;\n  width: auto;\n  height: 48px;\n}\n.input-lg + .form-control-feedback,\n.control-feedback-lg {\n  font-size: @input-icon-font-size-lg;\n  line-height: 40px;\n  width: auto;\n  height: 40px;\n  padding-right: 15px;\n}\n.input-sm + .form-control-feedback,\n.control-feedback-sm {\n  line-height: 29px;\n  height: 29px;\n  width: auto;\n  padding-right: 10px;\n}\n\n// Feedback states\n.has-success {\n  .form-control-validation(@brand-success; @brand-success);\n}\n.has-warning {\n  .form-control-validation(@brand-warning; @brand-warning);\n}\n.has-error {\n  .form-control-validation(@brand-danger; @brand-danger);\n}\n\n.form-control[disabled] + .form-control-feedback,\n.form-control[readonly] + .form-control-feedback,\nfieldset[disabled] .form-control + .form-control-feedback,\n.form-control.disabled + .form-control-feedback {\n  cursor: @cursor-disabled;\n  color: mix(@gray, white, 40%);\n  background-color: transparent;\n  .opacity(.7);\n}\n\n\n// Help text\n//\n// Apply to any element you wish to create light text for placement immediately\n// below a form control. Use for general help, formatting, or instructional text.\n\n.help-block {\n  font-size: ceil((@component-font-size-base * 0.933));\n  margin-bottom: 5px;\n  color: mix(@brand-primary, @gray-light, 60%);\n}\n\n// Form groups\n//\n// Designed to help with the organization and spacing of vertical forms. For\n// horizontal forms, use the predefined grid classes.\n\n.form-group {\n  position: relative;\n  margin-bottom: 20px;\n}\n\n// Horizontal forms\n//\n// Horizontal forms are built on grid classes and allow you to create forms with\n// labels on the left and inputs on the right.\n\n.form-horizontal {\n\n  // Consistent vertical alignment of radios and checkboxes\n  //\n  // Labels also get some reset styles, but that is scope to a media query below.\n  .radio,\n  .checkbox,\n  .radio-inline,\n  .checkbox-inline {\n    margin-top: 0;\n    margin-bottom: 0;\n    padding-top: 0;\n  }\n\n  // Reset spacing and right align labels, but scope to media queries so that\n  // labels on narrow viewports stack the same as a default form example.\n  @media (min-width: @screen-sm-min) {\n    .control-label {\n      padding-top: 3px;\n      padding-bottom: 3px;\n    }\n  }\n\n  // Make form groups behave like rows\n  .form-group {\n    .make-row();\n  }\n\n  .form-control-static {\n    padding-top: 6px;\n    padding-bottom: 6px;\n  }\n\n  // Form group sizes\n  //\n  // Quick utility class for applying `.input-lg` and `.input-sm` styles to the\n  // inputs and labels within a `.form-group`.\n  .form-group-hg {\n    @media (min-width: @screen-sm-min) {\n      .control-label {\n        font-size: @input-font-size-hg;\n        padding-top: 2px;\n        padding-bottom: 0;\n      }\n    }\n    .form-control {\n      &:extend(.input-hg);\n    }\n  }\n  .form-group-lg {\n    @media (min-width: @screen-sm-min) {\n      .control-label {\n        font-size: @input-font-size-lg;\n        padding-top: 3px;\n        padding-bottom: 2px;\n      }\n    }\n  }\n  .form-group-sm {\n    @media (min-width: @screen-sm-min) {\n      .control-label {\n        font-size: @input-font-size-sm;\n        padding-top: 2px;\n        padding-bottom: 2px;\n      }\n    }\n  }\n}\n", "// Vendor Prefixes\n//\n// ##\n\n// - Animations\n// - Backface visibility\n// - Box shadow\n// - Box sizing\n// - Content columns\n// - Hyphens\n// - Placeholder text\n// - Transformations\n// - Transitions\n// - User Select\n\n\n// Animations\n.animation(@animation) {\n  -webkit-animation: @animation;\n       -o-animation: @animation;\n          animation: @animation;\n}\n.animation-name(@name) {\n  -webkit-animation-name: @name;\n          animation-name: @name;\n}\n.animation-duration(@duration) {\n  -webkit-animation-duration: @duration;\n          animation-duration: @duration;\n}\n.animation-timing-function(@timing-function) {\n  -webkit-animation-timing-function: @timing-function;\n          animation-timing-function: @timing-function;\n}\n.animation-delay(@delay) {\n  -webkit-animation-delay: @delay;\n          animation-delay: @delay;\n}\n.animation-iteration-count(@iteration-count) {\n  -webkit-animation-iteration-count: @iteration-count;\n          animation-iteration-count: @iteration-count;\n}\n.animation-direction(@direction) {\n  -webkit-animation-direction: @direction;\n          animation-direction: @direction;\n}\n.animation-fill-mode(@fill-mode) {\n  -webkit-animation-fill-mode: @fill-mode;\n          animation-fill-mode: @fill-mode;\n}\n\n// Backface visibility\n// Prevent browsers from flickering when using CSS 3D transforms.\n// Default value is `visible`, but can be changed to `hidden`\n\n.backface-visibility(@visibility){\n  -webkit-backface-visibility: @visibility;\n     -moz-backface-visibility: @visibility;\n          backface-visibility: @visibility;\n}\n\n// Drop shadows\n//\n// Note: Deprecated `.box-shadow()` as of v3.1.0 since all of Bootstrap's\n// supported browsers that have box shadow capabilities now support it.\n\n.box-shadow(@shadow) {\n  -webkit-box-shadow: @shadow; // iOS <4.3 & Android <4.1\n          box-shadow: @shadow;\n}\n\n// Box sizing\n.box-sizing(@boxmodel) {\n  -webkit-box-sizing: @boxmodel;\n     -moz-box-sizing: @boxmodel;\n          box-sizing: @boxmodel;\n}\n\n// CSS3 Content Columns\n.content-columns(@column-count; @column-gap: @grid-gutter-width) {\n  -webkit-column-count: @column-count;\n     -moz-column-count: @column-count;\n          column-count: @column-count;\n  -webkit-column-gap: @column-gap;\n     -moz-column-gap: @column-gap;\n          column-gap: @column-gap;\n}\n\n// Optional hyphenation\n.hyphens(@mode: auto) {\n  word-wrap: break-word;\n  -webkit-hyphens: @mode;\n     -moz-hyphens: @mode;\n      -ms-hyphens: @mode; // IE10+\n       -o-hyphens: @mode;\n          hyphens: @mode;\n}\n\n// Placeholder text\n.placeholder(@color: @input-color-placeholder) {\n  &::-moz-placeholder           { color: @color;   // Firefox\n                                  opacity: 1; } // See https://github.com/twbs/bootstrap/pull/11526\n  &:-ms-input-placeholder       { color: @color; } // Internet Explorer 10+\n  &::-webkit-input-placeholder  { color: @color; } // Safari and Chrome\n}\n\n// Transformations\n.scale(@ratio) {\n  -webkit-transform: scale(@ratio);\n      -ms-transform: scale(@ratio); // IE9 only\n       -o-transform: scale(@ratio);\n          transform: scale(@ratio);\n}\n.scale(@ratioX; @ratioY) {\n  -webkit-transform: scale(@ratioX, @ratioY);\n      -ms-transform: scale(@ratioX, @ratioY); // IE9 only\n       -o-transform: scale(@ratioX, @ratioY);\n          transform: scale(@ratioX, @ratioY);\n}\n.scaleX(@ratio) {\n  -webkit-transform: scaleX(@ratio);\n      -ms-transform: scaleX(@ratio); // IE9 only\n       -o-transform: scaleX(@ratio);\n          transform: scaleX(@ratio);\n}\n.scaleY(@ratio) {\n  -webkit-transform: scaleY(@ratio);\n      -ms-transform: scaleY(@ratio); // IE9 only\n       -o-transform: scaleY(@ratio);\n          transform: scaleY(@ratio);\n}\n.skew(@x; @y) {\n  -webkit-transform: skewX(@x) skewY(@y);\n      -ms-transform: skewX(@x) skewY(@y); // See https://github.com/twbs/bootstrap/issues/4885; IE9+\n       -o-transform: skewX(@x) skewY(@y);\n          transform: skewX(@x) skewY(@y);\n}\n.translate(@x; @y) {\n  -webkit-transform: translate(@x, @y);\n      -ms-transform: translate(@x, @y); // IE9 only\n       -o-transform: translate(@x, @y);\n          transform: translate(@x, @y);\n}\n.translate3d(@x; @y; @z) {\n  -webkit-transform: translate3d(@x, @y, @z);\n          transform: translate3d(@x, @y, @z);\n}\n.rotate(@degrees) {\n  -webkit-transform: rotate(@degrees);\n      -ms-transform: rotate(@degrees); // IE9 only\n       -o-transform: rotate(@degrees);\n          transform: rotate(@degrees);\n}\n.rotateX(@degrees) {\n  -webkit-transform: rotateX(@degrees);\n      -ms-transform: rotateX(@degrees); // IE9 only\n       -o-transform: rotateX(@degrees);\n          transform: rotateX(@degrees);\n}\n.rotateY(@degrees) {\n  -webkit-transform: rotateY(@degrees);\n      -ms-transform: rotateY(@degrees); // IE9 only\n       -o-transform: rotateY(@degrees);\n          transform: rotateY(@degrees);\n}\n.perspective(@perspective) {\n  -webkit-perspective: @perspective;\n     -moz-perspective: @perspective;\n          perspective: @perspective;\n}\n.perspective-origin(@perspective) {\n  -webkit-perspective-origin: @perspective;\n     -moz-perspective-origin: @perspective;\n          perspective-origin: @perspective;\n}\n.transform-origin(@origin) {\n  -webkit-transform-origin: @origin;\n     -moz-transform-origin: @origin;\n      -ms-transform-origin: @origin; // IE9 only\n          transform-origin: @origin;\n}\n\n\n// Transitions\n\n.transition(@transition) {\n  -webkit-transition: @transition;\n       -o-transition: @transition;\n          transition: @transition;\n}\n.transition-property(@transition-property) {\n  -webkit-transition-property: @transition-property;\n          transition-property: @transition-property;\n}\n.transition-delay(@transition-delay) {\n  -webkit-transition-delay: @transition-delay;\n          transition-delay: @transition-delay;\n}\n.transition-duration(@transition-duration) {\n  -webkit-transition-duration: @transition-duration;\n          transition-duration: @transition-duration;\n}\n.transition-timing-function(@timing-function) {\n  -webkit-transition-timing-function: @timing-function;\n          transition-timing-function: @timing-function;\n}\n.transition-transform(@transition) {\n  -webkit-transition: -webkit-transform @transition;\n     -moz-transition: -moz-transform @transition;\n       -o-transition: -o-transform @transition;\n          transition: transform @transition;\n}\n\n\n// User select\n// For selecting text on the page\n\n.user-select(@select) {\n  -webkit-user-select: @select;\n     -moz-user-select: @select;\n      -ms-user-select: @select; // IE10+\n          user-select: @select;\n}\n", "// Form validation states\n//\n// Used in forms.less to generate the form validation CSS for warnings, errors,\n// and successes.\n\n.form-control-validation(@text-color: @brand-primary; @border-color: @gray-light; @background-color: @inverse) {\n  // Color the label and help text\n  .help-block,\n  .control-label,\n  .radio,\n  .checkbox,\n  .radio-inline,\n  .checkbox-inline {\n    color: @text-color;\n  }\n  // Set the border and box shadow on specific inputs to match\n  .form-control {\n    color: @text-color;\n    border-color: @border-color;\n    box-shadow: none;\n    .placeholder(@text-color);\n\n    &:focus {\n      border-color: @border-color;\n      box-shadow: none;\n    }\n  }\n  // Set validation states also for addons\n  .input-group-addon {\n    color: @text-color;\n    border-color: @border-color;\n    background-color: @background-color;\n  }\n  .form-control-feedback {\n    color: @text-color;\n  }\n}\n\n// Form control focus state\n//\n// Generate a customized focus state and for any input with the specified color,\n// which defaults to the `@input-focus-border` variable.\n//\n// We highly encourage you to not customize the default value, but instead use\n// this to tweak colors on an as-needed basis. This aesthetic change is based on\n// WebKit's default styles, but applicable to a wider range of browsers. Its\n// usability and accessibility should be taken into account with any change.\n//\n// Example usage: change the default blue border and shadow to white for better\n// contrast against a dark gray background.\n\n.form-control-focus(@color: @brand-secondary) {\n  .form-group.focus &,\n  &:focus {\n    border-color: @color;\n    outline: 0;\n    box-shadow: none;\n  }\n}\n\n// Form control sizing\n//\n// Relative text size, padding, and border-radii changes for form controls. For\n// horizontal sizing, wrap controls in the predefined grid classes. `<select>`\n// element gets special love because it's special, and that's a fact!\n\n.input-size(@input-height; @padding-vertical; @padding-horizontal; @font-size; @line-height; @border-radius: @border-radius-large) {\n  height: @input-height;\n  padding: @padding-vertical @padding-horizontal;\n  font-size: @font-size;\n  line-height: @line-height;\n  border-radius: @border-radius;\n\n  select& {\n    height: @input-height;\n    line-height: @input-height;\n  }\n\n  textarea&,\n  select[multiple]& {\n    height: auto;\n  }\n}\n\n// Reset rounded corners for form controls\n//\n\n.form-controls-corners-reset() {\n  .input-group .form-control:first-child,\n  .input-group-addon:first-child,\n  .input-group-btn:first-child > .btn,\n  .input-group-btn:first-child > .dropdown-toggle,\n  .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle) {\n    .border-right-radius(0);\n  }\n  .input-group .form-control:last-child,\n  .input-group-addon:last-child,\n  .input-group-btn:last-child > .btn,\n  .input-group-btn:last-child > .dropdown-toggle,\n  .input-group-btn:first-child > .btn:not(:first-child) {\n    .border-left-radius(0);\n  }\n}\n", "// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n// Centered container element\n.container-fixed() {\n  margin-right: auto;\n  margin-left: auto;\n  padding-left:  (@grid-gutter-width / 2);\n  padding-right: (@grid-gutter-width / 2);\n  &:extend(.clearfix all);\n}\n\n// Creates a wrapper for a series of columns\n.make-row(@gutter: @grid-gutter-width) {\n  margin-left:  (@gutter / -2);\n  margin-right: (@gutter / -2);\n  &:extend(.clearfix all);\n}\n\n// Generate the extra small columns\n.make-xs-column(@columns; @gutter: @grid-gutter-width) {\n  position: relative;\n  float: left;\n  width: percentage((@columns / @grid-columns));\n  min-height: 1px;\n  padding-left:  (@gutter / 2);\n  padding-right: (@gutter / 2);\n}\n.make-xs-column-offset(@columns) {\n  margin-left: percentage((@columns / @grid-columns));\n}\n.make-xs-column-push(@columns) {\n  left: percentage((@columns / @grid-columns));\n}\n.make-xs-column-pull(@columns) {\n  right: percentage((@columns / @grid-columns));\n}\n\n// Generate the small columns\n.make-sm-column(@columns; @gutter: @grid-gutter-width) {\n  position: relative;\n  min-height: 1px;\n  padding-left:  (@gutter / 2);\n  padding-right: (@gutter / 2);\n\n  @media (min-width: @screen-sm-min) {\n    float: left;\n    width: percentage((@columns / @grid-columns));\n  }\n}\n.make-sm-column-offset(@columns) {\n  @media (min-width: @screen-sm-min) {\n    margin-left: percentage((@columns / @grid-columns));\n  }\n}\n.make-sm-column-push(@columns) {\n  @media (min-width: @screen-sm-min) {\n    left: percentage((@columns / @grid-columns));\n  }\n}\n.make-sm-column-pull(@columns) {\n  @media (min-width: @screen-sm-min) {\n    right: percentage((@columns / @grid-columns));\n  }\n}\n\n// Generate the medium columns\n.make-md-column(@columns; @gutter: @grid-gutter-width) {\n  position: relative;\n  min-height: 1px;\n  padding-left:  (@gutter / 2);\n  padding-right: (@gutter / 2);\n\n  @media (min-width: @screen-md-min) {\n    float: left;\n    width: percentage((@columns / @grid-columns));\n  }\n}\n.make-md-column-offset(@columns) {\n  @media (min-width: @screen-md-min) {\n    margin-left: percentage((@columns / @grid-columns));\n  }\n}\n.make-md-column-push(@columns) {\n  @media (min-width: @screen-md-min) {\n    left: percentage((@columns / @grid-columns));\n  }\n}\n.make-md-column-pull(@columns) {\n  @media (min-width: @screen-md-min) {\n    right: percentage((@columns / @grid-columns));\n  }\n}\n\n// Generate the large columns\n.make-lg-column(@columns; @gutter: @grid-gutter-width) {\n  position: relative;\n  min-height: 1px;\n  padding-left:  (@gutter / 2);\n  padding-right: (@gutter / 2);\n\n  @media (min-width: @screen-lg-min) {\n    float: left;\n    width: percentage((@columns / @grid-columns));\n  }\n}\n.make-lg-column-offset(@columns) {\n  @media (min-width: @screen-lg-min) {\n    margin-left: percentage((@columns / @grid-columns));\n  }\n}\n.make-lg-column-push(@columns) {\n  @media (min-width: @screen-lg-min) {\n    left: percentage((@columns / @grid-columns));\n  }\n}\n.make-lg-column-pull(@columns) {\n  @media (min-width: @screen-lg-min) {\n    right: percentage((@columns / @grid-columns));\n  }\n}\n", "//\n// Input groups\n// --------------------------------------------------\n\n\n// Base styles\n// -------------------------\n.input-group {\n   .form-control {\n    position: static;\n  }\n}\n\n// Sizing options\n//\n// Remix the default form control sizing classes into new ones for easier\n// manipulation.\n\n.input-group-hg > .form-control,\n.input-group-hg > .input-group-addon,\n.input-group-hg > .input-group-btn > .btn { .input-hg(); }\n.input-group-lg > .form-control,\n.input-group-lg > .input-group-addon,\n.input-group-lg > .input-group-btn > .btn { .input-lg(); }\n.input-group-sm > .form-control,\n.input-group-sm > .input-group-addon,\n.input-group-sm > .input-group-btn > .btn { .input-sm(); }\n\n// Text input groups\n// -------------------------\n.input-group-addon {\n  padding: 10px 12px;\n  font-size: @component-font-size-base;\n  color: @inverse;\n  text-align: center;\n  background-color: @gray-light;\n  border: 2px solid @gray-light;\n  border-radius: @border-radius-large;\n  transition: border .25s linear, color .25s linear, background-color .25s linear;\n\n  .input-group-hg &,\n  .input-group-lg &,\n  .input-group-sm &, {\n    line-height: 1;\n  }\n}\n\n// Reset rounded corners\n.form-controls-corners-reset();\n\n\n\n// Focus State\n// -------------------------\n.form-group.focus,\n.input-group.focus {\n  .input-group-addon {\n    background-color: @brand-secondary;\n    border-color: @brand-secondary;\n  }\n  .input-group-btn {\n    > .btn-default + .btn-default {\n      border-left-color: mix(@brand-secondary, black, 85%);\n    }\n    .btn {\n      border-color: @brand-secondary;\n      background-color: @inverse;\n      color: @brand-secondary;\n    }\n    .btn-default {\n      .button-variant(@btn-default-color, @brand-secondary, @btn-primary-hover-bg, @btn-primary-active-bg);\n    }\n  }\n}\n\n// Button input groups\n// -------------------------\n.input-group-btn {\n  .btn {\n    background-color: @inverse;\n    border: 2px solid @gray-light;\n    color: @gray-light;\n    line-height: 18px;\n    height: 42px;\n  }\n  .btn-default {\n    .button-variant(@btn-default-color, @gray-light, @btn-hover-bg, @btn-active-bg);\n  }\n  .input-group-hg & .btn {\n    line-height: 31px;\n  }\n  .input-group-lg & .btn {\n    line-height: 21px;\n  }\n  .input-group-sm & .btn {\n    line-height: 19px;\n  }\n  &:first-child > .btn {\n    border-right-width: 0;\n    margin-right: -3px;\n  }\n  &:last-child > .btn {\n    border-left-width: 0;\n    margin-left: -3px;\n  }\n  & > .btn-default + .btn-default {\n    border-left: 2px solid @gray-light;\n  }\n  & > .btn:first-child + .btn {\n    .caret {\n      margin-left: 0;\n    }\n  }\n}\n\n\n// Rounded input groups\n// -------------------------\n.input-group-rounded {\n  .input-group-btn + .form-control,\n  .input-group-btn:last-child .btn {\n    .border-right-radius(20px);\n\n    .input-group-hg& {\n      .border-right-radius(27px);\n    }\n    .input-group-lg& {\n      .border-right-radius(25px);\n    }\n  }\n  .form-control:first-child,\n  .input-group-btn:first-child .btn {\n    .border-left-radius(20px);\n\n    .input-group-hg& {\n      .border-left-radius(27px);\n    }\n    .input-group-lg& {\n      .border-left-radius(25px);\n    }\n  }\n  // Remove left padding for .form-control after button\n  .input-group-btn + .form-control {\n    padding-left: 0;\n  }\n}\n", "// Single side border-radius\n\n.border-top-radius(@radius) {\n  border-top-right-radius: @radius;\n   border-top-left-radius: @radius;\n}\n.border-right-radius(@radius) {\n  border-bottom-right-radius: @radius;\n     border-top-right-radius: @radius;\n}\n.border-bottom-radius(@radius) {\n  border-bottom-right-radius: @radius;\n   border-bottom-left-radius: @radius;\n}\n.border-left-radius(@radius) {\n  border-bottom-left-radius: @radius;\n     border-top-left-radius: @radius;\n}\n", "//\n// Checkbox & Radio\n// --------------------------------------------------\n\n.checkbox,\n.radio {\n  margin-bottom: 12px;\n  padding-left: 32px;\n  position: relative;\n  transition: color .25s linear;\n  font-size: ceil((@component-font-size-base * 0.933)); // ~14px\n  line-height: 1.5; // 21px;\n\n  .icons {\n    color: @gray-light;\n    display: block;\n    height: 20px;\n    top: 0;\n    left: 0;\n    position: absolute;\n    width: 20px;\n    text-align: center;\n    line-height: 20px;\n    font-size: 20px;\n    cursor: pointer;\n\n    .icon-checked {\n      .opacity(0);\n    }\n  }\n}\n\n.checkbox,\n.radio {\n  .icon-checked,\n  .icon-unchecked {\n    display: inline-table;\n    position: absolute;\n    left: 0;\n    top: 0;\n    background-color: transparent;\n    margin: 0;\n    opacity: 1;\n    filter: none;\n    transition: color .25s linear;\n\n    &:before {\n      font-family: 'Flat-UI-Icons';\n      speak: none;\n      font-style: normal;\n      font-weight: normal;\n      font-variant: normal;\n      text-transform: none;\n      -webkit-font-smoothing: antialiased;\n      -moz-osx-font-smoothing: grayscale;\n    }\n  }\n}\n.checkbox {\n  .icon-checked:before {\n    content: \"\\e60e\";\n  }\n  .icon-unchecked:before {\n    content: \"\\e60d\";\n  }\n}\n.radio {\n  .icon-checked:before {\n    content: \"\\e60c\";\n  }\n  .icon-unchecked:before {\n    content: \"\\e60b\";\n  }\n}\n\n.checkbox,\n.radio {\n  input[type=\"checkbox\"].custom-checkbox,\n  input[type=\"radio\"].custom-radio {\n    outline: none !important;\n    opacity: 0;\n    position: absolute;\n    margin: 0;\n    padding: 0;\n    left: 0;\n    top: 0;\n    width: 20px;\n    height: 20px;\n\n\n    // Alternate States\n    // --------------------------------------------------\n\n    &:hover:not(.nohover):not(:disabled) + .icons {\n      .icon-unchecked {\n        .opacity(0);\n      }\n      .icon-checked {\n        opacity: 1;\n        filter: none;\n      }\n    }\n\n    &:checked + .icons {\n      color: @brand-secondary;\n\n      .icon-unchecked {\n        .opacity(0);\n      }\n      .icon-checked {\n        opacity: 1;\n        filter: none;\n        color: @brand-secondary;\n      }\n    }\n\n    &:disabled + .icons {\n      cursor: default;\n      color: mix(@gray-light, white, 38%);\n\n      .icon-unchecked {\n        opacity: 1;\n        filter: none;\n      }\n      .icon-checked {\n        .opacity(0);\n      }\n    }\n\n    &:disabled:checked + .icons {\n      color: mix(@gray-light, white, 38%);\n\n      .icon-unchecked {\n        .opacity(0);\n      }\n      .icon-checked {\n        opacity: 1;\n        filter: none;\n        color: mix(@gray-light, white, 38%);\n      }\n    }\n\n    &:indeterminate + .icons {\n      color: @gray-light;\n\n      .icon-unchecked {\n        opacity: 1;\n        filter: none;\n      }\n      .icon-checked {\n        .opacity(0);\n      }\n      &:before {\n        content: \"\\2013\";\n        position: absolute;\n        top: 0;\n        left: 0;\n        line-height: 20px;\n        width: 20px;\n        text-align: center;\n        color: @inverse;\n        font-size: 22px;\n        z-index: 10;\n      }\n\n    }\n  }\n\n  // Alternate Color\n  // --------------------------------------------------\n\n  // Primary\n\n  &.primary input[type=\"checkbox\"].custom-checkbox,\n  &.primary input[type=\"radio\"].custom-radio {\n    & + .icons {\n      color: @brand-primary;\n    }\n    // Checked State\n    &:checked + .icons {\n      color: @brand-secondary;\n    }\n    // Disabled state\n    &:disabled + .icons {\n      cursor: default;\n      color: @gray-light;\n\n      &.checked {\n        color: @gray-light;\n      }\n    }\n\n    &:indeterminate + .icons {\n      color: @brand-primary;\n    }\n\n  }\n}\n\n// Group Addon\n.radio,\n.checkbox {\n  .input-group-addon & {\n    margin: -2px 0;\n    padding-left: 20px;\n\n    .icons {\n      color: mix(@gray-light, white, 38%);\n    }\n    input[type=\"checkbox\"].custom-checkbox,\n    input[type=\"radio\"].custom-radio {\n      &:checked + .icons {\n        color: @inverse;\n\n        .icon-checked {\n          color: @inverse;\n        }\n      }\n      &:disabled + .icons {\n        color: fade(mix(@gray-light, white, 38%), 60%);\n      }\n      &:disabled:checked + .icons {\n        color: fade(mix(@gray-light, white, 38%), 60%);\n\n        .icon-checked {\n          color: fade(mix(@gray-light, white, 38%), 60%);\n        }\n      }\n    }\n  }\n}\n\n.radio + .radio,\n.checkbox + .checkbox {\n  margin-top: 10px;\n}\n\n// Form inline style\n\n.form-inline .checkbox, .form-inline .radio {\n  padding-left: 32px;\n}\n", "//\n// Tags Input\n// --------------------------------------------------\n\n.bootstrap-tagsinput {\n  background-color: @tagsinput-container-bg;\n  border: 2px solid @tagsinput-container-border-color;\n  border-radius: @tagsinput-container-border-radius;\n  margin-bottom: 18px;\n  padding: 6px 1px 1px 6px;\n  text-align: left;\n  font-size: 0;\n\n  .tag {\n    border-radius: @tagsinput-tag-border-radius;\n    background-color: @tagsinput-tag-bg;\n    color: @tagsinput-tag-color;\n    font-size: floor((@component-font-size-base * 0.886)); // ~13px\n    cursor: pointer;\n    display: inline-block;\n    position: relative;\n    vertical-align: middle;\n    overflow: hidden;\n    margin: 0 7px 7px 0;\n    line-height: 15px;\n    height: 27px;\n    padding: 6px 21px;\n    transition: .25s linear;\n\n    > span {\n      color: @tagsinput-tag-icon-color;\n      cursor: pointer;\n      font-size: 12px;\n      position: absolute;\n      right: 0;\n      text-align: right;\n      text-decoration: none;\n      top: 0;\n      width: 100%;\n      bottom: 0;\n      padding: 0 10px 0 0;\n      z-index: 2;\n      .opacity(0);\n      transition: opacity .25s linear;\n\n      &:after {\n        content: \"\\e609\";\n        font-family: \"Flat-UI-Icons\";\n        -webkit-font-smoothing: antialiased;\n        -moz-osx-font-smoothing: grayscale;\n        line-height: 27px;\n      }\n    }\n\n    &:hover {\n      background-color: @tagsinput-tag-hover-bg;\n      color: @tagsinput-tag-hover-color;\n      padding-right: 28px;\n      padding-left: 14px;\n\n      > span {\n        opacity: 1;\n        filter: none;\n      }\n    }\n  }\n\n  input[type=\"text\"] {\n    font-size: ceil((@component-font-size-base * 0.933)); // 14px;\n    border: none;\n    box-shadow: none;\n    outline: none;\n    background-color: transparent;\n    padding: 0;\n    margin: 0;\n    width: auto !important;\n    max-width: inherit;\n    min-width: 80px;\n    vertical-align: top;\n    height: 29px;\n    color: @tagsinput-input-color;\n\n    &:first-child {\n      height: 23px;\n      margin: 3px 0 8px;\n    }\n  }\n}\n\n.tags_clear {\n  clear: both;\n  width: 100%;\n  height: 0;\n}\n.not_valid {\n  background: #fbd8db !important;\n  color: #90111a !important;\n  margin-left: 5px !important;\n}\n\n// Alternate Color\n// --------------------------------------------------\n.tagsinput-primary {\n  margin-bottom: 18px;\n\n  .bootstrap-tagsinput {\n    border-color: @tagsinput-primary-container-border-color;\n    margin-bottom: 0;\n  }\n  .tag {\n    background-color: @tagsinput-primary-tag-bg;\n    color: @tagsinput-primary-tag-color;\n\n    &:hover {\n      background-color: @tagsinput-primary-tag-hover-bg;\n      color: @tagsinput-primary-tag-hover-color;\n    }\n  }\n}\n// Styles for Typeahead support\n// --------------------------------------------------\n.bootstrap-tagsinput .twitter-typeahead {\n  width: auto;\n  vertical-align: top;\n\n  .tt-input {\n    min-width: 200px;\n  }\n  .tt-dropdown-menu {\n    width: auto;\n    min-width: 120px;\n    margin-top: 11px;\n  }\n}\n", "//\n// Typeahead\n// --------------------------------------------------\n\n.twitter-typeahead {\n  width: 100%;\n\n  .tt-dropdown-menu {\n    width: 100%;\n    margin-top: 5px;\n    border: 2px solid @brand-secondary;\n    padding: 5px 0;\n    background-color: @inverse;\n    border-radius: @border-radius-large;\n  }\n\n  .tt-suggestion {\n    p {\n      padding: 6px 14px;\n      font-size: ceil((@component-font-size-base * 0.933));\n      line-height: 1.429; // ~20px\n      margin: 0;\n    }\n\n    &:first-child,\n    &:last-child {\n      p {\n        padding: 6px 14px;\n      }\n    }\n    &.tt-is-under-cursor, // Deprecated\n    &.tt-cursor {\n      cursor: pointer;\n      color: #fff;\n      background-color: mix(@brand-secondary, black, 85%);\n    }\n  }\n}\n", "//\n// Progress bars\n// --------------------------------------------------\n\n// Outer container\n.progress {\n  background: mix(@brand-primary, white, 10%);\n  border-radius: 32px;\n  height: @progress-height;\n  box-shadow: none;\n}\n\n// Bar of progress\n.progress-bar {\n  background: @brand-secondary;\n  line-height: @progress-height;\n  box-shadow: none;\n}\n\n// Variations\n// -------------------------\n\n.progress-bar-success {\n  background-color: @brand-success;\n}\n.progress-bar-warning {\n  background-color: @brand-warning;\n}\n.progress-bar-danger {\n  background-color: @brand-danger;\n}\n.progress-bar-info {\n  background-color: @brand-info;\n}\n", "// Slider\n// --------------------------------------------------\n\n// Default controls\n// -------------------------\n\n.ui-slider {\n  .progress();\n  margin-bottom: 20px;\n  position: relative;\n  cursor: pointer;\n}\n\n.ui-slider-handle {\n  background-color: @slider-handle-bg;\n  border-radius: 50%;\n  cursor: pointer;\n  height: 18px;\n  position: absolute;\n  width: 18px;\n  z-index: 2;\n  transition: background .25s;\n\n  &:hover,\n  &:focus {\n    background-color: @slider-handle-hover-bg;\n    outline: none;\n  }\n  &:active {\n    background-color: @slider-handle-active-bg;\n  }\n}\n\n.ui-slider-range {\n  background-color: @slider-range-bg;\n  display: block;\n  height: 100%;\n  position: absolute;\n  z-index: 1;\n}\n\n// Segments\n// -------------------------\n.ui-slider-segment {\n  background-color: @slider-segment-bg;\n  border-radius: 50%;\n  height: 6px;\n  width: 6px;\n}\n\n// Values\n// -------------------------\n.ui-slider-value {\n  float: right;\n  font-size: @slider-value-font-size;\n  margin-top: @slider-height;\n\n  &.first {\n    clear: left;\n    float: left;\n  }\n}\n\n// Horizontal orientation\n// -------------------------\n\n.ui-slider-horizontal {\n  .ui-slider-handle {\n    margin-left: -9px;\n    top: -3px;\n\n    &[style*=\"100\"] {\n      margin-left: -15px;\n    }\n  }\n  .ui-slider-range {\n    border-radius: 30px 0 0 30px;\n  }\n  .ui-slider-segment {\n    float: left;\n    margin: 3px -6px 0 0;\n  }\n}\n\n// Vertical orientation\n// -------------------------\n\n.ui-slider-vertical {\n  width: @slider-height;\n\n  .ui-slider-handle {\n    margin-left: -3px;\n    margin-bottom: -11px;\n    top: auto;\n  }\n  .ui-slider-range {\n    width: 100%;\n    bottom: 0;\n    border-radius: 0 0 30px 30px;\n  }\n  .ui-slider-segment {\n    position: absolute;\n    right: 3px;\n  }\n}\n", "//\n// Pager\n// --------------------------------------------------\n\n.pager {\n  background-color: @pager-bg;\n  border-radius: @pager-border-radius;\n  color: @pager-color;\n  font-size: 16px;\n  font-weight: 700;\n  display: inline-block;\n\n  li {\n    &:first-child {\n      > a,\n      > span {\n        border-left: none;\n        border-radius: @pager-border-radius 0 0 @pager-border-radius;\n      }\n    }\n\n    > a,\n    > span {\n      background: none;\n      border: none;\n      border-left: 2px solid mix(@brand-primary, black, 85%);\n      color: @inverse;\n      padding: @pager-padding;\n      text-decoration: none;\n      white-space: nowrap;\n      border-radius: 0 @pager-border-radius @pager-border-radius 0;\n      line-height: 1.313;\n\n      &:hover,\n      &:focus {\n        background-color: @pager-hover-bg;\n      }\n      &:active {\n        background-color: @pager-active-bg;\n      }\n\n      // Add some spacing between the icon and text\n      [class*=\"fui-\"] + span {\n        margin-left: 8px;\n      }\n      span + [class*=\"fui-\"] {\n        margin-left: 8px;\n      }\n    }\n  }\n}\n", "//\n// Pagination\n// --------------------------------------------------\n\n.pagination {\n  position: relative;\n  display: block;\n  background: @pagination-bg;\n  color: @pagination-color;\n  padding: 0;\n  display: inline-block;\n  border-radius: @pagination-border-radius;\n  word-spacing: -0.5px;\n\n  @media (min-width: @screen-sm-min) {\n    display: inline-block;\n  }\n\n  @media (max-width: @screen-xs-max) {\n    height: 41px;\n    padding: 0 55px 0 52px;\n    overflow: auto;\n    white-space: nowrap;\n    border-radius: @pagination-border-radius;\n  }\n\n  li {\n    display: inline-block;\n    margin-right: -2px;\n    vertical-align: middle;\n    word-spacing: normal;\n\n    a {\n      position: static;\n    }\n\n    // Pseudos and states\n    &.active {\n      > a, > span {\n        background-color: @pagination-hover-bg;\n        color: @inverse;\n        border-color: mix(@pagination-bg, white, 80%);\n\n        &,\n        &:hover,\n        &:focus {\n          background-color: @pagination-hover-bg;\n          color: @pagination-color;\n          border-color: mix(@pagination-bg, white, 80%);\n        }\n      }\n      &.previous,\n      &.next {\n        > a, > span {\n          margin: 0;\n\n          &,\n          &:hover,\n          &:focus {\n            background-color: @pagination-hover-bg;\n            color: @pagination-color;\n          }\n        }\n      }\n    }\n    &:first-child {\n      > a,\n      > span {\n        border-radius: @pagination-border-radius 0 0 @pagination-border-radius;\n        border-left: none;\n      }\n      &.previous + li {\n        > a,\n        > span {\n          border-left-width: 0;\n        }\n      }\n    }\n    &:last-child {\n      margin-right: 0;\n\n      > a,\n      > span {\n        &,\n        &:hover,\n        &:focus {\n          border-radius: 0 @pagination-border-radius @pagination-border-radius 0;\n        }\n      }\n    }\n    &.previous,\n    &.next {\n      > a,\n      > span {\n        border-right: 2px solid mix(@pagination-bg, white, 66%);\n        font-size: floor((@component-font-size-base * 1.067)); // ~16px\n        min-width: auto;\n        padding: 12px 17px;\n        background-color: transparent;\n      }\n    }\n    &.next {\n      > a,\n      > span {\n        border-right: none;\n      }\n    }\n    &.disabled {\n      > a,\n      > span {\n        color: @pagination-color;\n        background-color: fade(@inverse, 30%);\n        border-right-color: mix(@pagination-bg, white, 80%);\n        cursor: @cursor-disabled;\n\n        &:hover,\n        &:focus,\n        &:active {\n          background-color: fade(@inverse, 40%);\n          color: @pagination-color;\n        }\n      }\n    }\n\n    @media (max-width: @screen-xs-max) {\n      &.next,\n      &.previous {\n        background-color: @pagination-bg;\n        position: absolute;\n        right: 0;\n        top: 0;\n        z-index: 10;\n        border-radius: 0 @pagination-border-radius @pagination-border-radius 0;\n      }\n      &.previous {\n        left: 0;\n        right: auto;\n        border-radius: @pagination-border-radius 0 0 @pagination-border-radius;\n      }\n    }\n\n    // Link\n    > a,\n    > span {\n      display: inline-block;\n      background: transparent;\n      border: none;\n      border-left: 2px solid mix(@pagination-bg, white, 66%);\n      color: @inverse;\n      font-size: ceil((@component-font-size-base * 0.933)); // ~14px\n      line-height: 16px;\n      min-height: 41px;\n      min-width: 41px;\n      outline: none;\n      padding: 12px 10px;\n      text-align: center;\n      transition: .25s ease-out;\n\n      &:hover,\n      &:focus {\n        background-color: @pagination-hover-bg;\n        color: @pagination-color;\n      }\n      &:active {\n        background-color: @pagination-hover-bg;\n        color: @pagination-color;\n      }\n    }\n  }\n\n  // Navigation buttons\n  > .btn {\n    &.previous,\n    &.next {\n      margin-right: 8px;\n      font-size: ceil((@component-font-size-base * 0.933)); // ~14px\n      line-height: 1.429; // ~20px\n      padding-left: 23px;\n      padding-right: 23px;\n\n      [class*=\"fui-\"] {\n        font-size: @icon-normal;\n        margin-left: -2px;\n        margin-top: -2px;\n      }\n    }\n\n    &.next {\n      margin-left: 8px;\n      margin-right: 0;\n\n      [class*=\"fui-\"] {\n        margin-right: -2px;\n        margin-left: 4px;\n      }\n    }\n  }\n\n  // Responsive\n  // --------------------------------------\n  @media (max-width: @screen-xs-max) {\n    & {\n      > .btn {\n        display: block;\n        margin: 0;\n        width: 50%;\n\n        &:first-child {\n          border-bottom: 2px solid mix(@pagination-bg, white, 80%);\n          border-radius: @pagination-border-radius 0 0;\n\n          &.btn-primary { border-bottom-color: mix(@brand-secondary, white, 80%); }\n          &.btn-danger  { border-bottom-color: mix(@brand-danger, white, 80%); }\n          &.btn-warning { border-bottom-color: mix(@brand-warning, white, 80%); }\n          &.btn-success { border-bottom-color: mix(@brand-success, white, 80%); }\n          &.btn-info    { border-bottom-color: mix(@brand-info, white, 80%); }\n          &.btn-inverse { border-bottom-color: mix(@brand-primary, white, 80%); }\n          > [class*=\"fui\"] { margin-left: -20px; }\n        }\n        & + ul {\n          padding: 0;\n          text-align: center;\n          border-radius: 0 0 @pagination-border-radius @pagination-border-radius;\n        }\n        & + ul + .btn {\n          border-bottom: 2px solid mix(@pagination-bg, white, 80%);\n          position: absolute;\n          right: 0;\n          top: 0;\n          border-radius: 0 @pagination-border-radius 0 0;\n\n          &.btn-primary { border-bottom-color: mix(@brand-secondary, white, 80%); }\n          &.btn-danger  { border-bottom-color: mix(@brand-danger, white, 80%); }\n          &.btn-warning { border-bottom-color: mix(@brand-warning, white, 80%); }\n          &.btn-success { border-bottom-color: mix(@brand-success, white, 80%); }\n          &.btn-info    { border-bottom-color: mix(@brand-info, white, 80%); }\n          &.btn-inverse { border-bottom-color: mix(@brand-primary, white, 80%); }\n          > [class*=\"fui\"] { margin-right: -20px; }\n        }\n      }\n\n      ul {\n        display: block;\n\n        > li {\n          > a {\n            border-radius: 0;\n          }\n        }\n      }\n    }\n  }\n}\n\n// Alternate Colors\n// --------------------------------------------------\n.pagination-danger  { .pagination-variant(@brand-danger, @btn-danger-hover-bg, @btn-danger-active-bg); }\n.pagination-success { .pagination-variant(@brand-success, @btn-success-hover-bg, @btn-success-active-bg); }\n.pagination-warning { .pagination-variant(@brand-warning, @btn-warning-hover-bg, @btn-warning-active-bg); }\n.pagination-info    { .pagination-variant(@brand-info, @btn-info-hover-bg, @btn-info-active-bg); }\n.pagination-inverse { .pagination-variant(@brand-primary, @btn-inverse-hover-bg, @btn-inverse-active-bg); }\n\n// Ultra minimal pagination\n// --------------------------------------------------\n.pagination-minimal {\n  > li {\n    &:first-child {\n      border-radius: @pagination-border-radius 0 0 @pagination-border-radius;\n\n      &.previous + li {\n        > a,\n        > span {\n          border-left-width: 5px;\n        }\n      }\n    }\n    &:last-child {\n      border-radius: 0 @pagination-border-radius @pagination-border-radius 0;\n    }\n    &.previous,\n    &.next {\n      > a,\n      > span {\n        background: transparent;\n        border: none;\n        border-right: 2px solid mix(@pagination-bg, white, 66%);\n        margin: 0 9px 0 0;\n        padding: 12px 17px;\n        border-radius: @pagination-border-radius 0 0 @pagination-border-radius;\n\n        &,\n        &:hover,\n        &:focus {\n          border-color: mix(@pagination-bg, white, 66%) !important;\n        }\n\n        @media (max-width: @screen-xs-max) {\n          margin-right: 0;\n        }\n      }\n    }\n    &.next {\n      margin-left: 9px;\n\n      > a,\n      > span {\n        border-left: 2px solid mix(@pagination-bg, white, 66%);\n        border-right: none;\n        margin: 0;\n        border-radius: 0 @pagination-border-radius @pagination-border-radius 0;\n      }\n    }\n    &.active {\n      > a,\n      > span {\n        background-color: @inverse;\n        border-color: @inverse;\n        border-width: 2px !important;\n        color: @pagination-bg;\n        margin: 10px 5px 9px;\n\n        &:hover,\n        &:focus {\n          background-color: @inverse;\n          border-color: @inverse;\n          color: @pagination-bg;\n        }\n      }\n      &.previous,\n      &.next {\n        border-color: mix(@pagination-bg, white, 66%);\n      }\n      &.previous {\n        margin-right: 6px;\n      }\n    }\n\n    // Link\n    > a,\n    > span {\n      background: @inverse;\n      border: 5px solid @pagination-bg;\n      color: @inverse;\n      line-height: 16px;\n      margin: 7px 2px 6px;\n      min-width: 0;\n      min-height: 16px;\n      padding: 0 4px;\n      border-radius: 50px;\n      background-clip: padding-box;\n      transition: background .2s ease-out, border-color 0s ease-out, color .2s ease-out;\n\n      &:hover,\n      &:focus {\n        background-color: @pagination-hover-bg;\n        border-color: @pagination-hover-bg;\n        color: @inverse;\n        transition: background .2s ease-out, border-color .2s ease-out, color .2s ease-out;\n      }\n      &:active {\n        background-color: mix(@brand-secondary, black, 85%);\n        border-color: mix(@brand-secondary, black, 85%);\n      }\n    }\n  }\n}\n\n// Text only pagination (no graphics)\n// --------------------------------------------------\n.pagination-plain {\n  font-size: floor((@component-font-size-base * 1.067)); // ~16px\n  font-weight: 700;\n  list-style-type: none;\n  margin: 0 0 20px;\n  padding: 0;\n  height: 57px;\n\n  > li {\n    display: inline;\n\n    &.previous {\n      padding-right: 23px;\n    }\n    &.next {\n      padding-left: 20px;\n    }\n    &.active {\n      > a {\n        color: mix(@gray-light, white, 66%);\n      }\n    }\n    > a {\n      padding: 0 5px;\n    }\n  }\n\n  @media (max-width: @screen-xs-min) {\n    overflow: hidden;\n    text-align: center;\n\n    > li {\n      &.previous {\n        display: block;\n        margin-bottom: 10px;\n        text-align: left;\n        width: 50%;\n      }\n      &.next {\n        float: right;\n        margin-top: -64px;\n        text-align: right;\n        width: 50%;\n      }\n    }\n  }\n\n  @media (min-width: @screen-sm-min) {\n    height: auto;\n  }\n}\n\n// Pagination dropdown\n// --------------------------------------------------\n.pagination-dropdown {\n  ul {\n    min-width: 67px;\n    width: auto;\n    left: 50%;\n    margin-left: -34px;\n\n    li {\n      display: block;\n      margin-right: 0;\n\n      &:first-child {\n        > a,\n        > span {\n          border-radius: @pagination-border-radius @pagination-border-radius 0 0;\n        }\n      }\n      &:last-child {\n        > a,\n        > span {\n          border-radius: 0 0 @pagination-border-radius @pagination-border-radius !important;\n        }\n      }\n      > a,\n      > span {\n        border-left: none;\n        display: block;\n        float: none;\n        padding: 8px 10px 7px;\n        text-align: center;\n        min-height: 0;\n      }\n    }\n  }\n\n  &.dropup {\n    position: relative;\n  }\n}\n", "// Pagination variants\n\n.pagination-variant(@color, @hover, @active) {\n  & {\n    background-color: @color;\n\n    li {\n      &.previous {\n        > a {\n          border-right-color: mix(@color, white, 66%);\n        }\n      }\n      > a, > span {\n        border-left-color: mix(@color, white, 66%);\n\n        &:hover, &:focus {\n          border-left-color: mix(@color, white, 66%);\n          background-color: @hover;\n        }\n        &:active {\n          background-color: @active;\n        }\n      }\n      &.active {\n        > a, > span {\n          border-left-color: mix(@color, white, 66%);\n          background-color: @active;\n\n          &:hover, &:focus {\n            border-left-color: mix(@color, white, 66%);\n            background-color: @hover;\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Tooltips\n// --------------------------------------------------\n\n// Base class\n.tooltip {\n  font-size: ceil((@component-font-size-base * 0.933)); // ~14px\n  line-height: 1.286; // 18px\n  z-index: @zindex-tooltip;\n\n  &.in     { .opacity(@tooltip-opacity); }\n  &.top    { margin-top:  -5px; padding: @tooltip-arrow-width 0; }\n  &.right  { margin-left:  5px; padding: 0 @tooltip-arrow-width; }\n  &.bottom { margin-top:   5px; padding: @tooltip-arrow-width 0; }\n  &.left   { margin-left: -5px; padding: 0 @tooltip-arrow-width; }\n}\n\n// Wrapper for the tooltip content\n.tooltip-inner {\n  max-width: @tooltip-max-width;\n  line-height: 1.286; // 18px\n  padding: 12px 12px;\n  color: @tooltip-color;\n  background-color: @tooltip-bg;\n  border-radius: @border-radius-large;\n}\n\n// Arrows\n.tooltip {\n  &.top .tooltip-arrow {\n    margin-left: -@tooltip-arrow-width;\n    border-width: @tooltip-arrow-width @tooltip-arrow-width 0;\n    border-top-color: @tooltip-arrow-color;\n  }\n  &.right .tooltip-arrow {\n    margin-top: -@tooltip-arrow-width;\n    border-width: @tooltip-arrow-width @tooltip-arrow-width @tooltip-arrow-width 0;\n    border-right-color: @tooltip-arrow-color;\n  }\n  &.left .tooltip-arrow {\n    margin-top: -@tooltip-arrow-width;\n    border-width: @tooltip-arrow-width 0 @tooltip-arrow-width @tooltip-arrow-width;\n    border-left-color: @tooltip-arrow-color;\n  }\n  &.bottom .tooltip-arrow {\n    margin-left: -@tooltip-arrow-width;\n    border-width: 0 @tooltip-arrow-width @tooltip-arrow-width;\n    border-bottom-color: @tooltip-arrow-color;\n  }\n}\n", "//\n// Dropdown menus\n// --------------------------------------------------\n\n// Dropdown arrow/caret\n.caret {\n  display: inline-block;\n  width: 0;\n  height: 0;\n  margin-left: 5px;\n  vertical-align: middle;\n  border-top:   @caret-width-base-vertical solid;\n  border-right: @caret-width-base solid transparent;\n  border-left:  @caret-width-base solid transparent;\n  transition: border-color .25s, color .25s;\n}\n\n// The dropdown menu (ul)\n.dropdown-menu {\n  z-index: @zindex-dropdown;\n  background-color: @dropdown-bg;\n  min-width: 220px;\n  border: none;\n  margin-top: 9px;\n  padding: 0;\n  font-size: ceil((@component-font-size-base * 0.933)); // ~14px\n  border-radius: @dropdown-border-radius;\n  box-shadow: none;\n\n  // Dividers (basically an hr) within the dropdown\n  .divider {\n    .nav-divider(@dropdown-divider-bg);\n  }\n\n  // Links within the dropdown menu\n  > li > a {\n    padding: 8px 16px;\n    line-height: 1.429; // 20px\n    color: @dropdown-link-color;\n  }\n\n  > li:first-child > a:first-child {\n    .border-top-radius(@dropdown-border-radius);\n  }\n  > li:last-child > a:first-child {\n    .border-bottom-radius(@dropdown-border-radius);\n  }\n\n  // Typeahead (deprecated)\n  &.typeahead {\n    display: none;\n    width: auto;\n    margin-top: 5px;    \n    border: 2px solid @brand-secondary;\n    padding: 5px 0;\n    background-color: @inverse;\n    border-radius: @border-radius-large;\n\n    li {\n      a {\n        padding: 6px 14px;\n      }\n      &:first-child,\n      &:last-child {\n        a {\n          padding: 6px 14px;\n          border-radius: 0;\n        }\n      }\n    }\n  }\n}\n\n// Hover/Focus state\n.dropdown-menu > li > a {\n  &:hover,\n  &:focus {\n    color: @dropdown-link-hover-color;\n    background-color: @dropdown-link-hover-bg;\n  }\n}\n\n// Active state\n.dropdown-menu > .active > a {\n  &,\n  &:hover,\n  &:focus {\n    color: @dropdown-link-active-color;\n    background-color: @dropdown-link-active-bg;\n  }\n}\n\n// Disabled state\n//\n// Gray out text and ensure the hover/focus state remains gray\n\n.dropdown-menu > .disabled > a {\n  &,\n  &:hover,\n  &:focus {\n    color: @dropdown-link-disabled-color;\n    background-color: transparent;\n    cursor: @cursor-disabled;\n  }\n}\n\n// Menu positioning\n//\n// Add extra class to `.dropdown-menu` to flip the alignment of the dropdown\n// menu with the parent.\n.dropdown-menu-right {\n  left: auto; // Reset the default from `.dropdown-menu`\n  right: 0;\n}\n// With v3, we enabled auto-flipping if you have a dropdown within a right\n// aligned nav component. To enable the undoing of that, we provide an override\n// to restore the default dropdown menu alignment.\n//\n// This is only for left-aligning a dropdown menu within a `.navbar-right` or\n// `.pull-right` nav component.\n.dropdown-menu-left {\n  left: 0;\n  right: auto;\n}\n\n// Dropdown section headers\n.dropdown-header {\n  padding: 8px 16px;\n  line-height: 1.538; // 20px\n  font-size: floor((@component-font-size-base * 0.867)); // 13px\n  text-transform: uppercase;\n  color: @dropdown-header-color;\n\n  &:first-child {\n    margin-top: 3px;\n  }\n}\n\n// Backdrop to catch body clicks on mobile, etc.\n.dropdown-backdrop {\n  z-index: (@zindex-dropdown - 10);\n}\n\n// Allow for dropdowns to go bottom up (aka, dropup-menu)\n//\n// Just add .dropup after the standard .dropdown class and you're set, bro.\n\n.dropup,\n.navbar-fixed-bottom .dropdown {\n  // Reverse the caret\n  .caret {\n    border-bottom: @caret-width-base-vertical solid;\n    margin-bottom: .25em;\n  }\n  // Different positioning for bottom up menu\n  .dropdown-menu {\n    margin-top: 0;\n    margin-bottom: 9px;\n  }\n}\n\n// Inversed Dropdown Menu\n// \n// ## Alternative color variation for dropdown\n\n.dropdown-menu-inverse {\n  background-color: @dropdown-inverse-bg;\n\n  // Dividers\n  .divider {\n    .nav-divider(@dropdown-inverse-divider-bg);\n  }\n  // Links within the dropdown menu\n  > li > a {\n    color: @dropdown-inverse-link-color;\n  }\n  // Hover/Focus state\n  > li > a {\n    &:hover,\n    &:focus {\n      color: @dropdown-inverse-link-hover-color;\n      background-color: @dropdown-inverse-link-hover-bg;\n    }\n  }\n\n  // Active state\n  > .active > a {\n    &,\n    &:hover,\n    &:focus {\n      color: @dropdown-inverse-link-active-color;\n      background-color: @dropdown-inverse-link-active-bg;\n    }\n  }\n\n  // Disabled state\n  //\n  // Gray out text and ensure the hover/focus state remains gray\n  > .disabled > a {\n    &,\n    &:hover,\n    &:focus {\n      color: @dropdown-inverse-link-disabled-color;\n    }\n  }\n  // Nuke hover/focus effects\n  > .disabled > a {\n    &:hover,\n    &:focus {\n      background-color: transparent;\n    }\n  }\n\n  // Dropdown section headers\n  .dropdown-header {\n    color: @dropdown-inverse-header-color;\n  }  \n}\n\n// Component alignment\n//\n// Reiterate per navbar.less and the modified component alignment there.\n\n@media (min-width: @grid-float-breakpoint) {\n  .navbar-right {\n    .dropdown-menu {\n      .dropdown-menu-right();\n    }\n    // Necessary for overrides of the default right aligned menu.\n    // Will remove come v4 in all likelihood.\n    .dropdown-menu-left {\n      .dropdown-menu-left();\n    }\n  }\n}", "// Horizontal dividers\n//\n// Dividers (basically an hr) within dropdowns and nav lists\n\n.nav-divider(@color: #e5e5e5) {\n  height: 2px;\n  margin: 3px 0;\n  overflow: hidden;\n  background-color: @color;\n}\n", "//\n// Select\n// --------------------------------------------------\n\n\n// Select container\n.select {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  min-width: 220px;\n  width: auto;\n\n  .form-group & {\n    width: 100%;\n\n    > .select2-choice {\n      width: 100%;\n    }\n  }\n  &.form-control {\n    border: none;\n    padding: 0;\n    height: auto;\n  }\n}\n\n// Select wrapper\n.select2-choice {\n  width: 100%;\n  display: inline-block;\n  position: relative;\n  border: none;\n  font-size: @select-font-size-base;\n  font-weight: @select-font-weight;\n  line-height: @select-line-height-base;\n  border-radius: @border-radius-base;\n  padding: 10px 39px 10px 15px;\n  transition: border .25s linear, color .25s linear, background-color .25s linear;\n\n  &:hover,\n  &:focus {\n    outline: none;\n  }\n  &:active {\n    outline: none;\n    box-shadow: none;\n  }\n  .select2-container-disabled & {\n    .opacity(@select-disabled-opacity);\n  }\n}\n\n// Select label\n.select2-chosen {\n  overflow: hidden;\n  text-align: left;\n}\n\n// Select arrow\n.select2-arrow {\n  display: inline-block;\n  border-width: 8px 6px;\n  border-color: @select-arrow-color transparent;\n  border-style: solid;\n  border-bottom-style: none;\n  position: absolute;\n  right: 16px;\n  top: 42%;\n  transform: scale(1.001);\n\n  b {\n    display: none;\n  }\n\n  .btn-lg & {\n    border-top-width: 8px;\n    border-right-width: 6px;\n    border-left-width: 6px;\n  }\n}\n\n// Alternate color variants\n.select-default {\n  .select-variant(@select-default-color, @select-default-bg, @select-default-hover-bg, @select-default-active-bg, @arrow-color: @inverse);\n}\n.select-primary {\n  .select-variant(@select-default-color, @brand-secondary, @select-primary-hover-bg, @select-primary-active-bg, @arrow-color: @inverse);\n}\n.select-info {\n  .select-variant(@select-default-color, @brand-info, @select-info-hover-bg, @select-info-active-bg, @arrow-color: @inverse);\n}\n.select-danger {\n  .select-variant(@select-default-color, @brand-danger, @select-danger-hover-bg, @select-danger-active-bg, @arrow-color: @inverse);\n}\n.select-success {\n  .select-variant(@select-default-color, @brand-success, @select-success-hover-bg, @select-success-active-bg, @arrow-color: @inverse);\n}\n.select-warning {\n  .select-variant(@select-default-color, @brand-warning, @select-warning-hover-bg, @select-warning-active-bg, @arrow-color: @inverse);\n}\n.select-inverse {\n  .select-variant(@select-default-color, @brand-primary, @select-inverse-hover-bg, @select-inverse-active-bg, @arrow-color: @inverse);\n}\n\n// Select sizes\n.select-hg {\n  .select2-container& {\n    > .select2-choice {\n      .select-size(13px, 20px, @select-font-size-hg, @select-line-height-hg, @border-radius-large);\n\n      .filter-option {\n        left: 20px;\n        right: 40px;\n        top: 13px;\n      }\n      .select2-arrow {\n        right: 20px;\n      }\n      > [class^=\"fui-\"] {\n        top: 2px;\n      }\n    }\n  }\n}\n.select-lg {\n  .select2-container& {\n    > .select2-choice {\n      .select-size(10px, 19px, @select-font-size-lg, @select-line-height-lg, @border-radius-large);\n\n      .filter-option {\n        left: 18px;\n        right: 38px;\n      }\n    }\n  }\n}\n.select-sm {\n  .select2-container& {\n    > .select2-choice {\n      .select-size(9px, 13px, @select-font-size-sm, @select-line-height-sm, @border-radius-base);\n\n      .filter-option {\n        left: 13px;\n        right: 33px;\n      }\n      .select2-arrow {\n        right: 13px;\n      }\n    }\n  }\n}\n\n\n// Multiselect\n//\n// ##\n\n// Multiselect container\n.multiselect {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  min-width: 220px;\n  width: auto;\n  background-color: @multiselect-container-bg;\n  border-radius: @multiselect-container-border-radius;\n  text-align: left;\n  font-size: 0;\n  width: auto;\n  max-width: none;\n\n  .form-group & {\n    width: 100%;\n\n    > .select2-choice {\n      width: 100%;\n    }\n  }\n  &.form-control {\n    height: auto;\n    padding: 6px 1px 1px 6px;\n    border: 2px solid @multiselect-container-border-color;\n  }\n}\n\n// Multiselect tags wrapper\n.select2-choices {\n  margin: 0;\n  padding: 0;\n  position: relative;\n  cursor: text;\n  overflow: hidden;\n  min-height: 26px;\n  &:extend(.clearfix all);\n\n  li {\n    float: left;\n    list-style: none;\n  }\n}\n\n// Multiselect tag\n.select2-search-choice {\n  border-radius: @multiselect-tag-border-radius;\n  color: @multiselect-tag-color;\n  font-size: floor((@component-font-size-base * 0.886)); // ~13px\n  cursor: pointer;\n  display: inline-block;\n  position: relative;\n  vertical-align: middle;\n  overflow: hidden;\n  margin: 0 5px 4px 0;\n  line-height: 15px;\n  height: 27px;\n  padding: 6px 21px;\n  transition: .25s linear;\n\n  &:hover {\n    padding-right: 28px;\n    padding-left: 14px;\n    color: @multiselect-tag-hover-color;\n\n    .select2-search-choice-close {\n      opacity: 1;\n      filter: none;\n      color: inherit;\n    }\n  }\n\n  // Tag close icon\n  .select2-search-choice-close {\n    color: @multiselect-tag-icon-color;\n    cursor: pointer;\n    font-size: ceil((@component-font-size-base * 0.8)); // ~12px;\n    position: absolute;\n    right: 0;\n    text-align: right;\n    text-decoration: none;\n    top: 0;\n    width: 100%;\n    bottom: 0;\n    padding-right: 10px;\n    z-index: 2;\n    .opacity(0);\n    transition: opacity .25s linear;\n\n    &:after {\n      content: \"\\e609\";\n      font-family: \"Flat-UI-Icons\";\n      line-height: 27px;\n      -webkit-font-smoothing: antialiased;\n      -moz-osx-font-smoothing: grayscale;\n    }\n  }\n}\n\n// Multiselect search field\n.select2-search-field {\n  input[type=\"text\"] {\n    color: @multiselect-input-color;\n    font-size: ceil((@component-font-size-base * 0.933)); // 14px;\n    border: none;\n    box-shadow: none;\n    outline: none;\n    background-color: transparent;\n    padding: 0;\n    margin: 0;\n    width: auto;\n    max-width: inherit;\n    min-width: 80px;\n    vertical-align: top;\n    height: 29px;\n  }\n  &:first-child input[type=\"text\"] {\n    height: 23px;\n    margin: 3px 0 5px;\n  }\n}\n\n// Multiselect variants\n.multiselect-default {\n  .multiple-select-variant(@select-default-bg, @select-default-hover-bg, @select-default-bg);\n}\n.multiselect-primary {\n  .multiple-select-variant(@brand-secondary, @select-primary-hover-bg, @brand-secondary);\n}\n.multiselect-info {\n  .multiple-select-variant(@brand-info, @select-info-hover-bg, @brand-info);\n}\n.multiselect-danger {\n  .multiple-select-variant(@brand-danger, @select-danger-hover-bg, @brand-danger);\n}\n.multiselect-success {\n  .multiple-select-variant(@brand-success, @select-success-hover-bg, @brand-success);\n}\n.multiselect-warning {\n  .multiple-select-variant(@brand-warning, @select-warning-hover-bg, @brand-warning);\n}\n.multiselect-inverse {\n  .multiple-select-variant(@brand-primary, @select-inverse-hover-bg, @brand-primary);\n}\n\n\n// Select dropdown\n//\n// ##\n\n// Dropdown container\n.select2-drop {\n  &:extend(.dropdown-menu);\n  min-width: 220px;\n  margin-top: 9px;\n  visibility: visible;\n  opacity: 1;\n  filter: none;\n  border-radius: @select-dropdown-border-radius;\n  font-size: 14px;\n  position: absolute;\n  z-index: 9999;\n  top: 100%;\n  transition: none;\n\n  &.select2-drop-above {\n    margin-top: -9px;\n  }\n  &.select2-drop-auto-width {\n    width: auto;\n  }\n  &.show-select-search .select2-search {\n    display: block;\n\n    + .select2-results {\n      > li:first-child .select2-result-label {\n        border-radius: 0;\n      }\n    }\n  }\n\n  // Result list\n  .select2-results {\n    padding: 0;\n    margin: 0;\n    list-style: none;\n\n    > li:first-child > .select2-result-label {\n      .border-top-radius(@select-dropdown-border-radius);\n    }\n    > li:last-child > .select2-result-label {\n      .border-bottom-radius(@select-dropdown-border-radius);\n    }\n  }\n  .select2-result-sub {\n    padding: 0;\n    margin: 0;\n    list-style: none;\n\n    > li:last-child > .select2-result-label {\n      .border-bottom-radius(@select-dropdown-border-radius);\n    }\n  }\n  // No results title\n  .select2-no-results {\n    padding: 8px 15px;\n  }\n  // Result list item\n  .select2-result-label {\n    line-height: 1.429; // ~20px\n    padding: 8px 16px;\n    user-select: none;\n    transition: background-color .25s, color .25s;\n  }\n  // ## Styles for lists without optgroups\n  .select2-result-selectable {\n    .select2-result-label {\n      color: @select-dropdown-item-color;\n      cursor: pointer;\n\n      &:focus,\n      &:hover,\n      &:active {\n        background-color: @select-dropdown-item-hover-bg;\n        color: @select-dropdown-item-hover-color;\n        outline: none;\n      }\n    }\n  }\n  // Disabled item\n  .select2-disabled {\n    cursor: default;\n    color: @select-dropdown-disabled-item-color;\n    .opacity(@select-dropdown-disabled-item-opacity);\n\n    &:focus,\n    &:hover,\n    &:active {\n      background: none !important;\n    }\n  }\n  // Highlighted item\n  .select2-highlighted {\n    > .select2-result-label {\n      background: @select-dropdown-highlighted-item-bg;\n      color: @select-dropdown-highlighted-item-color;\n    }\n  }\n  // ## Styles for lists with optgroups\n  .select2-result-with-children {\n    // Optgroup title\n    > .select2-result-label {\n      font-size: floor((@component-font-size-base * 0.867)); // 13px\n      text-transform: uppercase;\n      color: @select-dropdown-optgroup-color;\n      margin-top: 5px;\n    }\n    + .select2-result-with-children {\n      > .select2-result-label {\n        margin-top: 11px;\n      }\n    }\n  }\n}\n\n// Dropdown wrapper\n.select2-results {\n  max-height: 200px;\n  position: relative;\n  overflow-x: hidden;\n  overflow-y: auto;\n  -webkit-tap-highlight-color: rgba(0,0,0,0);\n}\n\n// Dropdown search field\n.select2-search {\n  padding: 8px 6px;\n  width: 100%;\n  display: none;\n\n  input[type=\"text\"] {\n    &:extend(.form-control all, .input-sm all);\n    width: 100%;\n    height: auto !important;\n  }\n}\n\n// Dropdown inverse variant\n.select-inverse-dropdown {\n  background-color: @brand-primary;\n  color: fade(@inverse, 75%);\n\n  // Result list\n  .select2-results {\n    .select2-result-label {\n      color: @inverse;\n\n      &:focus,\n      &:hover,\n      &:active {\n        background: mix(@brand-primary, black, 85%);\n      }\n    }\n    &.select2-disabled .select2-result-label:hover {\n      color: @inverse;\n    }\n  }\n  // Optgroup result list\n  .select2-result-with-children {\n\n    // Optgroup title\n    > .select2-result-label {\n      color: fade(@inverse, 60%);\n\n      &:hover {\n        color: @inverse;\n        background: none !important;\n      }\n    }\n  }\n}\n\n// Multiselect dropdown wrapper\n.select2-drop-multi {\n  border-radius: @multiselect-dropdown-border-radius;\n\n  .select2-results {\n    padding: 2px 0;\n  }\n  .select2-result {\n    padding: 2px 4px;\n  }\n  .select2-result-label {\n    border-radius: @multiselect-dropdown-item-border-radius;\n  }\n  .select2-selected {\n    display: none;\n  }\n}\n\n\n// Helpers\n//\n// ##\n\n.select2-offscreen,\n.select2-offscreen:focus {\n  clip: rect(0 0 0 0) !important;\n  width: 1px !important;\n  height: 1px !important;\n  border: 0 !important;\n  margin: 0 !important;\n  padding: 0 !important;\n  overflow: hidden !important;\n  position: absolute !important;\n  outline: 0 !important;\n  left: 0 !important;\n  top: 0 !important;\n}\n\n.select2-hidden-accessible {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.select2-offscreen,\n.select2-offscreen:focus {\n  clip: rect(0 0 0 0) !important;\n  width: 1px !important;\n  height: 1px !important;\n  border: 0 !important;\n  margin: 0 !important;\n  padding: 0 !important;\n  overflow: hidden !important;\n  position: absolute !important;\n  outline: 0 !important;\n  left: 0 !important;\n  top: 0 !important;\n}\n\n.select2-display-none {\n  display: none;\n}\n\n.select2-measure-scrollbar {\n  position: absolute;\n  top: -10000px;\n  left: -10000px;\n  width: 100px;\n  height: 100px;\n  overflow: scroll;\n}\n\n.select2-drop-mask {\n  border: 0;\n  margin: 0;\n  padding: 0;\n  position: fixed;\n  left: 0;\n  top: 0;\n  min-height: 100%;\n  min-width: 100%;\n  height: auto;\n  width: auto;\n  z-index: 9998;\n  /* styles required for IE to work */\n  background-color: #fff;\n  .opacity(0);\n}\n", "// Select variants\n//\n\n.select-variant(@color; @background; @hover-background; @active-background; @disabled-background: @gray-light; @arrow-color) {\n  .select2-choice {\n    color: @color;\n    background-color: @background;\n\n    &:hover,\n    &.hover,\n    &:focus,\n    &:active {\n      color: @color;\n      background-color: @hover-background;\n      border-color: @hover-background;\n    }\n    &:active {\n      background: @active-background;\n      border-color: @active-background;\n    }\n    .select2-container-disabled& {\n      &,\n      &:hover,\n      &:focus,\n      &:active {\n        background-color: @disabled-background;\n        border-color: @background;\n      }\n    }\n\n    .select2-arrow {\n      border-top-color: @arrow-color;\n    }\n  }\n}\n\n.select-size(@padding-vertical; @padding-horizontal; @font-size; @line-height; @border-radius) {\n  .button-size(@padding-vertical; @padding-horizontal; @font-size; @line-height; @border-radius);\n  padding-right: (@padding-horizontal * 2 + 9px);\n  min-height: round((@line-height*@font-size + 2*@padding-vertical)); // we need min-height for empty ones\n}\n\n.multiple-select-variant(@background; @hover-background; @border-color) {\n  .select2-container-multi& {\n      border-color: @border-color;\n\n      .select2-search-choice {\n        background-color: @background;\n\n        &:hover {\n          background-color: @hover-background;\n        }\n      }\n  }\n}\n", "//\n// Tiles\n// -------------------------------------------------\n\n.tile {\n  background-color: @tiles-bg;\n  border-radius: @tiles-border-radius;\n  padding: 14px;\n  margin-bottom: 20px;\n  position: relative;\n  text-align: center;\n\n  .tile-hot-ribbon {\n    display: block;\n    position: absolute;\n    right: -4px;\n    top: -4px;\n    width: 82px;\n  }\n  p {\n    font-size: 15px;\n    margin-bottom: 33px;\n  }\n}\n.tile-image {\n  height: 100px;\n  margin: 31px 0 27px;\n  vertical-align: bottom;\n\n  &.big-illustration {\n    height: 111px;\n    margin-top: 20px;\n    width: 112px;\n  }\n}\n.tile-title {\n  font-size: 20px;\n  margin: 0;\n}\n", "//\n// Navbars\n// --------------------------------------------------\n\n// Wrapper and base class\n//\n// Provide a static navbar from which we expand to create full-width, fixed, and\n// other navbar variations.\n\n.navbar {\n  font-size: floor((@component-font-size-base * 1.067)); // ~16px\n  min-height: @navbar-height-base;\n  margin-bottom: @navbar-margin-bottom;\n  border: none;\n  border-radius: @navbar-border-radius;\n}\n\n// Navbar heading\n//\n// Groups `.navbar-brand` and `.navbar-toggle` into a single component for easy\n// styling of responsive aspects.\n\n.navbar-header {\n  @media (min-width: @grid-float-breakpoint) {\n    float: left;\n  }\n}\n\n// Navbar collapse (body)\n//\n// Group your navbar content into this for easy collapsing and expanding across\n// various device sizes. By default, this content is collapsed when <768px, but\n// will expand past that for a horizontal display.\n//\n// To start (on mobile devices) the navbar links, forms, and buttons are stacked\n// vertically and include a `max-height` to overflow in case you have too much\n// content for the user's viewport.\n\n.navbar-collapse {\n  box-shadow: none;\n  padding-right: 21px;\n  padding-left: 21px;\n\n  .navbar-form:first-child {\n    border:none;\n  }\n  @media (min-width: @grid-float-breakpoint) {\n    // Account for first and last children spacing\n    .navbar-nav.navbar-left:first-child {\n      margin-left: -21px;\n\n      > li:first-child a {\n        .border-left-radius(@navbar-border-radius);\n      }\n    }\n    .navbar-nav.navbar-right:last-child {\n      margin-right: -21px;\n\n      > .dropdown:last-child > a {\n        border-radius: 0 @navbar-border-radius @navbar-border-radius 0;\n      }\n    }\n    .navbar-form.navbar-right:last-child {\n      .navbar-fixed-top &,\n      .navbar-fixed-bottom & {\n        margin-right: 0;\n      }\n    }\n  }\n  @media (max-width: @grid-float-breakpoint-max) {\n    .navbar-nav.navbar-right:last-child {\n      margin-bottom: 3px;\n    }\n  }\n}\n\n// Both navbar header and collapse\n//\n// When a container is present, change the behavior of the header and collapse.\n\n.navbar {\n  .container,\n  .container-fluid {\n    padding-left: 21px;\n    padding-right: 21px;\n\n    > .navbar-header,\n    > .navbar-collapse {\n      margin-right: -21px;\n      margin-left:  -21px;\n\n      @media (min-width: @grid-float-breakpoint) {\n        margin-right: 0;\n        margin-left:  0;\n      }\n    }\n  }\n}\n\n//\n// Navbar alignment options\n//\n// Display the navbar across the entirety of the page or fixed it to the top or\n// bottom of the page.\n\n// Static top (unfixed, but 100% wide) navbar\n.navbar-static-top {\n  z-index: @zindex-navbar;\n  border-width: 0;\n  border-radius: 0;\n}\n\n// Fix the top/bottom navbars when screen real estate supports it\n.navbar-fixed-top,\n.navbar-fixed-bottom {\n  z-index: @zindex-navbar-fixed;\n  border-radius: 0;\n}\n.navbar-fixed-top {\n  border-width: 0;\n}\n.navbar-fixed-bottom {\n  margin-bottom: 0; // override .navbar defaults\n  border-width: 0;\n}\n\n// Brand/project name\n\n.navbar-brand {\n  font-size: floor((@component-font-size-base * 1.6)); // ~24px\n  line-height: 1.042;  // ~25px\n  height: @navbar-height-base;\n  font-weight: 700;\n  padding: ((@navbar-height-base - 25px) / 2) 21px;\n\n  > [class*=\"fui-\"] {\n    font-size: floor((@component-font-size-base * 1.267)); // ~19px\n    line-height: 1.263; // ~24px\n    vertical-align: top;\n  }\n\n  @media (min-width: @grid-float-breakpoint) {\n    .navbar > .container &,\n    .navbar > .container-fluid & {\n      margin-left: -21px;\n    }\n  }\n}\n\n// Navbar toggle\n//\n// Custom button for toggling the `.navbar-collapse`, powered by the collapse\n// JavaScript plugin.\n\n.navbar-toggle {\n  border: none;\n  color: @brand-primary;\n  margin: 0 0 0 21px;\n  padding: 0 21px;\n  height: @navbar-height-base;\n  line-height: @navbar-height-base;\n\n  &:before {\n    color: @link-color;\n    content: \"\\e61a\";\n    font-family: \"Flat-UI-Icons\";\n    font-size: floor((@component-font-size-base * 1.467)); // ~22px\n    font-style: normal;\n    font-weight: normal;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    transition: color .25s linear;\n  }\n  &:hover,\n  &:focus {\n    outline: none;\n\n    &:before {\n      color: @link-hover-color;\n    }\n  }\n  .icon-bar {\n    display: none;\n  }\n\n  @media (min-width: @grid-float-breakpoint) {\n    display: none;\n  }\n}\n\n// Navbar nav links\n//\n// Builds on top of the `.nav` components with it's own modifier class to make\n// the nav the full height of the horizontal nav (above 768px).\n\n.navbar-nav {\n  margin: 0;\n\n  > li > a {\n    font-size: floor((@component-font-size-base * 1.067)); // ~16px\n    padding: ((@navbar-height-base - 23px) / 2) 21px;\n    line-height: 23px;\n    font-weight: 700;\n  }\n  > li > a:hover,\n  > li > a:focus,\n  .open > a:focus,\n  .open > a:hover {\n    background-color: transparent;\n  }\n\n  [class^=\"fui-\"] {\n    line-height: 20px;\n    position: relative;\n    top: 1px;\n  }\n  .visible-sm,\n  .visible-xs {\n    > [class^=\"fui-\"] {\n      margin-left: 12px;\n    }\n  }\n\n  @media (max-width: @grid-float-breakpoint-max) {\n    margin: 0 -21px;\n\n    // Dropdowns get custom display when collapsed\n    .open .dropdown-menu {\n      > li > a,\n      .dropdown-header {\n        padding: 7px 15px 7px 31px !important;\n      }\n      > li > a {\n        line-height: 23px;\n      }\n    }\n    > li > a {\n      padding-top: 7px;\n      padding-bottom: 7px;\n    }\n  }\n}\n\n// Navbar form\n//\n// Extension of the `.form-inline` with some extra flavor for optimum display in\n// our navbars.\n\n.navbar-input {\n  .input-size(@input-height-sm; 5px; 10px; @input-font-size-sm; @navbar-input-line-height);\n}\n\n.navbar-form {\n  box-shadow: none;\n  margin-top: 0;\n  margin-bottom: 0;\n  padding-right: 19px;\n  padding-left: 19px;\n\n  // Vertically center in expanded, horizontal navbar\n  .navbar-vertical-align(@input-height-sm);\n\n  @media (max-width: @grid-float-breakpoint-max) {\n    margin: 3px -21px;\n    width: auto;\n  }\n\n  // Controls sizing\n  .form-control,\n  .input-group-addon,\n  .btn { .navbar-input(); }\n\n  .btn {\n    margin: 0;\n  }\n\n  // Reset rounded corners\n  .form-controls-corners-reset();\n\n  .form-control {\n    font-size: @component-font-size-base;\n    border-radius: 5px;\n    display: table-cell;\n  }\n  .form-group ~ .btn {\n    font-size: @component-font-size-base;\n    border-radius: 5px;\n    margin-left: 5px;\n  }\n  .form-group + .btn {\n    margin-right: 5px;\n  }\n\n  @media (min-width: @grid-float-breakpoint) {\n    .input-group { // Width fix for Webkit and IE11\n      width: 195px;\n    }\n  }\n\n  @media (max-width: @grid-float-breakpoint-max) {\n    .form-group {\n      margin-bottom: 7px;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n    .form-group + .btn {\n      margin-left: 0;\n    }\n  }\n}\n\n// Dropdown menus\n\n// Menu position and menu carets\n.navbar-nav > li {\n  > .dropdown-menu {\n    min-width: 100%;\n    margin-top: 9px;\n    border-radius: @border-radius-base;\n  }\n  &.open > .dropdown-menu {\n    @media (max-width: @grid-float-breakpoint-max) {\n      margin-top: 0 !important;\n    }\n  }\n}\n\n// Menu position and menu caret support for dropups via extra dropup class\n.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {\n  .border-bottom-radius(@border-radius-base);\n}\n\n.navbar-nav > .open > .dropdown-toggle,\n.navbar-nav > .open > .dropdown-toggle:focus,\n.navbar-nav > .open > .dropdown-toggle:hover {\n  background-color: transparent;\n}\n\n// Text in navbars\n//\n// Add a class to make any element properly align itself vertically within the navbars.\n\n.navbar-text {\n  font-size: floor((@component-font-size-base * 1.067)); // ~16px\n  line-height: 1.438; // ~23px\n  color: @brand-primary;\n  margin-top: 0;\n  margin-bottom: 0;\n\n  .navbar-vertical-align(23px, @navbar-height-base);\n\n  @media (min-width: @grid-float-breakpoint) {\n    margin-left: 21px;\n    margin-right: 21px;\n\n    // Outdent the form if last child to line up with content down the page\n    &.navbar-right:last-child {\n      margin-right: 0;\n    }\n  }\n}\n\n// Buttons in navbars\n//\n// Vertically center a button within a navbar (when *not* in a form).\n\n.navbar-btn {\n  margin-top: ((@navbar-height-base - @input-height-base) / 2);\n  margin-bottom: ((@navbar-height-base - @input-height-base) / 2);\n\n  &.btn-sm {\n    margin-top: ((@navbar-height-base - @input-height-sm) / 2);\n    margin-bottom: (((@navbar-height-base - @input-height-sm) / 2) - 1);\n  }\n  &.btn-xs {\n    margin-top: ((@navbar-height-base - 25) / 2);\n    margin-bottom: ((@navbar-height-base - 25) / 2);\n  }\n}\n\n// Unread icon\n//\n.navbar-unread,\n.navbar-new {\n  font-family: @font-family-base;\n  background-color: @brand-secondary;\n  border-radius: 50%;\n  color: @inverse;\n  font-size: 0;\n  font-weight: 700;\n  height: 6px;\n  line-height: 1;\n  position: absolute;\n  right: 12px;\n  text-align: center;\n  top: 35%;\n  width: 6px;\n  z-index: 10;\n\n  @media (max-width: @grid-float-breakpoint) {\n    position: static;\n    float: right;\n    margin: 0 0 0 10px;\n  }\n\n  .active & {\n    background-color: @inverse;\n    display: none;\n  }\n}\n\n.navbar-new {\n  background-color: @brand-danger;\n  font-size: 12px;\n  height: 18px;\n  line-height: 17px;\n  margin: -6px -10px;\n  min-width: 18px;\n  padding: 0 1px;\n  width: auto;\n  -webkit-font-smoothing: subpixel-antialiased;\n}\n\n// Alternate navbars\n// --------------------------------------------------\n\n// Default navbar\n.navbar-default {\n  background-color: @navbar-default-bg;\n\n  .navbar-brand {\n    color: @navbar-default-brand-color;\n    &:hover,\n    &:focus {\n      color: @navbar-default-brand-hover-color;\n      background-color: @navbar-default-brand-hover-bg;\n    }\n  }\n\n  .navbar-toggle {\n    &:before {\n      color: @navbar-default-toggle-color;\n    }\n    &:hover,\n    &:focus {\n      background-color: transparent;\n\n      &:before {\n        color: @navbar-default-toggle-hover-color;\n      }\n    }\n  }\n\n  .navbar-collapse,\n  .navbar-form {\n    border-color: @navbar-default-form-border;\n    border-width: 2px;\n  }\n\n  .navbar-nav {\n    > li > a {\n      color: @navbar-default-link-color;\n\n      &:hover,\n      &:focus {\n        color: @navbar-default-link-hover-color;\n        background-color: @navbar-default-link-hover-bg;\n      }\n    }\n    > .active > a {\n      &,\n      &:hover,\n      &:focus {\n        color: @navbar-default-link-active-color;\n        background-color: @navbar-default-link-active-bg;\n      }\n    }\n    > .disabled > a {\n      &,\n      &:hover,\n      &:focus {\n        color: @navbar-default-link-disabled-color;\n        background-color: @navbar-default-link-disabled-bg;\n      }\n    }\n  }\n\n  // Dropdown menu items and carets\n  .navbar-nav {\n    // Caret text color\n    > .dropdown > a .caret {\n      border-top-color: @navbar-default-caret-color;\n      border-bottom-color: @navbar-default-caret-color;\n    }\n    // Caret should match text color on active\n    > .active > a .caret {\n      border-top-color: @navbar-default-caret-active-color;\n      border-bottom-color: @navbar-default-caret-active-color;\n    }\n    // Caret should match text color on hover\n    > .dropdown > a:hover .caret,\n    > .dropdown > a:focus .caret {\n      border-top-color: @navbar-default-caret-hover-color;\n      border-bottom-color: @navbar-default-caret-hover-color;\n    }\n\n    // Remove background color from open dropdown\n    > .open > a {\n      &,\n      &:hover,\n      &:focus {\n        background-color: @navbar-default-link-active-bg;\n        color: @navbar-default-link-active-color;\n        .caret {\n          border-top-color: @navbar-default-caret-active-color;\n          border-bottom-color: @navbar-default-caret-active-color;\n        }\n      }\n    }\n\n\n    @media (max-width: @grid-float-breakpoint-max) {\n      // Dropdowns get custom display when collapsed\n      .open .dropdown-menu {\n        > li > a {\n          color: @navbar-default-link-color;\n          &:hover,\n          &:focus {\n            color: @navbar-default-link-hover-color;\n            background-color: @navbar-default-link-hover-bg;\n          }\n        }\n        > .active > a {\n          &,\n          &:hover,\n          &:focus {\n            color: @navbar-default-link-active-color;\n            background-color: @navbar-default-link-active-bg;\n          }\n        }\n        > .disabled > a {\n          &,\n          &:hover,\n          &:focus {\n            color: @navbar-default-link-disabled-color;\n            background-color: @navbar-default-link-disabled-bg;\n          }\n        }\n      }\n    }\n  }\n\n  .navbar-form {\n    .form-control {\n      border-color: transparent;\n      .placeholder(@navbar-default-form-placeholder);\n\n      &:focus {\n        border-color: @brand-secondary;\n         color: @brand-secondary;\n      }\n    }\n    .input-group-btn .btn {\n      border-color: transparent;\n      color: @navbar-default-form-icon;\n    }\n    .input-group.focus {\n      .form-control,\n      .input-group-btn .btn {\n         border-color: @brand-secondary;\n         color: @brand-secondary;\n      }\n    }\n  }\n\n  .navbar-text {\n    color: @brand-primary;\n  }\n\n  // Links in navbars\n  //\n  // Add a class to ensure links outside the navbar nav are colored correctly.\n\n  .navbar-link {\n    color: @navbar-default-link-color;\n    &:hover {\n      color: @navbar-default-link-hover-color;\n    }\n  }\n\n  .btn-link {\n    color: @navbar-default-link-color;\n    &:hover,\n    &:focus {\n      color: @navbar-default-link-hover-color;\n    }\n    &[disabled],\n    fieldset[disabled] & {\n      &:hover,\n      &:focus {\n        color: @navbar-default-link-disabled-color;\n      }\n    }\n  }\n}\n\n// Inverse navbar\n.navbar-inverse {\n  background-color: @navbar-inverse-bg;\n\n  .navbar-brand {\n    color: @navbar-inverse-brand-color;\n    &:hover,\n    &:focus {\n      color: @navbar-inverse-brand-hover-color;\n      background-color: @navbar-inverse-brand-hover-bg;\n    }\n  }\n\n  .navbar-toggle {\n    &:before {\n      color: @navbar-inverse-toggle-color;\n    }\n    &:hover,\n    &:focus {\n      background-color: transparent;\n\n      &:before {\n        color: @navbar-inverse-toggle-hover-color;\n      }\n    }\n  }\n\n  .navbar-collapse {\n    border-color: @navbar-inverse-form-border;\n    border-width: 2px;\n  }\n\n  .navbar-nav {\n    > li > a {\n      color: @navbar-inverse-link-color;\n\n      &:hover,\n      &:focus {\n        color: @navbar-inverse-link-hover-color;\n        background-color: @navbar-inverse-link-hover-bg;\n      }\n    }\n    > .active > a {\n      &,\n      &:hover,\n      &:focus {\n        color: @navbar-inverse-link-active-color;\n        background-color: @navbar-inverse-link-active-bg;\n      }\n    }\n    > .disabled > a {\n      &,\n      &:hover,\n      &:focus {\n        color: @navbar-inverse-link-disabled-color;\n        background-color: @navbar-inverse-link-disabled-bg;\n      }\n    }\n  }\n\n  // Dropdown menu items and carets\n  .navbar-nav {\n    // Caret should match text color on hover\n    > .dropdown > a:hover .caret,\n    > .dropdown > a:focus .caret {\n      border-top-color: @navbar-inverse-caret-hover-color;\n      border-bottom-color: @navbar-inverse-caret-hover-color;\n    }\n\n    // Remove background color from open dropdown\n    > .open > a {\n      &,\n      &:hover,\n      &:focus {\n        background-color: @navbar-inverse-link-active-bg;\n        color: @navbar-inverse-link-active-color;\n        border-left-color: transparent;\n        .caret {\n          border-top-color: @navbar-inverse-link-active-color;\n          border-bottom-color: @navbar-inverse-link-active-color;\n        }\n      }\n    }\n    > .dropdown > a .caret {\n      border-top-color: @navbar-inverse-caret-color;\n      border-bottom-color: @navbar-inverse-caret-color;\n    }\n\n    > .open {\n      > .dropdown-menu {\n        background-color: @navbar-inverse-dropdown-bg;\n        padding: 3px 4px;\n\n        > li > a {\n          color: @navbar-inverse-dropdown-link-color;\n          border-radius: @border-radius-base;\n          padding: 6px 9px;\n\n          &:hover,\n          &:focus {\n            color: @navbar-inverse-dropdown-link-hover-color;\n            background-color: @navbar-inverse-dropdown-link-hover-bg;\n          }\n        }\n        > .divider {\n          background-color: @navbar-inverse-divider;\n          height: 2px;\n          margin-left: -4px;\n          margin-right: -4px;\n        }\n      }\n    }\n\n    @media (max-width: @grid-float-breakpoint-max) {\n       > li > a {\n         border-left-width: 0;\n       }\n      // Dropdowns get custom display when collapsed\n      .open .dropdown-menu {\n        > li > a {\n          color: @navbar-inverse-link-color;\n          &:hover,\n          &:focus {\n            color: @navbar-inverse-link-hover-color;\n            background-color: @navbar-inverse-link-hover-bg;\n          }\n        }\n        > .active > a {\n          &,\n          &:hover,\n          &:focus {\n            color: @navbar-inverse-link-active-color;\n            background-color: @navbar-inverse-link-active-bg;\n          }\n        }\n        > .disabled > a {\n          &,\n          &:hover,\n          &:focus {\n            color: @navbar-inverse-link-disabled-color;\n            background-color: @navbar-inverse-link-disabled-bg;\n          }\n        }\n      }\n      // Custom background for dividers when collapsed\n      .dropdown-menu .divider {\n        background-color: @navbar-inverse-divider;\n      }\n    }\n  }\n\n  .navbar-form {\n    .form-control {\n      color: @navbar-inverse-form-placeholder;\n      border-color: transparent;\n      background-color: @navbar-inverse-form-bg;\n      .placeholder(@navbar-inverse-form-placeholder);\n\n      &:focus {\n        border-color: @brand-secondary;\n         color: @brand-secondary;\n      }\n    }\n    .btn {\n      .button-variant(@btn-default-color, @brand-secondary, @btn-primary-hover-bg, @btn-primary-active-bg);\n    }\n    .input-group-btn .btn {\n      border-color: transparent;\n      background-color: @navbar-inverse-form-bg;\n      color: @navbar-inverse-form-icon;\n    }\n    .input-group.focus {\n      .form-control,\n      .input-group-btn .btn {\n         border-color: @brand-secondary;\n         color: @brand-secondary;\n      }\n    }\n\n    @media (max-width: @grid-float-breakpoint-max) {\n      border-color: @navbar-inverse-form-border;\n      border-width: 2px 0;\n    }\n  }\n\n  .navbar-text {\n    color: @inverse;\n\n    a {\n      color: @navbar-inverse-link-color;\n\n      &:hover,\n      &:focus {\n        color: @navbar-inverse-link-hover-color;\n      }\n    }\n  }\n\n  .navbar-btn {\n    .button-variant(@btn-default-color, @brand-secondary, @btn-primary-hover-bg, @btn-primary-active-bg);\n  }\n}\n\n// Embossed navbar\n.navbar-embossed {\n  @media (min-width: @grid-float-breakpoint) {\n    > .navbar-collapse {\n      border-radius: @navbar-border-radius;\n      box-shadow: inset 0 -2px 0 fade(black, 15%);\n    }\n    &.navbar-inverse .navbar-nav {\n      .active > a,\n      .open > a {\n        box-shadow: inset 0 -2px 0 fade(black, 15%);\n      }\n    }\n  }\n}\n\n// Large navbar\n.navbar-lg {\n  min-height: @navbar-height-large;\n\n  .navbar-brand {\n    line-height: 1;\n    height: @navbar-height-large;\n    padding-top: ((@navbar-height-large - 24px) / 2);\n    padding-bottom: ((@navbar-height-large - 24px) / 2);\n\n    > [class*=\"fui-\"] {\n      font-size: floor((@component-font-size-base * 1.6)); // ~24px\n      line-height: 1;\n    }\n  }\n\n  .navbar-nav {\n    > li > a {\n      font-size: @component-font-size-base;\n      line-height: 1.6;\n\n      @media (min-width: @grid-float-breakpoint) {\n        padding-top: ((@navbar-height-large - 24px) / 2);\n        padding-bottom: ((@navbar-height-large - 24px) / 2);\n      }\n    }\n  }\n\n  .navbar-toggle {\n    height: @navbar-height-large;\n    line-height: @navbar-height-large;\n  }\n\n  .navbar-form {\n    .navbar-vertical-align(@input-height-sm; @navbar-height-large);\n  }\n\n  .navbar-text {\n    .navbar-vertical-align(23px; @navbar-height-large);\n  }\n\n  .navbar-btn {\n    margin-top: ((@navbar-height-large - @input-height-base) / 2);\n    margin-bottom: ((@navbar-height-large - @input-height-base) / 2);\n\n    &.btn-sm {\n      margin-top: ((@navbar-height-large - @input-height-sm) / 2);\n      margin-bottom: ((@navbar-height-large - @input-height-sm) / 2);\n    }\n    &.btn-xs {\n      margin-top: ((@navbar-height-large - 25px) / 2);\n      margin-bottom: ((@navbar-height-large - 25px) / 2);\n    }\n  }\n}\n", "// Navbar vertical align\n// -------------------------\n// Vertically center elements in the navbar.\n// Example: an element has a height of 30px, so write out `.navbar-vertical-align(30px);` to calculate the appropriate top margin.\n\n.navbar-vertical-align(@element-height; @navbar-height: @navbar-height-base) {\n  padding-top: ((@navbar-height - @element-height) / 2);\n  padding-bottom: ((@navbar-height - @element-height) / 2);\n}\n", "//\n// Switch\n// --------------------------------------------------\n\n// Switch wrapper\n// --------------------------------------------------\n\n.@{switch-name} {\n  font-size: @component-font-size-base; // 15px\n  line-height: @switch-height;\n  display: inline-block;\n  cursor: pointer;\n  border-radius: @switch-border-radius;\n  position: relative;\n  text-align: left;\n  overflow: hidden;\n  vertical-align: middle;\n  width: @switch-width;\n  height: @switch-height;\n  -webkit-mask-box-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNy4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSIwIDAgODAgMjkiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDgwIDI5IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxwYXRoIGQ9Ik04MCwxNC41YzAsOC02LjUsMTQuNS0xNC41LDE0LjVoLTUxQzYuNSwyOSwwLDIyLjUsMCwxNC41bDAsMEMwLDYuNSw2LjUsMCwxNC41LDBoNTFDNzMuNSwwLDgwLDYuNSw4MCwxNC41TDgwLDE0LjV6Ii8+DQo8L3N2Zz4NCg==) 0 0 stretch;\n  user-select: none;\n\n  // Handlers wrapp\n  > div {\n    display: inline-block;\n    width: 132px;\n    border-radius: @switch-border-radius;\n    transform: translate3d(0, 0, 0);\n\n    // Handlers\n    > span {\n      font-weight: 700;\n      line-height: 19px;\n      cursor: pointer;\n      display: inline-block;\n      height: 100%;\n      padding-bottom: 5px;\n      padding-top: 5px;\n      text-align: center;\n      z-index: 1;\n      width: 66px;\n      transition: box-shadow .25s ease-out;\n\n      > [class^=\"fui-\"] {\n        text-indent: 0;\n      }\n    }\n    > label {\n      cursor: pointer;\n      display: block;\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      text-indent: -9999px;\n      font-size: 0;\n      top: 0;\n      left: 0;\n      margin: 0;\n      z-index: 200;\n      .opacity(0);\n    }\n  }\n\n  input[type=\"radio\"],\n  input[type=\"checkbox\"] {\n    position: absolute !important;\n    margin: 0;\n    top: 0;\n    left: 0;\n    z-index: -1;\n    .opacity(0);\n  }\n}\n\n// `On` handler\n.@{switch-name}-handle-on {\n  .border-left-radius(@switch-border-radius);\n\n  .@{switch-name}-off & {\n    .switch-handle-off-variant(default, @gray-light, mix(@brand-primary, white, 63%));\n    .switch-handle-off-variant(primary, @brand-primary, @brand-secondary);\n    .switch-handle-off-variant(success, @brand-success, @inverse);\n    .switch-handle-off-variant(warning, @brand-warning, @inverse);\n    .switch-handle-off-variant(info, @brand-info, @inverse);\n    .switch-handle-off-variant(danger, @brand-danger, @inverse);\n  }\n}\n\n// `Off` handler\n.@{switch-name}-handle-off {\n  .border-right-radius(@switch-border-radius);\n\n  &:before {\n    display: inline-block;\n    content: \" \";\n    border: 4px solid transparent;\n    border-radius: 50%;\n    text-align: center;\n    vertical-align: top;\n    padding: 0;\n    height: 29px;\n    width: 29px;\n    position: absolute;\n    top: 0;\n    left: 51px;\n    z-index: 100;\n    background-clip: padding-box;\n    transition: border-color .25s ease-out, background-color .25s ease-out;\n  }\n}\n\n// Switch `animate` state\n.@{switch-name}-animate {\n  > div {\n    transition: margin-left .25s ease-out;\n  }\n}\n.@{switch-name}-on > div {\n  margin-left: 0;\n}\n\n.@{switch-name}-off > div {\n  margin-left: -51px;\n}\n\n// Switch `disabled` states\n.@{switch-name}-disabled,\n.@{switch-name}-readonly {\n  .opacity(.5);\n  cursor: default;\n\n  > div > span,\n  > div > label {\n    cursor: default !important;\n  }\n}\n\n// Switch `focus` state\n.@{switch-name}-focused {\n  outline: 0;\n}\n\n\n// Handler feedback states\n// --------------------------------------------------\n\n.@{switch-name}-default {\n  .switch-variant(@inverse, @gray-light, @gray-light, mix(@brand-primary, white, 63%));\n}\n\n.@{switch-name}-primary {\n  .switch-variant(@brand-secondary, @brand-primary, @brand-primary, @brand-secondary);\n}\n\n.@{switch-name}-info {\n  .switch-variant(@inverse, @brand-info, @brand-info, @inverse);\n}\n\n.@{switch-name}-success {\n  .switch-variant(@inverse, @brand-success, @brand-success, @inverse);\n}\n\n.@{switch-name}-warning {\n  .switch-variant(@inverse, @brand-warning, @brand-warning, @inverse);\n}\n\n.@{switch-name}-danger {\n  .switch-variant(@inverse, @brand-danger, @brand-danger, @inverse);\n}\n\n\n// Square skin\n// --------------------------------------------------\n\n.@{switch-name}-square {\n  .@{switch-name} {\n    -webkit-mask-box-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNy4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSIwIDAgODAgMjkiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDgwIDI5IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxwYXRoIGQ9Ik04MCwyNWMwLDIuMi0xLjgsNC00LDRINGMtMi4yLDAtNC0xLjgtNC00VjRjMC0yLjIsMS44LTQsNC00aDcyYzIuMiwwLDQsMS44LDQsNFYyNXoiLz4NCjwvc3ZnPg0K) 0 0 stretch;\n    border-radius: @border-radius-base;\n\n    > div {\n      border-radius: @border-radius-base;\n    }\n    .@{switch-name}-handle-on {\n      text-indent: -15px;\n      .border-left-radius(@border-radius-base);\n    }\n    .@{switch-name}-handle-off {\n      text-indent: 15px;\n      .border-right-radius(@border-radius-base);\n\n      &:before {\n         border: none;\n        .border-left-radius(0);\n        .border-right-radius((@border-radius-base - 2));\n      }\n    }\n  }\n  .@{switch-name}-off {\n    .@{switch-name}-handle-off:before {\n      .border-left-radius((@border-radius-base - 2));\n      .border-right-radius(0);\n    }\n  }\n}\n", "// Switch variants\n\n.switch-variant(@handle-color, @handle-bg, @label-border, @label-bg) {\n  color: @handle-color;\n  background-color: @handle-bg;\n\n  // second handler \"label\"\n  ~ .@{switch-name}-handle-off:before {\n    background-color: @label-bg;\n    border-color: @label-border;\n  }\n\n  // second handler inset shadow\n  .@{switch-name}-on & {\n    ~ .@{switch-name}-handle-off {\n      box-shadow: inset 16px 0 0 @handle-bg;\n    }\n  }\n}\n\n// Switch handle-off variant\n.switch-handle-off-variant(@handle-name, @handle-border, @handle-bg) {\n  // second heandler outset shadow\n  & ~ .@{switch-name}-handle-off.@{switch-name}-@{handle-name} {\n    box-shadow: ~\"inset 0 0 transparent, -16px 0 0 @{handle-border}\";\n  }\n  // second heandler \"label\"\n  ~ .@{switch-name}-handle-off.@{switch-name}-@{handle-name}:before {\n    border-color: @handle-border;\n    background-color: @handle-bg;\n  }\n}", "//\n// Sharing box\n// --------------------------------------------------\n\n// Module color variable\n@share-color: mix(@brand-primary, @inverse, 8%);\n\n.share {\n  background-color: @share-color;\n  position: relative;\n  border-radius: @border-radius-large;\n\n  ul {\n    list-style-type: none;\n    margin: 0;\n    padding: 15px;\n  }\n  li {\n    font-size: @component-font-size-base;\n    line-height: 1.4;\n    padding-top: 11px;\n    .clearfix();\n\n    &:first-child {\n      padding-top: 0;\n    }\n  }\n  .toggle {\n    float: right;\n    margin: 0;\n  }\n  .btn {\n    .border-top-radius(0);\n  }\n}\n\n.share-label {\n  float: left;\n  font-size: 15px;\n  line-height: 1.4;\n  padding-top: 5px;\n  width: 50%;\n}\n", "// Clearfix\n//\n// For modern browsers\n// 1. The space content is one way to avoid an Opera bug when the\n//    contenteditable attribute is included anywhere else in the document.\n//    Otherwise it causes space to appear at the top and bottom of elements\n//    that are clearfixed.\n// 2. The use of `table` rather than `block` is only necessary if using\n//    `:before` to contain the top-margins of child elements.\n//\n// Source: http://nicolasgallagher.com/micro-clearfix-hack/\n\n.clearfix() {\n  &:before,\n  &:after {\n    content: \" \"; // 1\n    display: table; // 2\n  }\n  &:after {\n    clear: both;\n  }\n}\n", "//\n// Video Player\n// --------------------------------------------------\n\n// Player wrapp\n.video-js {\n  background-color: transparent;\n  position: relative;\n  padding-bottom: 47px;\n  font-size: 0;\n  vertical-align: middle;\n  overflow: hidden;\n  backface-visibility: hidden;\n  border-top-radius: @vplayer-border-radius;\n  width: 100% !important;\n  height: auto !important;\n\n  // <video>\n  .vjs-tech {\n    height: 100%;\n    width: 100%;\n    display: block;\n  }\n  &::-moz-full-screen {\n    position: absolute;\n  }\n  &::-webkit-full-screen {\n    width: 100% !important;\n    height: 100% !important;\n  }\n}\n\n// Fullscreen view\n.vjs-fullscreen {\n  position: fixed;\n  overflow: hidden;\n  z-index: @vplayer-fullscreen-zindex;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: 100% !important;\n  height: 100% !important;\n  border-top-radius: 0;\n\n  .vjs-control-bar {\n    margin-top: 0;\n    .border-bottom-radius(0);\n  }\n  .vjs-tech {\n    background-color: @vplayer-fullscreen-bg;\n  }\n}\n\n// Poster\n.vjs-poster {\n  margin: 0 auto;\n  padding: 0;\n  cursor: pointer;\n  position: relative;\n  width: 100%;\n  max-height: 100%;\n  border-top-radius: @vplayer-border-radius;\n}\n\n// Control bar\n.vjs-control-bar {\n  position: relative;\n  height: 47px;\n  color: @vplayer-control-bar-color;\n  background: @vplayer-control-bar-bg;\n  margin-top: -1px;\n  .border-bottom-radius(@vplayer-border-radius);\n  user-select: none;\n\n  &.vjs-fade-out {\n    visibility: visible !important;\n    opacity: 1 !important;\n  }\n}\n\n// Other elements\n.vjs-text-track-display {\n  text-align: center;\n  position: absolute;\n  bottom: 4em;\n  left: 1em;\n  right: 1em;\n  font-family: @font-family-base;\n}\n.vjs-text-track {\n  display: none;\n  color: @vplayer-control-bar-color;\n  font-size: 1.4em;\n  text-align: center;\n  margin-bottom: .1em;\n  background-color: @vplayer-text-track-bg;\n}\n.vjs-subtitles {\n  color: @inverse;\n}\n.vjs-captions {\n  color: #fc6;\n}\n.vjs-tt-cue {\n  display: block;\n}\n\n.vjs-fade-in {\n  visibility: visible !important;\n  opacity: 1 !important;\n  transition: visibility 0s linear 0s, opacity .3s linear;\n}\n.vjs-fade-out {\n  visibility: hidden !important;\n  opacity: 0 !important;\n  transition: visibility 0s linear 1.5s, opacity 1.5s linear;\n}\n\n// Player control general style\n// --------------------------------------------------\n\n.vjs-control {\n  background-position: center;\n  background-repeat: no-repeat;\n  position: relative;\n  text-align: center;\n  display: inline-block;\n  height: 18px;\n  width: 18px;\n  vertical-align: middle;\n\n  &:focus {\n    outline: 0\n  }\n  > div {\n    background-position: center;\n    background-repeat: no-repeat;\n  }\n}\n\n// Player controls\n// --------------------------------------------------\n\n.vjs-control-text {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n// Play control\n.vjs-play-control {\n  cursor: pointer;\n  height: 47px;\n  width: 58px;\n\n  > div {\n    position: relative;\n    height: 47px;\n\n    &:before,\n    &:after {\n      position: absolute;\n      font-family: \"Flat-UI-Icons\";\n      color: @vplaver-play-control-color;\n      font-size: floor((@component-font-size-base * 1.067)); // 16px\n      top: 38%;\n      left: 50%;\n      margin: -0.5em 0 0 -0.5em;\n      -webkit-font-smoothing: antialiased;\n      transition: color .25s, opacity .25s;\n    }\n    &:after {\n      content: \"\\e615\";\n    }\n    &:before {\n      content: \"\\e616\";\n    }\n  }\n\n  .vjs-paused & {\n    &:hover {\n      > div:before {\n        color: @vplaver-play-control-hover-color;\n      }\n    }\n    > div {\n      &:after {\n        .opacity(0);\n      }\n      &:before {\n        opacity: 1;\n        filter: none;\n      }\n    }\n  }\n  .vjs-playing & {\n    &:hover {\n      > div:after {\n        color: @vplaver-play-control-hover-color;\n      }\n    }\n    > div {\n      &:after {\n        opacity: 1;\n        filter: none;\n      }\n      &:before {\n        .opacity(0);\n      }\n    }\n  }\n}\n\n// Rewind control\n.vjs-rewind-control {\n  width: 5em;\n  cursor: pointer !important;\n\n  > div {\n    width: 19px;\n    height: 16px;\n    background: none transparent;\n    margin: .5em auto 0;\n  }\n}\n\n// Mute control\n.vjs-mute-control {\n  float: right;\n  margin: 14px 0;\n  cursor: pointer !important;\n\n  &:hover,\n  &:focus {\n    > div {\n      color: @vplaver-second-controls-hover-color;\n    }\n  }\n\n  > div {\n    height: 18px;\n    color: @vplaver-second-controls-color;\n\n    &:after,\n    &:before {\n      font-family: \"Flat-UI-Icons\";\n      font-size: floor((@component-font-size-base * 1.067)); // 16px\n      line-height: 18px;\n      position: absolute;\n      left: 50%;\n      margin: 0 0 0 -0.5em;\n      -webkit-font-smoothing: antialiased;\n      -moz-osx-font-smoothing: grayscale;\n      transition: color .25s, opacity .25s;\n    }\n    &:after {\n      content: \"\\e617\";\n    }\n    &:before {\n      content: \"\\e618\";\n      .opacity(0);\n    }\n  }\n\n  // Muted state\n  &.vjs-vol-0 {\n    > div {\n      &:after {\n        .opacity(0);\n      }\n      &:before {\n        opacity: 1;\n        filter: none;\n      }\n    }\n  }\n}\n\n// Volume control\n.vjs-volume-control,\n.vjs-volume-level,\n.vjs-volume-handle,\n.vjs-volume-bar {\n  display: none;\n}\n\n// Progress control\n.vjs-progress-control {\n  height: 12px;\n  position: absolute;\n  left: 60px;\n  right: 160px;\n  width: auto;\n  top: 18px;\n  background: @vplaver-progress-bg;\n  border-radius: 32px;\n}\n\n.vjs-progress-holder {\n  position: relative;\n  cursor: pointer !important;\n  padding: 0;\n  margin: 0;\n  height: 12px;\n}\n\n.vjs-play-progress,\n.vjs-load-progress {\n  display: block;\n  height: 12px;\n  margin: 0;\n  padding: 0;\n  border-radius: 32px;\n}\n\n.vjs-play-progress {\n  background: @vplaver-play-progress-bg;\n  left: -1px;\n  position: absolute;\n  top: 0;\n  .border-right-radius(0);\n}\n\n.vjs-load-progress {\n  background: @vplaver-load-progress-bg;\n\n  &[style*=\"100%\"],\n  &[style*=\"99%\"] {\n    border-radius: 32px;\n  }\n}\n\n.vjs-seek-handle {\n  background-color: @vplayer-seek-handle-bg;\n  width: 18px;\n  height: 18px;\n  top: 0;\n  position: absolute;\n  margin: -3px 0 0 -3px;\n  border-radius: 50%;\n  transition: background-color .25s;\n\n  &[style*=\"95.\"] {\n    margin-left: 3px;\n  }\n  &[style=\"left: 0%;\"] {\n    margin-left: -2px;\n  }\n  &:hover,\n  &:focus {\n    background-color: @vplayer-seek-handle-hover-bg;\n  }\n  &:active {\n    background-color: @vplayer-seek-handle-active-bg;\n  }\n}\n\n\n// Time control\n.vjs-time-controls {\n  font-family: @font-family-base;\n  font-weight: 300;\n  font-size: floor((@component-font-size-base * 0.867)); // 13px\n  line-height: normal;\n  width: auto;\n  height: auto;\n  position: absolute;\n}\n\n.vjs-time-divider {\n  color: @vplayer-time-divider-color;\n  font-size: ceil((@component-font-size-base * 0.933)); // 14px;\n  position: absolute;\n  right: 114px;\n  top: 11px;\n}\n\n.vjs-remaining-time {\n  display: none;\n}\n\n.vjs-current-time {\n  right: 122px;\n  top: 16px;\n}\n\n.vjs-duration {\n  color: @vplayer-duration-color;\n  right: 85px;\n  top: 16px;\n}\n\n// Fullscreen control\n.vjs-fullscreen-control {\n  cursor: pointer;\n  float: right;\n  margin: 14px 15px;\n\n  &:hover,\n  &:focus {\n    > div {\n      color: @vplaver-second-controls-hover-color;\n    }\n  }\n  > div {\n    height: 18px;\n    color: @vplaver-second-controls-color;\n\n    &:before {\n      font-family: \"Flat-UI-Icons\";\n      content: \"\\e619\";\n      font-size: floor((@component-font-size-base * 1.067)); // 16px\n      line-height: 18px;\n      position: absolute;\n      left: 50%;\n      margin: 0 0 0 -0.5em;\n      -webkit-font-smoothing: antialiased;\n      -moz-osx-font-smoothing: grayscale;\n      transition: color .25s, opacity .25s;\n    }\n  }\n}\n\n// Subtitles menu. Hide for no need by design.\n.vjs-menu-button {\n  display: none !important;\n}\n\n// Rreloader\n// --------------------------------------------------\n\n.vjs-loading-spinner {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  background: #ebedee;\n  display: none;\n  height: 16px;\n  width: 16px;\n  border-radius: 10px;\n  margin: -8px 0 0 -8px;\n  animation: sharp 2s ease infinite;\n}\n\n.sharp-keyframes() {\n  0% {\n    background-color: @vplayer-preloader-primary-bg;\n    border-radius: 10px;\n    transform: rotate(0deg);\n  }\n  50% {\n    background-color: @vplayer-preloader-secondary-bg;\n    border-radius: 0;\n    transform: rotate(180deg);\n  }\n  100% {\n    background-color: @vplayer-preloader-primary-bg;\n    border-radius: 10px;\n    transform: rotate(360deg);\n  }\n}\n\n@-webkit-keyframes sharp {\n  .sharp-keyframes();\n}\n\n@-moz-keyframes sharp {\n  .sharp-keyframes();\n}\n\n@-o-keyframes sharp {\n  .sharp-keyframes();\n}\n\n@keyframes sharp {\n  .sharp-keyframes();\n}\n", "//\n// Todo list\n// --------------------------------------------------\n\n.todo {\n  color: @todo-color;\n  margin-bottom: 20px;\n  border-radius: @todo-border-radius;\n\n  ul {\n    background-color: @todo-bg-active;\n    margin: 0;\n    padding: 0;\n    list-style-type: none;\n    border-radius: 0 0 @todo-border-radius @todo-border-radius;\n  }\n  li {\n    background: @todo-bg;\n    background-size: 20px 20px;\n    cursor: pointer;\n    font-size: ceil((@component-font-size-base * 0.933)); // ~14px\n    line-height: 1.214;\n    margin-top: 2px;\n    padding: 18px 42px 21px 25px;\n    position: relative;\n    transition: .25s;\n\n    &:first-child {\n      margin-top: 0;\n    }\n    &:last-child {\n      border-radius: 0 0 @todo-border-radius @todo-border-radius;\n      padding-bottom: 21px;\n    }\n    &.todo-done {\n      background: transparent;\n      color: @todo-color-active;\n\n      .todo-name {\n        color: @todo-color-active;\n      }\n    }\n    &:after {\n      content: \" \";\n      display: block;\n      width: 20px;\n      height: 20px;\n      position: absolute;\n      top: 50%;\n      right: 22px;\n      margin-top: -10px;\n      background: @todo-name-color;\n      border-radius: 50%;\n    }\n    &.todo-done:after {\n      content: \"\\e60a\";\n      font-family: 'Flat-UI-Icons';\n      text-align: center;\n      font-size: ceil((@component-font-size-base * 0.786)); // ~11px\n      line-height: 21px;\n      font-style: normal;\n      font-weight: normal;\n      font-variant: normal;\n      text-transform: none;\n      -webkit-font-smoothing: antialiased;\n      -moz-osx-font-smoothing: grayscale;\n      background: @todo-color-active;\n      color: @todo-bg-active;\n    }\n  }\n}\n\n.todo-search {\n  position: relative;\n  background: @todo-search-bg;\n  background-size: 16px 16px;\n  border-radius: @todo-border-radius @todo-border-radius 0 0;\n  color: @todo-search-color;\n  padding: 19px 25px 20px;\n\n  &:before {\n    position: absolute;\n    font-family: 'Flat-UI-Icons';\n    content: \"\\e630\";\n    font-size: 16px;\n    line-height: 17px;\n    display: inline-block;\n    top: 50%;\n    left: 92%;\n    margin: -.5em 0 0 -1em;\n  }\n}\n\n.todo-search-field {\n  background: none;\n  border: none;\n  color: @todo-search-color;\n  font-size: 19px;\n  font-weight: 700;\n  margin: 0;\n  line-height: 23px;\n  padding: 5px 0;\n  text-indent: 0;\n  box-shadow: none;\n  .placeholder(@todo-search-color);\n  outline: none;\n}\n\n.todo-icon {\n  float: left;\n  font-size: 24px;\n  padding: 11px 22px 0 0;\n}\n\n.todo-content {\n  padding-top: 1px;\n  overflow: hidden;\n}\n\n.todo-name {\n  color: @todo-name-color;\n  font-size: 17px;\n  margin: 1px 0 3px;\n}\n", "//\n// Palette\n// --------------------------------------------------\n\n.pallete-item {\n  width: 140px;\n  float: left;\n  margin: 0 0 20px 20px;\n}\n.palette {\n  font-size: ceil((@component-font-size-base * 0.933)); // ~14px\n  line-height: 1.214; // ~17px\n  color: @inverse;\n  margin: 0;\n  padding: 15px;\n  text-transform: uppercase;\n\n  dt,\n  dd {\n    line-height: 1.429;\n  }\n  dt {\n    display: block;\n    font-weight: bold;\n    opacity: .8;\n  }\n  dd {\n    font-weight: 300;\n    margin-left: 0;\n    opacity: .8;\n    -webkit-font-smoothing: subpixel-antialiased;\n  }\n}\n\n//\n// Pallet grid\n// --------------------------------------------------\n.pallet-variant(~\"turquoise\", ~\"green-sea\");\n.pallet-variant(~\"emerald\", ~\"nephritis\");\n.pallet-variant(~\"peter-river\", ~\"belize-hole\");\n.pallet-variant(~\"amethyst\", ~\"wisteria\");\n.pallet-variant(~\"wet-asphalt\", ~\"midnight-blue\");\n\n.pallet-variant(~\"sun-flower\", ~\"orange\");\n.pallet-variant(~\"carrot\", ~\"pumpkin\");\n.pallet-variant(~\"alizarin\", ~\"pomegranate\");\n.pallet-variant(~\"clouds\", ~\"silver\");\n.pallet-variant(~\"concrete\", ~\"asbestos\");\n\n.palette-clouds {\n  color: #bdc3c7;\n}\n\n// Palette paragraph\n.palette-paragraph {\n  color: #7f8c8d;\n  font-size: 12px;\n  line-height: 17px;\n\n  span {\n    color: #bdc3c7;\n  }\n}\n\n// Headline\n.palette-headline {\n  color: #7f8c8d;\n  font-size: 13px;\n  font-weight: 700;\n  margin-top: -3px;\n}\n", "// Pallet color variants\n//\n\n.pallet-variant(@first-color, @second-color) {\n  .palette-@{first-color} {\n    background-color: ~\"@{@{first-color}}\";\n  }\n  .palette-@{second-color} {\n    background-color: ~\"@{@{second-color}}\";\n  }\n}\n", "//\n//  Login screen\n// --------------------------------------------------\n\n// Module color variable\n@form-color: mix(@brand-primary, @inverse, 9%);\n\n.login {\n  background: url(../img/login/imac.png) 0 0 no-repeat;\n  background-size: 940px 778px;\n  color: @inverse;\n  margin-bottom: 77px;\n  padding: 38px 38px 267px;\n  position: relative;\n}\n\n.login-screen {\n  background-color: @brand-secondary;\n  min-height: 473px;\n  padding: 123px 199px 33px 306px;\n}\n\n.login-icon {\n  left: 200px;\n  position: absolute;\n  top: 160px;\n  width: 96px;\n\n  > img {\n    display: block;\n    margin-bottom: 6px;\n    width: 100%;\n  }\n  > h4 {\n    font-size: 17px;\n    font-weight: 300;\n    line-height: 34px;\n    opacity: .95;\n\n    small {\n      color: inherit;\n      display: block;\n      font-size: inherit;\n      font-weight: 700;\n    }\n  }\n}\n\n// LOGIN FORM\n// -----------\n.login-form {\n  background-color: @form-color;\n  padding: 24px 23px 20px;\n  position: relative;\n  border-radius: @border-radius-large;\n\n  .control-group {\n    margin-bottom: 6px;\n    position: relative;\n  }\n  .login-field {\n    border-color: transparent;\n    font-size: 17px;\n    text-indent: 3px;\n\n    &:focus {\n      border-color: @brand-secondary;\n\n      & + .login-field-icon {\n        color: @brand-secondary;\n      }\n    }\n  }\n  .login-field-icon {\n    color: mix(@gray, @inverse, 60%);\n    font-size: 16px;\n    position: absolute;\n    right: 15px;\n    top: 3px;\n    transition: all .25s;\n  }\n}\n\n.login-link {\n  color: mix(@gray, @inverse, 60%);\n  display: block;\n  font-size: 13px;\n  margin-top: 15px;\n  text-align: center;\n}\n\n// Retina support\n@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (-moz-min-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 3/2), only screen and (-o-min-device-pixel-ratio: 2/1), only screen and (-moz-min-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 2) {\n  .login {\n    background-image: url(../img/login/imac-2x.png);\n  }\n}\n", "//\n// Footer\n// --------------------------------------------------\n\nfooter {\n  background-color: mix(@brand-primary, @inverse, 9%);\n  color: mix(@brand-primary, @inverse, 34%);\n  font-size: 15px;\n  padding: 0;\n\n  a {\n    color: mix(@brand-primary, @inverse, 50%);\n    font-weight: 700;\n  }\n  p {\n    font-size: 15px;\n    line-height: 20px;\n    margin-bottom: 10px;\n  }\n}\n\n.footer-title {\n  margin: 0 0 22px;\n  padding-top: 21px;\n  font-size: 24px;\n  line-height: 40px;\n}\n\n.footer-brand {\n  display: block;\n  margin-bottom: 26px;\n  width: 220px;\n\n  img {\n    width: 216px;\n  }\n}\n\n// Footer banner\n.footer-banner {\n  background-color: @brand-secondary;\n  color: mix(@brand-secondary, @inverse, 20%);\n  margin-left: 42px;\n  min-height: 316px;\n  padding: 0 30px 30px;\n\n  .footer-title {\n    color: @inverse;\n  }\n  a {\n    color: lighten(@brand-secondary, 42%);\n    text-decoration: underline;\n\n    &:hover {\n      text-decoration: none;\n    }\n  }\n  ul {\n    list-style-type: none;\n    margin: 0 0 26px;\n    padding: 0;\n\n    li {\n      border-top: 1px solid lighten(@brand-secondary, 2%);\n      line-height: 19px;\n      padding: 6px 0;\n\n      &:first-child {\n        border-top: none;\n        padding-top: 1px;\n      }\n    }\n  }\n}\n", "// Should be used to modify the default spacing between objects (not between nodes of * the same object)\n// p,m = padding,margin\n// a,t,r,b,l,h,v = all,top,right,bottom,left,horizontal,vertical\n// x,s,m,l,n = extra-small(@x),small(@s),medium(@m),large(@l),none(0px)\n@x: 3px;\n@s: 5px;\n@m: 10px;\n@l: 20px;\n\n.last-col {\n  overflow: hidden;\n}\n\n.ptn, .pvn, .pan {\n  padding-top: 0;\n}\n\n.ptx, .pvx, .pax {\n  padding-top: @x;\n}\n\n.pts, .pvs, .pas {\n  padding-top: @s;\n}\n\n.ptm, .pvm, .pam {\n  padding-top: @m;\n}\n\n.ptl, .pvl, .pal {\n  padding-top: @l;\n}\n\n.prn, .phn, .pan {\n  padding-right: 0;\n}\n\n.prx, .phx, .pax {\n  padding-right: @x;\n}\n\n.prs, .phs, .pas {\n  padding-right: @s;\n}\n\n.prm, .phm, .pam {\n  padding-right: @m;\n}\n\n.prl, .phl, .pal {\n  padding-right: @l;\n}\n\n.pbn, .pvn, .pan {\n  padding-bottom: 0;\n}\n\n.pbx, .pvx, .pax {\n  padding-bottom: @x;\n}\n\n.pbs, .pvs, .pas {\n  padding-bottom: @s;\n}\n\n.pbm, .pvm, .pam {\n  padding-bottom: @m;\n}\n\n.pbl, .pvl, .pal {\n  padding-bottom: @l;\n}\n\n.pln, .phn, .pan {\n  padding-left: 0;\n}\n\n.plx, .phx, .pax {\n  padding-left: @x;\n}\n\n.pls, .phs, .pas {\n  padding-left: @s;\n}\n\n.plm, .phm, .pam {\n  padding-left: @m;\n}\n\n.pll, .phl, .pal {\n  padding-left: @l;\n}\n\n.mtn, .mvn, .man {\n  margin-top: 0px;\n}\n\n.mtx, .mvx, .max {\n  margin-top: @x;\n}\n\n.mts, .mvs, .mas {\n  margin-top: @s;\n}\n\n.mtm, .mvm, .mam {\n  margin-top: @m;\n}\n\n.mtl, .mvl, .mal {\n  margin-top: @l;\n}\n\n.mrn, .mhn, .man {\n  margin-right: 0px;\n}\n\n.mrx, .mhx, .max {\n  margin-right: @x;\n}\n\n.mrs, .mhs, .mas {\n  margin-right: @s;\n}\n\n.mrm, .mhm, .mam {\n  margin-right: @m;\n}\n\n.mrl, .mhl, .mal {\n  margin-right: @l;\n}\n\n.mbn, .mvn, .man {\n  margin-bottom: 0px;\n}\n\n.mbx, .mvx, .max {\n  margin-bottom: @x;\n}\n\n.mbs, .mvs, .mas {\n  margin-bottom: @s;\n}\n\n.mbm, .mvm, .mam {\n  margin-bottom: @m;\n}\n\n.mbl, .mvl, .mal {\n  margin-bottom: @l;\n}\n\n.mln, .mhn, .man {\n  margin-left: 0px;\n}\n\n.mlx, .mhx, .max {\n  margin-left: @x;\n}\n\n.mls, .mhs, .mas {\n  margin-left: @s;\n}\n\n.mlm, .mhm, .mam {\n  margin-left: @m;\n}\n\n.mll, .mhl, .mal {\n  margin-left: @l;\n}\n", "/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */\n\n// ==========================================================================\n// Print styles.\n// Inlined to avoid the additional HTTP request: h5bp.com/r\n// ==========================================================================\n\n@media print {\n  .btn {\n    border-style: solid;\n    border-width: 2px;\n  }\n  .dropdown-menu {\n    background: #fff !important;  \n    border: 2px solid #ddd;\n  }\n  .input-group-rounded .input-group-btn {\n    & + .form-control, \n    & + .select2-search input[type=\"text\"] {\n      padding-left: 10px;\n    }\n  }\n  .form-control {\n    border: 2px solid #ddd !important;\n  }\n  .bootstrap-switch {\n    height: 33px;\n    width: 84px;\n    border: 2px solid #bdc3c7;\n  }\n  .tooltip {\n    border: 2px solid #bdc3c7;\n  }\n  .progress, .ui-slider {\n    background: #ddd !important;\n  }\n  .progress-bar, .ui-slider-range, .ui-slider-handle {\n    background: #bdc3c7 !important;\n  }\n}\n"]}