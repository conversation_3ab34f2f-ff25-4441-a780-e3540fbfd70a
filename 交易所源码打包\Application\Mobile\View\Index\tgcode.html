<!DOCTYPE html>
<html lang="zh-CN"  style="background:rgb(245, 245, 245);">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/base.css" />
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/nologed.css" />
	<title>{$webname}</title>
	<style>
		::-webkit-input-placeholder { /* WebKit browsers */
		  color: #b5b5b5;
		  font-size: 18px;
		}

		::-moz-placeholder { /* Mozilla Firefox 19+ */
		  color: #b5b5b5;
		  font-size: 18px;
		}
		input:focus{background:#F5F5F5;outline: 1px solid #F5F5F5;}
		a:hover,a:link,a:visited,a:active{color:#FCD535;text-decoration:none;}
		.imgbox{width: 100%;height: 160px;margin-top: 60px;text-align: center;line-height: 160px;}
		.logobox {width: 100%;height: 30px;line-height: 30px;}
		.logobox_left {width: 30%;height: 30px;line-height: 30px;float: left;text-align: right;}
		.logobox_right {width: 65%;height: 30px;line-height: 30px;float: right;text-align: left;}
		.logobox_right span {font-size: 20px;font-weight: bold;color: #00b897;}
		.txtbox {width: 96%;height: 80px;background: #1b1d2a;margin: 15px auto;border-radius: 15px;padding: 10px;margin-top: 30px;}
		.txtbox_tb {width: 100%;height: 30px;line-height: 30px;}
		.txtbox_tb span {font-size: 14px;color: #000;}
		.logobox {width: 100%;height: 30px;line-height: 30px;}

		.no_content {
			height: 125vh;
		}

		.msg-box {
			background: #1b1d2a;
			padding: 10px;
			margin-bottom: 10px;
			border-radius: 10px;
			height: 120px!important;
		}
	</style>
  </head>
  <body style="background:#121420;">
	<div class="container-fluid"  style="padding:0px;width:100vw;">
		<div class="no_header"  style="position: fixed;z-index: 9999;background: #121420;padding:0px 10px;top:0px;">
			<div class="fl allhg txtl" style="line-height:50px;width:10%;">
				<i class="bi bi-arrow-left fcc fw" onclick="goback()" style="font-size: 24px;"></i>
			</div>

			<div class="fl allhg" id="centerbox" style="width:80%;text-align:center;line-height:50px;">
				<span class="fcc fe6im fzmmm">{:L('邀请')}</span>
			</div>

			<div class="fr allhg txtr" style="line-height:50px;width:10%;">
			</div>
		</div>

		<div class="no_content" style="width:90%;margin:0 auto;margin-top: 60px;">
			
			<div class="no_inbox">
				
				<div class="imgbox">
					<img src="/Upload/public/{$clist.webtjimgs}" style="height:80px;">
				</div>	
				
				<div class="logobox">
					<div style="width:100%;height:30px;line-height:30px;text-align:center;">
						<span style="font-size:22px;font-weight:bold;" class="fe6im">{$webname}</span>
					</div>
				</div>

				<div class="txtbox">
					<div class="txtbox_tb">
						<span>{$clist.tgtext}</span>
					</div>
				</div>

				<div class="logobox" style="height:100px;margin-top:30px;">
					<div class="logobox_left" style="width:45%;">				
						<div style="width:80px;height:80px;margin-top:20px;float:right;">
							<img src="/Public/Static/qrcode/{$invit}.png" style="width:80px;" />
						</div>
					</div>
					<div class="logobox_right" style="width:52%;">
						<div style="width:100%;height:20px;line-height:20px;margin-top:60px;">
							<span class="fe6im" style="font-size:14px;">{:L('我的邀请码')}</span>
						</div>	
		
						<div style="width:100%;height:20px;line-height:20px;">
							<span class="fe6im" style="font-size:14px;">{$invit}</span>
						</div>
					
					</div>
					<input type="hidden" value="{$url}" id="qrcode_url">
				</div>

				<div onclick="copyUrl()" style="width:60%;height:50px;line-height:50px;background: #00b897;color: #fff;text-align:center;margin:10px auto;border-radius:15px;">
					<span style="color:#fff;font-size:14px;">{:L('复制邀请链接')}</span>
				</div>
				
				<div class="fe6im" style="width:100%;height:300px;">
				    <div class="msg-box" style="width:100%;height:100px;">
				        <p style="width:100%;height:30px;line-height:30px;font-size:16px;font-weight:bold;">{:L('三代会员统计')}</p>
				        <div style="width:100%;height:70px;">
				            <div style="width:50%;height:70px;float:left;">
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {:L('已认证')}
				                </div>
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {$carr.allrz} {:L('人')}
				                </div>
				            </div>
				            <div style="width:50%;height:70px;float:left;">
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {:L('未认证')}
				                </div>
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {$carr.allnrz} {:L('人')}
				                </div>
				            </div>
				        </div>
				    </div>
				    
				    <div class="msg-box" style="width:100%;height:100px;">
				        <p style="width:100%;height:30px;line-height:30px;font-size:16px;font-weight:bold;">{:L('一代会员')}</p>
				        <div style="width:100%;height:70px;">
				            <div style="width:50%;height:70px;float:left;">
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {:L('已认证')}
				                </div>
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {$carr.one} {:L('人')}
				                </div>
				            </div>
				            <div style="width:50%;height:70px;float:left;">
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {:L('未认证')}
				                </div>
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {$carr.onen} {:L('人')}
				                </div>
				            </div>
				        </div>
				    </div>
				    
				    <div class="msg-box" style="width:100%;height:100px;">
				        <p style="width:100%;height:30px;line-height:30px;font-size:16px;font-weight:bold;">{:L('二代会员')}</p>
				        <div style="width:100%;height:70px;">
				            <div style="width:50%;height:70px;float:left;">
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {:L('已认证')}
				                </div>
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {$carr.two} {:L('人')}
				                </div>
				            </div>
				            <div style="width:50%;height:70px;float:left;">
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {:L('未认证')}
				                </div>
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {$carr.twon} {:L('人')}
				                </div>
				            </div>
				        </div>
				    </div>
				    
				    <div class="msg-box" style="width:100%;height:100px;">
				        <p style="width:100%;height:30px;line-height:30px;font-size:16px;font-weight:bold;">{:L('三代会员')}</p>
				        <div style="width:100%;height:70px;">
				            <div style="width:50%;height:70px;float:left;">
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {:L('已认证')}
				                </div>
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {$carr.three} {:L('人')}
				                </div>
				            </div>
				            <div style="width:50%;height:70px;float:left;">
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {:L('未认证')}
				                </div>
				                <div style="width:100%;heigth:30px;line-height:30px;text-align:center;">
				                    {$carr.threen} {:L('人')}
				                </div>
				            </div>
				        </div>
				    </div>
				    
				</div>
				
				

			</div>
		</div>

	</div>		
</body>

<body>
<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
<script type="text/javascript" src="/Public/Static/js/layer/layer.js" ></script>
<script type="text/javascript">
    function copyUrl(){
        var qrcode_url=$("#qrcode_url").val();
        copy(qrcode_url);
    }

    function copy(message) {
        var input = document.createElement("input");
        input.value = message;
        document.body.appendChild(input);
        input.select();
        input.setSelectionRange(0, input.value.length), document.execCommand('Copy');
        document.body.removeChild(input);
        layer.msg("{:L('复制成功')}");
    }
</script>
<script type="text/javascript">
    function goback(){
        window.history.go(-1);
    }
</script>
</html>



