== REQUIREMENTS ==

 * PHP5
 * PHP GD2 extension with JPEG and PNG support
 
== INSTALLATION ==

If you want to recreate cache by yourself make sure cache directory is
writable and you have permisions to write into it. Also make sure you are
able to read files in it if you have cache option enabled
 
== CONFIGURATION ==

Feel free to modify config constants in qrconfig.php file. Read about it in
provided comments and project wiki page (links in README file)

== QUICK START ==

Notice: probably you should'nt use all of this in same script :)

<?phpb

//include only that one, rest required files will be included from it
include "qrlib.php"

//write code into file, Error corection lecer is lowest, L (one form: L,M,Q,H)
//each code square will be 4x4 pixels (4x zoom)
//code will have 2 code squares white boundary around 

QRcode::png('PHP QR Code :)', 'test.png', 'L', 4, 2);

//same as above but outputs file directly into browser (with appr. header etc.)
//all other settings are default
//WARNING! it should be FIRST and ONLY output generated by script, otherwise
//rest of output will land inside PNG binary, breaking it for sure
QRcode::png('PHP QR Code :)');

//show benchmark
QRtools::timeBenchmark();

//rebuild cache
QRtools::buildCache();

//code generated in text mode - as a binary table
//then displayed out as HTML using Unicode block building chars :)
$tab = $qr->encode('PHP QR Code :)');
QRspec::debug($tab, true);

== TCPDF INTEGRATION ==

Inside bindings/tcpdf you will find slightly modified 2dbarcodes.php.
Instal phpqrcode liblaty inside tcpdf folder, then overwrite (or merge)
2dbarcodes.php 

Then use similar as example #50 from TCPDF examples:

<?php

$style = array(
    'border' => true,
    'padding' => 4,
    'fgcolor' => array(0,0,0),
    'bgcolor' => false, //array(255,255,255)
);

//code name: QR, specify error correction level after semicolon (L,M,Q,H)
$pdf->write2DBarcode('PHP QR Code :)', 'QR,L', '', '', 30, 30, $style, 'N');
