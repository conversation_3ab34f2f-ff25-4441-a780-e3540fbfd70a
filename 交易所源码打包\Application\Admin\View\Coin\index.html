<include file="Public:header" />
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title">币种管理</span>
		</div>

		<div class="cf">
			<div class="fl">
				<a class="btn  navbar-btn btn-sm" href="{:U('Coin/edit')}">新 增</a>
			<button class="btn ajax-post btn-info navbar-btn btn-sm" url="{:U('Coin/status',array('method'=>'resume'))}" target-form="ids">启 用</button>
            <button class="btn ajax-post btn-warning navbar-btn btn-sm" url="{:U('Coin/status',array('method'=>'forbid'))}" target-form="ids">禁 用</button>
            <button class="btn ajax-post confirm btn-danger navbar-btn btn-sm" url="{:U('Coin/status',array('method'=>'delete'))}" target-form="ids">删 除</button>
			<button class="btn btn-success navbar-btn btn-sm" onclick="chk_qianbao();">一键检查所有钱包</button>
			</div>
			<div class="search-form fr cf">
				<div class="sleft">
					<form name="formSearch" id="formSearch" method="get" name="form1" action="{:U('Coin/index')}">
						<input type="text" name="name" class="search-input" value="{$Think.get.name}" placeholder="请输入币种名">
						<a class="sch-btn" href="javascript:;" id="search">
							<i class="btn-search"></i>
						</a>
					</form>
					<script>
						//搜索功能
						$(function(){
							$('#search').click(function(){
								$('#formSearch').submit();
							});
						});
						//回车搜索
						$(".search-input").keyup(function(e){
							if(e.keyCode===13){
								$("#search").click();
								return false;
							}
						});
					</script>
				</div>
			</div>
		</div>
		<div class="data-table table-striped">
			<table class="">
				<thead>
					<tr>
						<th class="row-selected row-selected"><input class="check-all" type="checkbox" /></th>
						<th class="">ID</th>
						<th class="">英文名</th>
						<th class="">中文名</th>
						<th class="">图标</th>
						<th class="">钱包服务器</th>
						<th class="">状态</th>
						<th class="">操作</th>
					</tr>
				</thead>
				<tbody>
					<notempty name="list"> 
						<volist name="list" id="vo">
							<tr>
								<td><input class="ids" type="checkbox" name="id[]" value="{$vo.id}" /></td>
								<td>{$vo.id}</td>
								<td>{$vo.name}</td>
								<td>{$vo.title}</td>
								<td><img src="__UPLOAD__/coin/{$vo.img}" height="18px" /></td>
								<td>
									<eq name="vo.type" value="qbb">
										<a href="{:U('Coin/info?coin='.$vo['name'])}">钱包信息</a>
										<a href="{:U('Coin/user?coin='.$vo['name'])}">所有账号</a>
										<a href="{:U('Coin/qing?coin='.$vo['name'])}">清空地址</a>
									<else/>
										没有钱包服务器
									</eq>
								</td>
								<td><eq name="vo.status" value="1">可用<else/>禁用</eq></td>
								<td>
									<neq name="vo.name" value="cny">
									<a href="{:U('Coin/edit?id='.$vo['id'])}" class="btn btn-primary btn-xs">编辑</a>
									</neq>
								</td>
							</tr>
						</volist> 
					<else />
						<td colspan="8" class="text-center empty-info">
							<i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据
						</td>
					</notempty>
				</tbody>
			</table>
			<div class="page">
				<div>{$page}</div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	//主导航高亮
	$('.Coin-box').addClass('current');
	$('.Coin-coin').addClass('current');

	function chk_qianbao(){
		window.location.href = "{:U('Tools/qianbao')}";
	}
</script>
<include file="Public:footer" />