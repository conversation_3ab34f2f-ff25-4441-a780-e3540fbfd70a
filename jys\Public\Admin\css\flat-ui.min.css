/*!
 * Flat UI Free v2.2.2 (http://designmodo.github.io/Flat-UI/)
 * Copyright 2013-2015 Designmodo, Inc.
 */
@font-face {
    font-family: Lato;
    font-style: normal;
    font-weight: 900;
    src: url(../fonts/lato/lato-black.eot);
    src: url(../fonts/lato/lato-black.eot?#iefix) format('embedded-opentype'), url(../fonts/lato/lato-black.woff) format('woff'), url(../fonts/lato/lato-black.ttf) format('truetype'), url(../fonts/lato/lato-black.svg#latoblack) format('svg')
}
@font-face {
    font-family: Lato;
    font-style: normal;
    font-weight: 700;
    src: url(../fonts/lato/lato-bold.eot);
    src: url(../fonts/lato/lato-bold.eot?#iefix) format('embedded-opentype'), url(../fonts/lato/lato-bold.woff) format('woff'), url(../fonts/lato/lato-bold.ttf) format('truetype'), url(../fonts/lato/lato-bold.svg#latobold) format('svg')
}
@font-face {
    font-family: Lato;
    font-style: italic;
    font-weight: 700;
    src: url(../fonts/lato/lato-bolditalic.eot);
    src: url(../fonts/lato/lato-bolditalic.eot?#iefix) format('embedded-opentype'), url(../fonts/lato/lato-bolditalic.woff) format('woff'), url(../fonts/lato/lato-bolditalic.ttf) format('truetype'), url(../fonts/lato/lato-bolditalic.svg#latobold-italic) format('svg')
}
@font-face {
    font-family: Lato;
    font-style: italic;
    font-weight: 400;
    src: url(../fonts/lato/lato-italic.eot);
    src: url(../fonts/lato/lato-italic.eot?#iefix) format('embedded-opentype'), url(../fonts/lato/lato-italic.woff) format('woff'), url(../fonts/lato/lato-italic.ttf) format('truetype'), url(../fonts/lato/lato-italic.svg#latoitalic) format('svg')
}
@font-face {
    font-family: Lato;
    font-style: normal;
    font-weight: 300;
    src: url(../fonts/lato/lato-light.eot);
    src: url(../fonts/lato/lato-light.eot?#iefix) format('embedded-opentype'), url(../fonts/lato/lato-light.woff) format('woff'), url(../fonts/lato/lato-light.ttf) format('truetype'), url(../fonts/lato/lato-light.svg#latolight) format('svg')
}
@font-face {
    font-family: Lato;
    font-style: normal;
    font-weight: 400;
    src: url(../fonts/lato/lato-regular.eot);
    src: url(../fonts/lato/lato-regular.eot?#iefix) format('embedded-opentype'), url(../fonts/lato/lato-regular.woff) format('woff'), url(../fonts/lato/lato-regular.ttf) format('truetype'), url(../fonts/lato/lato-regular.svg#latoregular) format('svg')
}
@font-face {
    font-family: Flat-UI-Icons;
    src: url(../fonts/glyphicons/flat-ui-icons-regular.eot);
    src: url(../fonts/glyphicons/flat-ui-icons-regular.eot?#iefix) format('embedded-opentype'), url(../fonts/glyphicons/flat-ui-icons-regular.woff) format('woff'), url(../fonts/glyphicons/flat-ui-icons-regular.ttf) format('truetype'), url(../fonts/glyphicons/flat-ui-icons-regular.svg#flat-ui-icons-regular) format('svg')
}
[class^=fui-], [class*=fui-] {
    font-family: Flat-UI-Icons;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    speak: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.fui-triangle-up:before {
    content: "\e600"
}
.fui-triangle-down:before {
    content: "\e601"
}
.fui-triangle-up-small:before {
    content: "\e602"
}
.fui-triangle-down-small:before {
    content: "\e603"
}
.fui-triangle-left-large:before {
    content: "\e604"
}
.fui-triangle-right-large:before {
    content: "\e605"
}
.fui-arrow-left:before {
    content: "\e606"
}
.fui-arrow-right:before {
    content: "\e607"
}
.fui-plus:before {
    content: "\e608"
}
.fui-cross:before {
    content: "\e609"
}
.fui-check:before {
    content: "\e60a"
}
.fui-radio-unchecked:before {
    content: "\e60b"
}
.fui-radio-checked:before {
    content: "\e60c"
}
.fui-checkbox-unchecked:before {
    content: "\e60d"
}
.fui-checkbox-checked:before {
    content: "\e60e"
}
.fui-info-circle:before {
    content: "\e60f"
}
.fui-alert-circle:before {
    content: "\e610"
}
.fui-question-circle:before {
    content: "\e611"
}
.fui-check-circle:before {
    content: "\e612"
}
.fui-cross-circle:before {
    content: "\e613"
}
.fui-plus-circle:before {
    content: "\e614"
}
.fui-pause:before {
    content: "\e615"
}
.fui-play:before {
    content: "\e616"
}
.fui-volume:before {
    content: "\e617"
}
.fui-mute:before {
    content: "\e618"
}
.fui-resize:before {
    content: "\e619"
}
.fui-list:before {
    content: "\e61a"
}
.fui-list-thumbnailed:before {
    content: "\e61b"
}
.fui-list-small-thumbnails:before {
    content: "\e61c"
}
.fui-list-large-thumbnails:before {
    content: "\e61d"
}
.fui-list-numbered:before {
    content: "\e61e"
}
.fui-list-columned:before {
    content: "\e61f"
}
.fui-list-bulleted:before {
    content: "\e620"
}
.fui-window:before {
    content: "\e621"
}
.fui-windows:before {
    content: "\e622"
}
.fui-loop:before {
    content: "\e623"
}
.fui-cmd:before {
    content: "\e624"
}
.fui-mic:before {
    content: "\e625"
}
.fui-heart:before {
    content: "\e626"
}
.fui-location:before {
    content: "\e627"
}
.fui-new:before {
    content: "\e628"
}
.fui-video:before {
    content: "\e629"
}
.fui-photo:before {
    content: "\e62a"
}
.fui-time:before {
    content: "\e62b"
}
.fui-eye:before {
    content: "\e62c"
}
.fui-chat:before {
    content: "\e62d"
}
.fui-home:before {
    content: "\e62e"
}
.fui-upload:before {
    content: "\e62f"
}
.fui-search:before {
    content: "\e630"
}
.fui-user:before {
    content: "\e631"
}
.fui-mail:before {
    content: "\e632"
}
.fui-lock:before {
    content: "\e633"
}
.fui-power:before {
    content: "\e634"
}
.fui-calendar:before {
    content: "\e635"
}
.fui-gear:before {
    content: "\e636"
}
.fui-bookmark:before {
    content: "\e637"
}
.fui-exit:before {
    content: "\e638"
}
.fui-trash:before {
    content: "\e639"
}
.fui-folder:before {
    content: "\e63a"
}
.fui-bubble:before {
    content: "\e63b"
}
.fui-export:before {
    content: "\e63c"
}
.fui-calendar-solid:before {
    content: "\e63d"
}
.fui-star:before {
    content: "\e63e"
}
.fui-star-2:before {
    content: "\e63f"
}
.fui-credit-card:before {
    content: "\e640"
}
.fui-clip:before {
    content: "\e641"
}
.fui-link:before {
    content: "\e642"
}
.fui-tag:before {
    content: "\e643"
}
.fui-document:before {
    content: "\e644"
}
.fui-image:before {
    content: "\e645"
}
.fui-facebook:before {
    content: "\e646"
}
.fui-youtube:before {
    content: "\e647"
}
.fui-vimeo:before {
    content: "\e648"
}
.fui-twitter:before {
    content: "\e649"
}
.fui-spotify:before {
    content: "\e64a"
}
.fui-skype:before {
    content: "\e64b"
}
.fui-pinterest:before {
    content: "\e64c"
}
.fui-path:before {
    content: "\e64d"
}
.fui-linkedin:before {
    content: "\e64e"
}
.fui-google-plus:before {
    content: "\e64f"
}
.fui-dribbble:before {
    content: "\e650"
}
.fui-behance:before {
    content: "\e651"
}
.fui-stumbleupon:before {
    content: "\e652"
}
.fui-yelp:before {
    content: "\e653"
}
.fui-wordpress:before {
    content: "\e654"
}
.fui-windows-8:before {
    content: "\e655"
}
.fui-vine:before {
    content: "\e656"
}
.fui-tumblr:before {
    content: "\e657"
}
.fui-paypal:before {
    content: "\e658"
}
.fui-lastfm:before {
    content: "\e659"
}
.fui-instagram:before {
    content: "\e65a"
}
.fui-html5:before {
    content: "\e65b"
}
.fui-github:before {
    content: "\e65c"
}
.fui-foursquare:before {
    content: "\e65d"
}
.fui-dropbox:before {
    content: "\e65e"
}
.fui-android:before {
    content: "\e65f"
}
.fui-apple:before {
    content: "\e660"
}
body {
    font-family: Lato, Helvetica, Arial, sans-serif;
    font-size: 18px;
    line-height: 1.72222;
    color: #34495e;
    background-color: #fff;
    min-width: 1200px;
}
a {
    color: #16a085;
    text-decoration: none;
    -webkit-transition: .25s;
    transition: .25s
}
a:hover, a:focus {
    color: #8f9fe2;
    text-decoration: none
}
a:focus {
    outline: 0
}
.img-rounded {
    border-radius: 6px
}
.img-thumbnail {
    display: inline-block;
    max-width: 100%;
    height: auto;
    padding: 4px;
    line-height: 1.72222;
    background-color: #fff;
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    -webkit-transition: all .25s ease-in-out;
    transition: all .25s ease-in-out
}
.img-comment {
    margin: 24px 0;
    font-size: 15px;
    font-style: italic;
    line-height: 1.2
}
h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: inherit;
    font-weight: 700;
    line-height: 1.1;
    color: inherit
}
h1 small, h2 small, h3 small, h4 small, h5 small, h6 small, .h1 small, .h2 small, .h3 small, .h4 small, .h5 small, .h6 small {
    color: #e7e9ec
}
h1, h2, h3 {
    margin-top: 30px;
    margin-bottom: 15px
}
h4, h5, h6 {
    margin-top: 15px;
    margin-bottom: 15px
}
h6 {
    font-weight: 400
}
h1, .h1 {
    font-size: 61px
}
h2, .h2 {
    font-size: 53px
}
h3, .h3 {
    font-size: 40px
}
h4, .h4 {
    font-size: 29px
}
h5, .h5 {
    font-size: 28px
}
h6, .h6 {
    font-size: 24px
}
p {
    margin: 0 0 15px;
    font-size: 18px;
    line-height: 1.72222
}
.lead {
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: 300;
    line-height: 1.46428571
}
@media (min-width: 768px) {
    .lead {
        font-size: 30.01px
    }
}
small, .small {
    font-size: 83%;
    line-height: 2.067
}
.text-muted {
    color: #bdc3c7
}
.text-inverse {
    color: #fff
}
.text-primary {
    color: #8f9fe2
}
a.text-primary:hover {
    color: #148f77
}
.text-warning {
    color: #f1c40f
}
a.text-warning:hover {
    color: #c29d0b
}
.text-danger {
    color: #e74c3c
}
a.text-danger:hover {
    color: #d62c1a
}
.text-success {
    color: #2ecc71
}
a.text-success:hover {
    color: #25a25a
}
.text-info {
    color: #3498db
}
a.text-info:hover {
    color: #217dbb
}
.bg-primary {
    color: #fff;
    background-color: #34495e
}
a.bg-primary:hover {
    background-color: #222f3d
}
.bg-success {
    background-color: #dff0d8
}
a.bg-success:hover {
    background-color: #c1e2b3
}
.bg-info {
    background-color: #d9edf7
}
a.bg-info:hover {
    background-color: #afd9ee
}
.bg-warning {
    background-color: #fcf8e3
}
a.bg-warning:hover {
    background-color: #f7ecb5
}
.bg-danger {
    background-color: #f2dede
}
a.bg-danger:hover {
    background-color: #e4b9b9
}
.page-header {
    padding-bottom: 14px;
    margin: 60px 0 30px;
    border-bottom: 2px solid #e7e9ec
}
ul, ol {
    margin-bottom: 15px
}
dl {
    margin-bottom: 30px
}
dt, dd {
    line-height: 1.72222
}
@media (min-width: 768px) {
    .dl-horizontal dt {
        width: 160px
    }
    .dl-horizontal dd {
        margin-left: 180px
    }
}
abbr[title], abbr[data-original-title] {
    border-bottom: 1px dotted #bdc3c7
}
blockquote {
    padding: 0 0 0 16px;
    margin: 0 0 30px;
    border-left: 3px solid #e7e9ec
}
blockquote p {
    margin-bottom: .4em;
    font-size: 20px;
    font-weight: 400;
    line-height: 1.55
}
blockquote small, blockquote .small {
    font-size: 18px;
    font-style: italic;
    line-height: 1.72222;
    color: inherit
}
blockquote small:before, blockquote .small:before {
    content: ""
}
blockquote.pull-right {
    padding-right: 16px;
    padding-left: 0;
    border-right: 3px solid #e7e9ec;
    border-left: 0
}
blockquote.pull-right small:after {
    content: ""
}
address {
    margin-bottom: 30px;
    line-height: 1.72222
}
sub, sup {
    font-size: 70%
}
code, kbd, pre, samp {
    font-family: Monaco, Menlo, Consolas, "Courier New", monospace
}
code {
    padding: 2px 6px;
    font-size: 85%;
    color: #c7254e;
    background-color: #f9f2f4;
    border-radius: 4px
}
kbd {
    padding: 2px 6px;
    font-size: 85%;
    color: #fff;
    background-color: #34495e;
    border-radius: 4px;
    box-shadow: none
}
pre {
    padding: 8px;
    margin: 0 0 15px;
    font-size: 13px;
    line-height: 1.72222;
    color: inherit;
    white-space: pre;
    background-color: #fff;
    border: 2px solid #e7e9ec;
    border-radius: 6px
}
.pre-scrollable {
    max-height: 340px
}
.thumbnail {
    display: block;
    padding: 4px;
    margin-bottom: 5px;
    line-height: 1.72222;
    background-color: #fff;
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    -webkit-transition: border .25s ease-in-out;
    transition: border .25s ease-in-out
}
.thumbnail > img, .thumbnail a > img {
    display: block;
    max-width: 100%;
    height: auto;
    margin-right: auto;
    margin-left: auto
}
a.thumbnail:hover, a.thumbnail:focus, a.thumbnail.active {
    border-color: #16a085
}
.thumbnail .caption {
    padding: 9px;
    color: #34495e
}
.btn {
    padding: 10px 15px;
    font-size: 15px;
    font-weight: 400;
    line-height: 1.4;
    border: none;
    border-radius: 4px;
    -webkit-transition: border .25s linear, color .25s linear, background-color .25s linear;
    transition: border .25s linear, color .25s linear, background-color .25s linear;
    -webkit-font-smoothing: subpixel-antialiased
}
.btn:hover, .btn:focus {
    color: #fff;
    outline: 0
}
.btn:active, .btn.active {
    outline: 0;
    box-shadow: none
}
.btn:focus:active {
    outline: 0
}
.btn.disabled, .btn[disabled], fieldset[disabled] .btn {
    color: rgba(255, 255, 255, .75);
    cursor: not-allowed;
    background-color: #bdc3c7;
    filter: alpha(opacity=70);
    opacity: .7
}
.btn [class^=fui-] {
    position: relative;
    top: 1px;
    margin: 0 1px;
    line-height: 1
}
.btn-xs.btn [class^=fui-] {
    top: 0;
    font-size: 11px
}
.btn-hg.btn [class^=fui-] {
    top: 2px
}
.btn-default {
    color: #fff;
    background-color: #bdc3c7
}
.btn-default:hover, .btn-default.hover, .btn-default:focus, .btn-default:active, .btn-default.active, .open > .dropdown-toggle.btn-default {
    color: #fff;
    background-color: #cacfd2;
    border-color: #cacfd2
}
.btn-default:active, .btn-default.active, .open > .dropdown-toggle.btn-default {
    background: #a1a6a9;
    border-color: #a1a6a9
}
.btn-default.disabled, .btn-default[disabled], fieldset[disabled] .btn-default, .btn-default.disabled:hover, .btn-default[disabled]:hover, fieldset[disabled] .btn-default:hover, .btn-default.disabled.hover, .btn-default[disabled].hover, fieldset[disabled] .btn-default.hover, .btn-default.disabled:focus, .btn-default[disabled]:focus, fieldset[disabled] .btn-default:focus, .btn-default.disabled:active, .btn-default[disabled]:active, fieldset[disabled] .btn-default:active, .btn-default.disabled.active, .btn-default[disabled].active, fieldset[disabled] .btn-default.active {
    background-color: #bdc3c7;
    border-color: #bdc3c7
}
.btn-default .badge {
    color: #bdc3c7;
    background-color: #fff
}
.btn-primary {
    color: #fff;
    background-color: #585f7a
}
.btn-primary:hover, .btn-primary.hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .open > .dropdown-toggle.btn-primary {
    color: #fff;
    background-color: #48c9b0;
    border-color: #48c9b0
}
.btn-primary:active, .btn-primary.active, .open > .dropdown-toggle.btn-primary {
    background: #16a085;
    border-color: #16a085
}
.btn-primary.disabled, .btn-primary[disabled], fieldset[disabled] .btn-primary, .btn-primary.disabled:hover, .btn-primary[disabled]:hover, fieldset[disabled] .btn-primary:hover, .btn-primary.disabled.hover, .btn-primary[disabled].hover, fieldset[disabled] .btn-primary.hover, .btn-primary.disabled:focus, .btn-primary[disabled]:focus, fieldset[disabled] .btn-primary:focus, .btn-primary.disabled:active, .btn-primary[disabled]:active, fieldset[disabled] .btn-primary:active, .btn-primary.disabled.active, .btn-primary[disabled].active, fieldset[disabled] .btn-primary.active {
    background-color: #bdc3c7;
    border-color: #8f9fe2
}
.btn-primary .badge {
    color: #8f9fe2;
    background-color: #fff
}
.btn-info {
    color: #fff;
    background-color: #3498db
}
.btn-info:hover, .btn-info.hover, .btn-info:focus, .btn-info:active, .btn-info.active, .open > .dropdown-toggle.btn-info {
    color: #fff;
    background-color: #5dade2;
    border-color: #5dade2
}
.btn-info:active, .btn-info.active, .open > .dropdown-toggle.btn-info {
    background: #2c81ba;
    border-color: #2c81ba
}
.btn-info.disabled, .btn-info[disabled], fieldset[disabled] .btn-info, .btn-info.disabled:hover, .btn-info[disabled]:hover, fieldset[disabled] .btn-info:hover, .btn-info.disabled.hover, .btn-info[disabled].hover, fieldset[disabled] .btn-info.hover, .btn-info.disabled:focus, .btn-info[disabled]:focus, fieldset[disabled] .btn-info:focus, .btn-info.disabled:active, .btn-info[disabled]:active, fieldset[disabled] .btn-info:active, .btn-info.disabled.active, .btn-info[disabled].active, fieldset[disabled] .btn-info.active {
    background-color: #bdc3c7;
    border-color: #3498db
}
.btn-info .badge {
    color: #3498db;
    background-color: #fff
}
.btn-danger {
    color: #fff;
    background-color: #e74c3c
}
.btn-danger:hover, .btn-danger.hover, .btn-danger:focus, .btn-danger:active, .btn-danger.active, .open > .dropdown-toggle.btn-danger {
    color: #fff;
    background-color: #ec7063;
    border-color: #ec7063
}
.btn-danger:active, .btn-danger.active, .open > .dropdown-toggle.btn-danger {
    background: #c44133;
    border-color: #c44133
}
.btn-danger.disabled, .btn-danger[disabled], fieldset[disabled] .btn-danger, .btn-danger.disabled:hover, .btn-danger[disabled]:hover, fieldset[disabled] .btn-danger:hover, .btn-danger.disabled.hover, .btn-danger[disabled].hover, fieldset[disabled] .btn-danger.hover, .btn-danger.disabled:focus, .btn-danger[disabled]:focus, fieldset[disabled] .btn-danger:focus, .btn-danger.disabled:active, .btn-danger[disabled]:active, fieldset[disabled] .btn-danger:active, .btn-danger.disabled.active, .btn-danger[disabled].active, fieldset[disabled] .btn-danger.active {
    background-color: #bdc3c7;
    border-color: #e74c3c
}
.btn-danger .badge {
    color: #e74c3c;
    background-color: #fff
}
.btn-success {
    color: #fff;
    background-color: #2ecc71
}
.btn-success:hover, .btn-success.hover, .btn-success:focus, .btn-success:active, .btn-success.active, .open > .dropdown-toggle.btn-success {
    color: #fff;
    background-color: #58d68d;
    border-color: #58d68d
}
.btn-success:active, .btn-success.active, .open > .dropdown-toggle.btn-success {
    background: #27ad60;
    border-color: #27ad60
}
.btn-success.disabled, .btn-success[disabled], fieldset[disabled] .btn-success, .btn-success.disabled:hover, .btn-success[disabled]:hover, fieldset[disabled] .btn-success:hover, .btn-success.disabled.hover, .btn-success[disabled].hover, fieldset[disabled] .btn-success.hover, .btn-success.disabled:focus, .btn-success[disabled]:focus, fieldset[disabled] .btn-success:focus, .btn-success.disabled:active, .btn-success[disabled]:active, fieldset[disabled] .btn-success:active, .btn-success.disabled.active, .btn-success[disabled].active, fieldset[disabled] .btn-success.active {
    background-color: #bdc3c7;
    border-color: #2ecc71
}
.btn-success .badge {
    color: #2ecc71;
    background-color: #fff
}
.btn-warning {
    color: #fff;
    background-color: #f1c40f
}
.btn-warning:hover, .btn-warning.hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active, .open > .dropdown-toggle.btn-warning {
    color: #fff;
    background-color: #f4d313;
    border-color: #f4d313
}
.btn-warning:active, .btn-warning.active, .open > .dropdown-toggle.btn-warning {
    background: #cda70d;
    border-color: #cda70d
}
.btn-warning.disabled, .btn-warning[disabled], fieldset[disabled] .btn-warning, .btn-warning.disabled:hover, .btn-warning[disabled]:hover, fieldset[disabled] .btn-warning:hover, .btn-warning.disabled.hover, .btn-warning[disabled].hover, fieldset[disabled] .btn-warning.hover, .btn-warning.disabled:focus, .btn-warning[disabled]:focus, fieldset[disabled] .btn-warning:focus, .btn-warning.disabled:active, .btn-warning[disabled]:active, fieldset[disabled] .btn-warning:active, .btn-warning.disabled.active, .btn-warning[disabled].active, fieldset[disabled] .btn-warning.active {
    background-color: #bdc3c7;
    border-color: #f1c40f
}
.btn-warning .badge {
    color: #f1c40f;
    background-color: #fff
}
.btn-inverse {
    color: #fff;
    background-color: #34495e
}
.btn-inverse:hover, .btn-inverse.hover, .btn-inverse:focus, .btn-inverse:active, .btn-inverse.active, .open > .dropdown-toggle.btn-inverse {
    color: #fff;
    background-color: #415b76;
    border-color: #415b76
}
.btn-inverse:active, .btn-inverse.active, .open > .dropdown-toggle.btn-inverse {
    background: #2c3e50;
    border-color: #2c3e50
}
.btn-inverse.disabled, .btn-inverse[disabled], fieldset[disabled] .btn-inverse, .btn-inverse.disabled:hover, .btn-inverse[disabled]:hover, fieldset[disabled] .btn-inverse:hover, .btn-inverse.disabled.hover, .btn-inverse[disabled].hover, fieldset[disabled] .btn-inverse.hover, .btn-inverse.disabled:focus, .btn-inverse[disabled]:focus, fieldset[disabled] .btn-inverse:focus, .btn-inverse.disabled:active, .btn-inverse[disabled]:active, fieldset[disabled] .btn-inverse:active, .btn-inverse.disabled.active, .btn-inverse[disabled].active, fieldset[disabled] .btn-inverse.active {
    background-color: #bdc3c7;
    border-color: #34495e
}
.btn-inverse .badge {
    color: #34495e;
    background-color: #fff
}
.btn-embossed {
    box-shadow: inset 0 -2px 0 rgba(0, 0, 0, .15)
}
.btn-embossed.active, .btn-embossed:active {
    box-shadow: inset 0 2px 0 rgba(0, 0, 0, .15)
}
.btn-wide {
    min-width: 140px;
    padding-right: 30px;
    padding-left: 30px
}
.btn-link {
    color: #16a085
}
.btn-link:hover, .btn-link:focus {
    color: #8f9fe2;
    text-decoration: underline;
    background-color: transparent
}
.btn-link[disabled]:hover, fieldset[disabled] .btn-link:hover, .btn-link[disabled]:focus, fieldset[disabled] .btn-link:focus {
    color: #bdc3c7;
    text-decoration: none
}
.btn-hg, .btn-group-hg > .btn {
    padding: 13px 20px;
    font-size: 22px;
    line-height: 1.227;
    border-radius: 6px
}
.btn-lg, .btn-group-lg > .btn {
    padding: 10px 19px;
    font-size: 17px;
    line-height: 1.471;
    border-radius: 6px
}
.btn-sm, .btn-group-sm > .btn {
    padding: 9px 13px;
    font-size: 13px;
    line-height: 1.385;
    border-radius: 4px
}
.btn-xs, .btn-group-xs > .btn {
    padding: 6px 9px;
    font-size: 12px;
    line-height: 1.083;
    border-radius: 3px
}
.btn-tip {
    padding-left: 10px;
    font-size: 92%;
    font-weight: 300
}
.btn-block {
    white-space: normal
}
[class*=btn-social-] {
    padding: 10px 15px;
    font-size: 13px;
    line-height: 1.077;
    border-radius: 4px
}
.btn-social-pinterest {
    color: #fff;
    background-color: #cb2028
}
.btn-social-pinterest:hover, .btn-social-pinterest:focus {
    background-color: #d54d53
}
.btn-social-pinterest:active, .btn-social-pinterest.active {
    background-color: #ad1b22
}
.btn-social-linkedin {
    color: #fff;
    background-color: #0072b5
}
.btn-social-linkedin:hover, .btn-social-linkedin:focus {
    background-color: #338ec4
}
.btn-social-linkedin:active, .btn-social-linkedin.active {
    background-color: #00619a
}
.btn-social-stumbleupon {
    color: #fff;
    background-color: #ed4a13
}
.btn-social-stumbleupon:hover, .btn-social-stumbleupon:focus {
    background-color: #f16e42
}
.btn-social-stumbleupon:active, .btn-social-stumbleupon.active {
    background-color: #c93f10
}
.btn-social-googleplus {
    color: #fff;
    background-color: #2d2d2d
}
.btn-social-googleplus:hover, .btn-social-googleplus:focus {
    background-color: #575757
}
.btn-social-googleplus:active, .btn-social-googleplus.active {
    background-color: #262626
}
.btn-social-facebook {
    color: #fff;
    background-color: #2f4b93
}
.btn-social-facebook:hover, .btn-social-facebook:focus {
    background-color: #596fa9
}
.btn-social-facebook:active, .btn-social-facebook.active {
    background-color: #28407d
}
.btn-social-twitter {
    color: #fff;
    background-color: #00bdef
}
.btn-social-twitter:hover, .btn-social-twitter:focus {
    background-color: #33caf2
}
.btn-social-twitter:active, .btn-social-twitter.active {
    background-color: #00a1cb
}
.btn-group > .btn + .btn {
    margin-left: 0
}
.btn-group > .btn + .dropdown-toggle {
    padding: 10px 12px;
    border-left: 2px solid rgba(52, 73, 94, .15)
}
.btn-group > .btn + .dropdown-toggle .caret {
    margin-right: 3px;
    margin-left: 3px
}
.btn-group > .btn.btn-gh + .dropdown-toggle .caret {
    margin-right: 7px;
    margin-left: 7px
}
.btn-group > .btn.btn-sm + .dropdown-toggle .caret {
    margin-right: 0;
    margin-left: 0
}
.dropdown-toggle .caret {
    margin-left: 8px
}
.btn-group-xs > .btn + .dropdown-toggle {
    padding: 6px 9px
}
.btn-group-sm > .btn + .dropdown-toggle {
    padding: 9px 13px
}
.btn-group-lg > .btn + .dropdown-toggle {
    padding: 10px 19px
}
.btn-group-hg > .btn + .dropdown-toggle {
    padding: 13px 20px
}
.btn-xs .caret {
    border-width: 6px 4px 0;
    border-bottom-width: 0
}
.btn-lg .caret {
    border-width: 8px 6px 0;
    border-bottom-width: 0
}
.dropup .btn-lg .caret {
    border-width: 0 6px 8px
}
.dropup .btn-xs .caret {
    border-width: 0 4px 6px
}
.btn-group > .btn, .btn-group > .dropdown-menu, .btn-group > .popover {
    font-weight: 400
}
.btn-group:focus .dropdown-toggle {
    outline: 0;
    -webkit-transition: .25s;
    transition: .25s
}
.btn-group.open .dropdown-toggle {
    color: rgba(255, 255, 255, .75);
    box-shadow: none
}
.btn-toolbar .btn.active {
    color: #fff
}
.btn-toolbar .btn > [class^=fui-] {
    margin: 0 1px;
    font-size: 16px
}
legend {
    display: block;
    width: 100%;
    padding: 0;
    margin-bottom: 30px / 2;
    font-size: 24px;
    line-height: inherit;
    color: inherit;
    border-bottom: none
}
textarea {
    padding: 5px 11px;
    font-size: 20px;
    line-height: 24px
}
input[type=search] {
    -webkit-appearance: none !important
}
label {
    font-size: 15px;
    font-weight: 400;
    line-height: 2.3
}
.form-control::-moz-placeholder, .select2-search input[type=text]::-moz-placeholder {
    color: #b2bcc5;
    opacity: 1
}
.form-control:-ms-input-placeholder, .select2-search input[type=text]:-ms-input-placeholder {
    color: #b2bcc5
}
.form-control::-webkit-input-placeholder, .select2-search input[type=text]::-webkit-input-placeholder {
    color: #b2bcc5
}
.form-control, .select2-search input[type=text] {
    height: 42px;
    padding: 8px 12px;
    font-family: Lato, Helvetica, Arial, sans-serif;
    font-size: 15px;
    line-height: 1.467;
    color: #34495e;
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    box-shadow: none;
    -webkit-transition: border .25s linear, color .25s linear, background-color .25s linear;
    transition: border .25s linear, color .25s linear, background-color .25s linear
}
.form-group.focus .form-control, .form-control:focus, .form-group.focus .select2-search input[type=text], .select2-search input[type=text]:focus {
    border-color: #8f9fe2;
    outline: 0;
    box-shadow: none
}
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control, .select2-search input[type=text][disabled], .select2-search input[type=text][readonly], fieldset[disabled] .select2-search input[type=text] {
    color: #d5dbdb;
    cursor: default;
    background-color: #f4f6f6;
    filter: alpha(opacity=70);
    border-color: #d5dbdb;
    opacity: .7
}
.form-control.flat, .select2-search input[type=text].flat {
    border-color: transparent
}
.form-control.flat:hover, .select2-search input[type=text].flat:hover {
    border-color: #bdc3c7
}
.form-control.flat:focus, .select2-search input[type=text].flat:focus {
    border-color: #8f9fe2
}
.input-sm, .form-group-sm .form-control, .form-group-sm .select2-search input[type=text], .select2-search input[type=text] {
    height: 35px;
    padding: 6px 10px;
    font-size: 13px;
    line-height: 1.462;
    border-radius: 6px
}
select.input-sm, select.form-group-sm .form-control, select.form-group-sm .select2-search input[type=text], select.select2-search input[type=text] {
    height: 35px;
    line-height: 35px
}
textarea.input-sm, textarea.form-group-sm .form-control, select[multiple].input-sm, select[multiple].form-group-sm .form-control, textarea.form-group-sm .select2-search input[type=text], select[multiple].form-group-sm .select2-search input[type=text], textarea.select2-search input[type=text], select[multiple].select2-search input[type=text] {
    height: auto
}
.input-lg, .form-group-lg .form-control, .form-group-lg .select2-search input[type=text] {
    height: 45px;
    padding: 10px 15px;
    font-size: 17px;
    line-height: 1.235;
    border-radius: 6px
}
select.input-lg, select.form-group-lg .form-control, select.form-group-lg .select2-search input[type=text] {
    height: 45px;
    line-height: 45px
}
textarea.input-lg, textarea.form-group-lg .form-control, select[multiple].input-lg, select[multiple].form-group-lg .form-control, textarea.form-group-lg .select2-search input[type=text], select[multiple].form-group-lg .select2-search input[type=text] {
    height: auto
}
.input-hg, .form-group-hg .form-control, .form-horizontal .form-group-hg .form-control, .form-group-hg .select2-search input[type=text], .form-horizontal .form-group-hg .select2-search input[type=text] {
    height: 53px;
    padding: 10px 16px;
    font-size: 22px;
    line-height: 1.318;
    border-radius: 6px
}
select.input-hg, select.form-group-hg .form-control, select.form-group-hg .select2-search input[type=text] {
    height: 53px;
    line-height: 53px
}
textarea.input-hg, textarea.form-group-hg .form-control, select[multiple].input-hg, select[multiple].form-group-hg .form-control, textarea.form-group-hg .select2-search input[type=text], select[multiple].form-group-hg .select2-search input[type=text] {
    height: auto
}
.form-control-feedback {
    position: absolute;
    top: 2px;
    right: 2px;
    padding: 0 12px 0 0;
    margin-top: 1px;
    font-size: 17px;
    line-height: 36px;
    color: #b2bcc5;
    pointer-events: none;
    background-color: transparent;
    border-radius: 6px
}
.input-hg + .form-control-feedback, .control-feedback-hg {
    width: auto;
    height: 48px;
    padding-right: 16px;
    font-size: 20px;
    line-height: 48px
}
.input-lg + .form-control-feedback, .control-feedback-lg {
    width: auto;
    height: 40px;
    padding-right: 15px;
    font-size: 18px;
    line-height: 40px
}
.input-sm + .form-control-feedback, .control-feedback-sm, .select2-search input[type=text] + .form-control-feedback {
    width: auto;
    height: 29px;
    padding-right: 10px;
    line-height: 29px
}
.has-success .help-block, .has-success .control-label, .has-success .radio, .has-success .checkbox, .has-success .radio-inline, .has-success .checkbox-inline {
    color: #2ecc71
}
.has-success .form-control, .has-success .select2-search input[type=text] {
    color: #2ecc71;
    border-color: #2ecc71;
    box-shadow: none
}
.has-success .form-control::-moz-placeholder, .has-success .select2-search input[type=text]::-moz-placeholder {
    color: #2ecc71;
    opacity: 1
}
.has-success .form-control:-ms-input-placeholder, .has-success .select2-search input[type=text]:-ms-input-placeholder {
    color: #2ecc71
}
.has-success .form-control::-webkit-input-placeholder, .has-success .select2-search input[type=text]::-webkit-input-placeholder {
    color: #2ecc71
}
.has-success .form-control:focus, .has-success .select2-search input[type=text]:focus {
    border-color: #2ecc71;
    box-shadow: none
}
.has-success .input-group-addon {
    color: #2ecc71;
    background-color: #fff;
    border-color: #2ecc71
}
.has-success .form-control-feedback {
    color: #2ecc71
}
.has-warning .help-block, .has-warning .control-label, .has-warning .radio, .has-warning .checkbox, .has-warning .radio-inline, .has-warning .checkbox-inline {
    color: #f1c40f
}
.has-warning .form-control, .has-warning .select2-search input[type=text] {
    color: #f1c40f;
    border-color: #f1c40f;
    box-shadow: none
}
.has-warning .form-control::-moz-placeholder, .has-warning .select2-search input[type=text]::-moz-placeholder {
    color: #f1c40f;
    opacity: 1
}
.has-warning .form-control:-ms-input-placeholder, .has-warning .select2-search input[type=text]:-ms-input-placeholder {
    color: #f1c40f
}
.has-warning .form-control::-webkit-input-placeholder, .has-warning .select2-search input[type=text]::-webkit-input-placeholder {
    color: #f1c40f
}
.has-warning .form-control:focus, .has-warning .select2-search input[type=text]:focus {
    border-color: #f1c40f;
    box-shadow: none
}
.has-warning .input-group-addon {
    color: #f1c40f;
    background-color: #fff;
    border-color: #f1c40f
}
.has-warning .form-control-feedback {
    color: #f1c40f
}
.has-error .help-block, .has-error .control-label, .has-error .radio, .has-error .checkbox, .has-error .radio-inline, .has-error .checkbox-inline {
    color: #e74c3c
}
.has-error .form-control, .has-error .select2-search input[type=text] {
    color: #e74c3c;
    border-color: #e74c3c;
    box-shadow: none
}
.has-error .form-control::-moz-placeholder, .has-error .select2-search input[type=text]::-moz-placeholder {
    color: #e74c3c;
    opacity: 1
}
.has-error .form-control:-ms-input-placeholder, .has-error .select2-search input[type=text]:-ms-input-placeholder {
    color: #e74c3c
}
.has-error .form-control::-webkit-input-placeholder, .has-error .select2-search input[type=text]::-webkit-input-placeholder {
    color: #e74c3c
}
.has-error .form-control:focus, .has-error .select2-search input[type=text]:focus {
    border-color: #e74c3c;
    box-shadow: none
}
.has-error .input-group-addon {
    color: #e74c3c;
    background-color: #fff;
    border-color: #e74c3c
}
.has-error .form-control-feedback {
    color: #e74c3c
}
.form-control[disabled] + .form-control-feedback, .form-control[readonly] + .form-control-feedback, fieldset[disabled] .form-control + .form-control-feedback, .form-control.disabled + .form-control-feedback, .select2-search input[type=text][disabled] + .form-control-feedback, .select2-search input[type=text][readonly] + .form-control-feedback, fieldset[disabled] .select2-search input[type=text] + .form-control-feedback, .select2-search input[type=text].disabled + .form-control-feedback {
    color: #d5dbdb;
    cursor: not-allowed;
    background-color: transparent;
    filter: alpha(opacity=70);
    opacity: .7
}
.help-block {
    margin-bottom: 5px;
    font-size: 14px;
    color: #6b7a88
}
.form-group {
    position: relative;
    margin-bottom: 20px
}
.form-horizontal .radio, .form-horizontal .checkbox, .form-horizontal .radio-inline, .form-horizontal .checkbox-inline {
    padding-top: 0;
    margin-top: 0;
    margin-bottom: 0
}
@media (min-width: 768px) {
    .form-horizontal .control-label {
        padding-top: 3px;
        padding-bottom: 3px
    }
}
.form-horizontal .form-group {
    margin-right: -15px;
    margin-left: -15px
}
.form-horizontal .form-control-static {
    padding-top: 6px;
    padding-bottom: 6px
}
@media (min-width: 768px) {
    .form-horizontal .form-group-hg .control-label {
        padding-top: 2px;
        padding-bottom: 0;
        font-size: 22px;

    }
}
@media (min-width: 768px) {
    .form-horizontal .form-group-lg .control-label {
        padding-top: 3px;
        padding-bottom: 2px;
        font-size: 17px
    }
}
@media (min-width: 768px) {
    .form-horizontal .form-group-sm .control-label {
        padding-top: 2px;
        padding-bottom: 2px;
        font-size: 13px
    }
}
.input-group .form-control, .input-group .select2-search input[type=text] {
    position: static
}
.input-group-hg > .form-control, .input-group-hg > .input-group-addon, .input-group-hg > .input-group-btn > .btn, .input-group-hg > .select2-search input[type=text] {
    height: 53px;
    padding: 10px 16px;
    font-size: 22px;
    line-height: 1.318;
    border-radius: 6px
}
select.input-group-hg > .form-control, select.input-group-hg > .input-group-addon, select.input-group-hg > .input-group-btn > .btn, select.input-group-hg > .select2-search input[type=text] {
    height: 53px;
    line-height: 53px
}
textarea.input-group-hg > .form-control, textarea.input-group-hg > .input-group-addon, textarea.input-group-hg > .input-group-btn > .btn, select[multiple].input-group-hg > .form-control, select[multiple].input-group-hg > .input-group-addon, select[multiple].input-group-hg > .input-group-btn > .btn, textarea.input-group-hg > .select2-search input[type=text], select[multiple].input-group-hg > .select2-search input[type=text] {
    height: auto
}
.input-group-lg > .form-control, .input-group-lg > .input-group-addon, .input-group-lg > .input-group-btn > .btn, .input-group-lg > .select2-search input[type=text] {
    height: 45px;
    padding: 10px 15px;
    font-size: 17px;
    line-height: 1.235;
    border-radius: 6px
}
select.input-group-lg > .form-control, select.input-group-lg > .input-group-addon, select.input-group-lg > .input-group-btn > .btn, select.input-group-lg > .select2-search input[type=text] {
    height: 45px;
    line-height: 45px
}
textarea.input-group-lg > .form-control, textarea.input-group-lg > .input-group-addon, textarea.input-group-lg > .input-group-btn > .btn, select[multiple].input-group-lg > .form-control, select[multiple].input-group-lg > .input-group-addon, select[multiple].input-group-lg > .input-group-btn > .btn, textarea.input-group-lg > .select2-search input[type=text], select[multiple].input-group-lg > .select2-search input[type=text] {
    height: auto
}
.input-group-sm > .form-control, .input-group-sm > .input-group-addon, .input-group-sm > .input-group-btn > .btn, .input-group-sm > .select2-search input[type=text] {
    height: 35px;
    padding: 6px 10px;
    font-size: 13px;
    line-height: 1.462;
    border-radius: 6px
}
select.input-group-sm > .form-control, select.input-group-sm > .input-group-addon, select.input-group-sm > .input-group-btn > .btn, select.input-group-sm > .select2-search input[type=text] {
    height: 35px;
    line-height: 35px
}
textarea.input-group-sm > .form-control, textarea.input-group-sm > .input-group-addon, textarea.input-group-sm > .input-group-btn > .btn, select[multiple].input-group-sm > .form-control, select[multiple].input-group-sm > .input-group-addon, select[multiple].input-group-sm > .input-group-btn > .btn, textarea.input-group-sm > .select2-search input[type=text], select[multiple].input-group-sm > .select2-search input[type=text] {
    height: auto
}
.input-group-addon {
    padding: 10px 12px;
    font-size: 15px;
    color: #fff;
    text-align: center;
    background-color: #bdc3c7;
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    -webkit-transition: border .25s linear, color .25s linear, background-color .25s linear;
    transition: border .25s linear, color .25s linear, background-color .25s linear
}
.input-group-hg .input-group-addon, .input-group-lg .input-group-addon, .input-group-sm .input-group-addon {
    line-height: 1
}
.input-group .form-control:first-child, .input-group-addon:first-child, .input-group-btn:first-child > .btn, .input-group-btn:first-child > .dropdown-toggle, .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle), .input-group .select2-search input[type=text]:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}
.input-group .form-control:last-child, .input-group-addon:last-child, .input-group-btn:last-child > .btn, .input-group-btn:last-child > .dropdown-toggle, .input-group-btn:first-child > .btn:not(:first-child), .input-group .select2-search input[type=text]:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}
.form-group.focus .input-group-addon, .input-group.focus .input-group-addon {
    background-color: #585f7a;
    border-color: #8f9fe2
}
.form-group.focus .input-group-btn > .btn-default + .btn-default, .input-group.focus .input-group-btn > .btn-default + .btn-default {
    border-left-color: #16a085
}
.form-group.focus .input-group-btn .btn, .input-group.focus .input-group-btn .btn {
    color: #8f9fe2;
    background-color: #fff;
    border-color: #8f9fe2
}
.form-group.focus .input-group-btn .btn-default, .input-group.focus .input-group-btn .btn-default {
    color: #fff;
    background-color: #585f7a
}
.form-group.focus .input-group-btn .btn-default:hover, .input-group.focus .input-group-btn .btn-default:hover, .form-group.focus .input-group-btn .btn-default.hover, .input-group.focus .input-group-btn .btn-default.hover, .form-group.focus .input-group-btn .btn-default:focus, .input-group.focus .input-group-btn .btn-default:focus, .form-group.focus .input-group-btn .btn-default:active, .input-group.focus .input-group-btn .btn-default:active, .form-group.focus .input-group-btn .btn-default.active, .input-group.focus .input-group-btn .btn-default.active, .open > .dropdown-toggle.form-group.focus .input-group-btn .btn-default, .open > .dropdown-toggle.input-group.focus .input-group-btn .btn-default {
    color: #fff;
    background-color: #48c9b0;
    border-color: #48c9b0
}
.form-group.focus .input-group-btn .btn-default:active, .input-group.focus .input-group-btn .btn-default:active, .form-group.focus .input-group-btn .btn-default.active, .input-group.focus .input-group-btn .btn-default.active, .open > .dropdown-toggle.form-group.focus .input-group-btn .btn-default, .open > .dropdown-toggle.input-group.focus .input-group-btn .btn-default {
    background: #16a085;
    border-color: #16a085
}
.form-group.focus .input-group-btn .btn-default.disabled, .input-group.focus .input-group-btn .btn-default.disabled, .form-group.focus .input-group-btn .btn-default[disabled], .input-group.focus .input-group-btn .btn-default[disabled], fieldset[disabled] .form-group.focus .input-group-btn .btn-default, fieldset[disabled] .input-group.focus .input-group-btn .btn-default, .form-group.focus .input-group-btn .btn-default.disabled:hover, .input-group.focus .input-group-btn .btn-default.disabled:hover, .form-group.focus .input-group-btn .btn-default[disabled]:hover, .input-group.focus .input-group-btn .btn-default[disabled]:hover, fieldset[disabled] .form-group.focus .input-group-btn .btn-default:hover, fieldset[disabled] .input-group.focus .input-group-btn .btn-default:hover, .form-group.focus .input-group-btn .btn-default.disabled.hover, .input-group.focus .input-group-btn .btn-default.disabled.hover, .form-group.focus .input-group-btn .btn-default[disabled].hover, .input-group.focus .input-group-btn .btn-default[disabled].hover, fieldset[disabled] .form-group.focus .input-group-btn .btn-default.hover, fieldset[disabled] .input-group.focus .input-group-btn .btn-default.hover, .form-group.focus .input-group-btn .btn-default.disabled:focus, .input-group.focus .input-group-btn .btn-default.disabled:focus, .form-group.focus .input-group-btn .btn-default[disabled]:focus, .input-group.focus .input-group-btn .btn-default[disabled]:focus, fieldset[disabled] .form-group.focus .input-group-btn .btn-default:focus, fieldset[disabled] .input-group.focus .input-group-btn .btn-default:focus, .form-group.focus .input-group-btn .btn-default.disabled:active, .input-group.focus .input-group-btn .btn-default.disabled:active, .form-group.focus .input-group-btn .btn-default[disabled]:active, .input-group.focus .input-group-btn .btn-default[disabled]:active, fieldset[disabled] .form-group.focus .input-group-btn .btn-default:active, fieldset[disabled] .input-group.focus .input-group-btn .btn-default:active, .form-group.focus .input-group-btn .btn-default.disabled.active, .input-group.focus .input-group-btn .btn-default.disabled.active, .form-group.focus .input-group-btn .btn-default[disabled].active, .input-group.focus .input-group-btn .btn-default[disabled].active, fieldset[disabled] .form-group.focus .input-group-btn .btn-default.active, fieldset[disabled] .input-group.focus .input-group-btn .btn-default.active {
    background-color: #bdc3c7;
    border-color: #8f9fe2
}
.form-group.focus .input-group-btn .btn-default .badge, .input-group.focus .input-group-btn .btn-default .badge {
    color: #8f9fe2;
    background-color: #fff
}
.input-group-btn .btn {
    height: 42px;
    line-height: 18px;
    color: #bdc3c7;
    background-color: #fff;
    border: 2px solid #bdc3c7
}
.input-group-btn .btn-default {
    color: #fff;
    background-color: #bdc3c7
}
.input-group-btn .btn-default:hover, .input-group-btn .btn-default.hover, .input-group-btn .btn-default:focus, .input-group-btn .btn-default:active, .input-group-btn .btn-default.active, .open > .dropdown-toggle.input-group-btn .btn-default {
    color: #fff;
    background-color: #cacfd2;
    border-color: #cacfd2
}
.input-group-btn .btn-default:active, .input-group-btn .btn-default.active, .open > .dropdown-toggle.input-group-btn .btn-default {
    background: #a1a6a9;
    border-color: #a1a6a9
}
.input-group-btn .btn-default.disabled, .input-group-btn .btn-default[disabled], fieldset[disabled] .input-group-btn .btn-default, .input-group-btn .btn-default.disabled:hover, .input-group-btn .btn-default[disabled]:hover, fieldset[disabled] .input-group-btn .btn-default:hover, .input-group-btn .btn-default.disabled.hover, .input-group-btn .btn-default[disabled].hover, fieldset[disabled] .input-group-btn .btn-default.hover, .input-group-btn .btn-default.disabled:focus, .input-group-btn .btn-default[disabled]:focus, fieldset[disabled] .input-group-btn .btn-default:focus, .input-group-btn .btn-default.disabled:active, .input-group-btn .btn-default[disabled]:active, fieldset[disabled] .input-group-btn .btn-default:active, .input-group-btn .btn-default.disabled.active, .input-group-btn .btn-default[disabled].active, fieldset[disabled] .input-group-btn .btn-default.active {
    background-color: #bdc3c7;
    border-color: #bdc3c7
}
.input-group-btn .btn-default .badge {
    color: #bdc3c7;
    background-color: #fff
}
.input-group-hg .input-group-btn .btn {
    line-height: 31px
}
.input-group-lg .input-group-btn .btn {
    line-height: 21px
}
.input-group-sm .input-group-btn .btn {
    line-height: 19px
}
.input-group-btn:first-child > .btn {
    margin-right: -3px;
    border-right-width: 0
}
.input-group-btn:last-child > .btn {
    margin-left: -3px;
    border-left-width: 0
}
.input-group-btn > .btn-default + .btn-default {
    border-left: 2px solid #bdc3c7
}
.input-group-btn > .btn:first-child + .btn .caret {
    margin-left: 0
}
.input-group-rounded .input-group-btn + .form-control, .input-group-rounded .input-group-btn:last-child .btn, .input-group-rounded .input-group-btn + .select2-search input[type=text] {
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px
}
.input-group-hg.input-group-rounded .input-group-btn + .form-control, .input-group-hg.input-group-rounded .input-group-btn:last-child .btn, .input-group-hg.input-group-rounded .input-group-btn + .select2-search input[type=text] {
    border-top-right-radius: 27px;
    border-bottom-right-radius: 27px
}
.input-group-lg.input-group-rounded .input-group-btn + .form-control, .input-group-lg.input-group-rounded .input-group-btn:last-child .btn, .input-group-lg.input-group-rounded .input-group-btn + .select2-search input[type=text] {
    border-top-right-radius: 25px;
    border-bottom-right-radius: 25px
}
.input-group-rounded .form-control:first-child, .input-group-rounded .input-group-btn:first-child .btn, .input-group-rounded .select2-search input[type=text]:first-child {
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px
}
.input-group-hg.input-group-rounded .form-control:first-child, .input-group-hg.input-group-rounded .input-group-btn:first-child .btn, .input-group-hg.input-group-rounded .select2-search input[type=text]:first-child {
    border-top-left-radius: 27px;
    border-bottom-left-radius: 27px
}
.input-group-lg.input-group-rounded .form-control:first-child, .input-group-lg.input-group-rounded .input-group-btn:first-child .btn, .input-group-lg.input-group-rounded .select2-search input[type=text]:first-child {
    border-top-left-radius: 25px;
    border-bottom-left-radius: 25px
}
.input-group-rounded .input-group-btn + .form-control, .input-group-rounded .input-group-btn + .select2-search input[type=text] {
    padding-left: 0
}
.checkbox, .radio {
    position: relative;
    padding-left: 32px;
    margin-bottom: 12px;
    font-size: 14px;
    line-height: 1.5;
    -webkit-transition: color .25s linear;
    transition: color .25s linear
}
.checkbox .icons, .radio .icons {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 20px;
    height: 20px;
    font-size: 20px;
    line-height: 20px;
    color: #bdc3c7;
    text-align: center;
    cursor: pointer
}
.checkbox .icons .icon-checked, .radio .icons .icon-checked {
    filter: alpha(opacity=0);
    opacity: 0
}
.checkbox .icon-checked, .radio .icon-checked, .checkbox .icon-unchecked, .radio .icon-unchecked {
    position: absolute;
    top: 0;
    left: 0;
    display: inline-table;
    margin: 0;
    background-color: transparent;
    filter: none;
    opacity: 1;
    -webkit-transition: color .25s linear;
    transition: color .25s linear;
    -webkit-filter: none
}
.checkbox .icon-checked:before, .radio .icon-checked:before, .checkbox .icon-unchecked:before, .radio .icon-unchecked:before {
    font-family: Flat-UI-Icons;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    speak: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.checkbox .icon-checked:before {
    content: "\e60e"
}
.checkbox .icon-unchecked:before {
    content: "\e60d"
}
.radio .icon-checked:before {
    content: "\e60c"
}
.radio .icon-unchecked:before {
    content: "\e60b"
}
.checkbox input[type=checkbox].custom-checkbox, .radio input[type=checkbox].custom-checkbox, .checkbox input[type=radio].custom-radio, .radio input[type=radio].custom-radio {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    padding: 0;
    margin: 0;
    outline: 0 !important;
    opacity: 0
}
.checkbox input[type=checkbox].custom-checkbox:hover:not(.nohover):not(:disabled) + .icons .icon-unchecked, .radio input[type=checkbox].custom-checkbox:hover:not(.nohover):not(:disabled) + .icons .icon-unchecked, .checkbox input[type=radio].custom-radio:hover:not(.nohover):not(:disabled) + .icons .icon-unchecked, .radio input[type=radio].custom-radio:hover:not(.nohover):not(:disabled) + .icons .icon-unchecked {
    filter: alpha(opacity=0);
    opacity: 0
}
.checkbox input[type=checkbox].custom-checkbox:hover:not(.nohover):not(:disabled) + .icons .icon-checked, .radio input[type=checkbox].custom-checkbox:hover:not(.nohover):not(:disabled) + .icons .icon-checked, .checkbox input[type=radio].custom-radio:hover:not(.nohover):not(:disabled) + .icons .icon-checked, .radio input[type=radio].custom-radio:hover:not(.nohover):not(:disabled) + .icons .icon-checked {
    filter: none;
    opacity: 1;
    -webkit-filter: none
}
.checkbox input[type=checkbox].custom-checkbox:checked + .icons, .radio input[type=checkbox].custom-checkbox:checked + .icons, .checkbox input[type=radio].custom-radio:checked + .icons, .radio input[type=radio].custom-radio:checked + .icons {
    color: #8f9fe2
}
.checkbox input[type=checkbox].custom-checkbox:checked + .icons .icon-unchecked, .radio input[type=checkbox].custom-checkbox:checked + .icons .icon-unchecked, .checkbox input[type=radio].custom-radio:checked + .icons .icon-unchecked, .radio input[type=radio].custom-radio:checked + .icons .icon-unchecked {
    filter: alpha(opacity=0);
    opacity: 0
}
.checkbox input[type=checkbox].custom-checkbox:checked + .icons .icon-checked, .radio input[type=checkbox].custom-checkbox:checked + .icons .icon-checked, .checkbox input[type=radio].custom-radio:checked + .icons .icon-checked, .radio input[type=radio].custom-radio:checked + .icons .icon-checked {
    color: #8f9fe2;
    filter: none;
    opacity: 1;
    -webkit-filter: none
}
.checkbox input[type=checkbox].custom-checkbox:disabled + .icons, .radio input[type=checkbox].custom-checkbox:disabled + .icons, .checkbox input[type=radio].custom-radio:disabled + .icons, .radio input[type=radio].custom-radio:disabled + .icons {
    color: #e6e8ea;
    cursor: default
}
.checkbox input[type=checkbox].custom-checkbox:disabled + .icons .icon-unchecked, .radio input[type=checkbox].custom-checkbox:disabled + .icons .icon-unchecked, .checkbox input[type=radio].custom-radio:disabled + .icons .icon-unchecked, .radio input[type=radio].custom-radio:disabled + .icons .icon-unchecked {
    filter: none;
    opacity: 1;
    -webkit-filter: none
}
.checkbox input[type=checkbox].custom-checkbox:disabled + .icons .icon-checked, .radio input[type=checkbox].custom-checkbox:disabled + .icons .icon-checked, .checkbox input[type=radio].custom-radio:disabled + .icons .icon-checked, .radio input[type=radio].custom-radio:disabled + .icons .icon-checked {
    filter: alpha(opacity=0);
    opacity: 0
}
.checkbox input[type=checkbox].custom-checkbox:disabled:checked + .icons, .radio input[type=checkbox].custom-checkbox:disabled:checked + .icons, .checkbox input[type=radio].custom-radio:disabled:checked + .icons, .radio input[type=radio].custom-radio:disabled:checked + .icons {
    color: #e6e8ea
}
.checkbox input[type=checkbox].custom-checkbox:disabled:checked + .icons .icon-unchecked, .radio input[type=checkbox].custom-checkbox:disabled:checked + .icons .icon-unchecked, .checkbox input[type=radio].custom-radio:disabled:checked + .icons .icon-unchecked, .radio input[type=radio].custom-radio:disabled:checked + .icons .icon-unchecked {
    filter: alpha(opacity=0);
    opacity: 0
}
.checkbox input[type=checkbox].custom-checkbox:disabled:checked + .icons .icon-checked, .radio input[type=checkbox].custom-checkbox:disabled:checked + .icons .icon-checked, .checkbox input[type=radio].custom-radio:disabled:checked + .icons .icon-checked, .radio input[type=radio].custom-radio:disabled:checked + .icons .icon-checked {
    color: #e6e8ea;
    filter: none;
    opacity: 1;
    -webkit-filter: none
}
.checkbox input[type=checkbox].custom-checkbox:indeterminate + .icons, .radio input[type=checkbox].custom-checkbox:indeterminate + .icons, .checkbox input[type=radio].custom-radio:indeterminate + .icons, .radio input[type=radio].custom-radio:indeterminate + .icons {
    color: #bdc3c7
}
.checkbox input[type=checkbox].custom-checkbox:indeterminate + .icons .icon-unchecked, .radio input[type=checkbox].custom-checkbox:indeterminate + .icons .icon-unchecked, .checkbox input[type=radio].custom-radio:indeterminate + .icons .icon-unchecked, .radio input[type=radio].custom-radio:indeterminate + .icons .icon-unchecked {
    filter: none;
    opacity: 1;
    -webkit-filter: none
}
.checkbox input[type=checkbox].custom-checkbox:indeterminate + .icons .icon-checked, .radio input[type=checkbox].custom-checkbox:indeterminate + .icons .icon-checked, .checkbox input[type=radio].custom-radio:indeterminate + .icons .icon-checked, .radio input[type=radio].custom-radio:indeterminate + .icons .icon-checked {
    filter: alpha(opacity=0);
    opacity: 0
}
.checkbox input[type=checkbox].custom-checkbox:indeterminate + .icons:before, .radio input[type=checkbox].custom-checkbox:indeterminate + .icons:before, .checkbox input[type=radio].custom-radio:indeterminate + .icons:before, .radio input[type=radio].custom-radio:indeterminate + .icons:before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    width: 20px;
    font-size: 22px;
    line-height: 20px;
    color: #fff;
    text-align: center;
    content: "\2013"
}
.checkbox.primary input[type=checkbox].custom-checkbox + .icons, .radio.primary input[type=checkbox].custom-checkbox + .icons, .checkbox.primary input[type=radio].custom-radio + .icons, .radio.primary input[type=radio].custom-radio + .icons {
    color: #34495e
}
.checkbox.primary input[type=checkbox].custom-checkbox:checked + .icons, .radio.primary input[type=checkbox].custom-checkbox:checked + .icons, .checkbox.primary input[type=radio].custom-radio:checked + .icons, .radio.primary input[type=radio].custom-radio:checked + .icons {
    color: #8f9fe2
}
.checkbox.primary input[type=checkbox].custom-checkbox:disabled + .icons, .radio.primary input[type=checkbox].custom-checkbox:disabled + .icons, .checkbox.primary input[type=radio].custom-radio:disabled + .icons, .radio.primary input[type=radio].custom-radio:disabled + .icons {
    color: #bdc3c7;
    cursor: default
}
.checkbox.primary input[type=checkbox].custom-checkbox:disabled + .icons.checked, .radio.primary input[type=checkbox].custom-checkbox:disabled + .icons.checked, .checkbox.primary input[type=radio].custom-radio:disabled + .icons.checked, .radio.primary input[type=radio].custom-radio:disabled + .icons.checked {
    color: #bdc3c7
}
.checkbox.primary input[type=checkbox].custom-checkbox:indeterminate + .icons, .radio.primary input[type=checkbox].custom-checkbox:indeterminate + .icons, .checkbox.primary input[type=radio].custom-radio:indeterminate + .icons, .radio.primary input[type=radio].custom-radio:indeterminate + .icons {
    color: #34495e
}
.input-group-addon .radio, .input-group-addon .checkbox {
    padding-left: 20px;
    margin: -2px 0
}
.input-group-addon .radio .icons, .input-group-addon .checkbox .icons {
    color: #e6e8ea
}
.input-group-addon .radio input[type=checkbox].custom-checkbox:checked + .icons, .input-group-addon .checkbox input[type=checkbox].custom-checkbox:checked + .icons, .input-group-addon .radio input[type=radio].custom-radio:checked + .icons, .input-group-addon .checkbox input[type=radio].custom-radio:checked + .icons {
    color: #fff
}
.input-group-addon .radio input[type=checkbox].custom-checkbox:checked + .icons .icon-checked, .input-group-addon .checkbox input[type=checkbox].custom-checkbox:checked + .icons .icon-checked, .input-group-addon .radio input[type=radio].custom-radio:checked + .icons .icon-checked, .input-group-addon .checkbox input[type=radio].custom-radio:checked + .icons .icon-checked {
    color: #fff
}
.input-group-addon .radio input[type=checkbox].custom-checkbox:disabled + .icons, .input-group-addon .checkbox input[type=checkbox].custom-checkbox:disabled + .icons, .input-group-addon .radio input[type=radio].custom-radio:disabled + .icons, .input-group-addon .checkbox input[type=radio].custom-radio:disabled + .icons {
    color: rgba(230, 232, 234, .6)
}
.input-group-addon .radio input[type=checkbox].custom-checkbox:disabled:checked + .icons, .input-group-addon .checkbox input[type=checkbox].custom-checkbox:disabled:checked + .icons, .input-group-addon .radio input[type=radio].custom-radio:disabled:checked + .icons, .input-group-addon .checkbox input[type=radio].custom-radio:disabled:checked + .icons {
    color: rgba(230, 232, 234, .6)
}
.input-group-addon .radio input[type=checkbox].custom-checkbox:disabled:checked + .icons .icon-checked, .input-group-addon .checkbox input[type=checkbox].custom-checkbox:disabled:checked + .icons .icon-checked, .input-group-addon .radio input[type=radio].custom-radio:disabled:checked + .icons .icon-checked, .input-group-addon .checkbox input[type=radio].custom-radio:disabled:checked + .icons .icon-checked {
    color: rgba(230, 232, 234, .6)
}
.radio + .radio, .checkbox + .checkbox {
    margin-top: 10px
}
.form-inline .checkbox, .form-inline .radio {
    padding-left: 32px
}
.bootstrap-tagsinput {
    padding: 6px 1px 1px 6px;
    margin-bottom: 18px;
    font-size: 0;
    text-align: left;
    background-color: #fff;
    border: 2px solid #ebedef;
    border-radius: 6px
}
.bootstrap-tagsinput .tag {
    position: relative;
    display: inline-block;
    height: 27px;
    padding: 6px 21px;
    margin: 0 7px 7px 0;
    overflow: hidden;
    font-size: 13px;
    line-height: 15px;
    color: #7b8996;
    vertical-align: middle;
    cursor: pointer;
    background-color: #ebedef;
    border-radius: 4px;
    -webkit-transition: .25s linear;
    transition: .25s linear
}
.bootstrap-tagsinput .tag > span {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    width: 100%;
    padding: 0 10px 0 0;
    font-size: 12px;
    color: #fff;
    text-align: right;
    text-decoration: none;
    cursor: pointer;
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transition: opacity .25s linear;
    transition: opacity .25s linear
}
.bootstrap-tagsinput .tag > span:after {
    font-family: Flat-UI-Icons;
    line-height: 27px;
    content: "\e609";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.bootstrap-tagsinput .tag:hover {
    padding-right: 28px;
    padding-left: 14px;
    color: #fff;
    background-color: #16a085
}
.bootstrap-tagsinput .tag:hover > span {
    filter: none;
    opacity: 1;
    -webkit-filter: none
}
.bootstrap-tagsinput input[type=text] {
    width: auto !important;
    min-width: 80px;
    max-width: inherit;
    height: 29px;
    padding: 0;
    margin: 0;
    font-size: 14px;
    color: #34495e;
    vertical-align: top;
    background-color: transparent;
    border: none;
    outline: 0;
    box-shadow: none
}
.bootstrap-tagsinput input[type=text]:first-child {
    height: 23px;
    margin: 3px 0 8px
}
.tags_clear {
    width: 100%;
    height: 0;
    clear: both
}
.not_valid {
    margin-left: 5px !important;
    color: #90111a !important;
    background: #fbd8db !important
}
.tagsinput-primary {
    margin-bottom: 18px
}
.tagsinput-primary .bootstrap-tagsinput {
    margin-bottom: 0;
    border-color: #8f9fe2
}
.tagsinput-primary .tag {
    color: #fff;
    background-color: #585f7a
}
.tagsinput-primary .tag:hover {
    color: #fff;
    background-color: #16a085
}
.bootstrap-tagsinput .twitter-typeahead {
    width: auto;
    vertical-align: top
}
.bootstrap-tagsinput .twitter-typeahead .tt-input {
    min-width: 200px
}
.bootstrap-tagsinput .twitter-typeahead .tt-dropdown-menu {
    width: auto;
    min-width: 120px;
    margin-top: 11px
}
.twitter-typeahead {
    width: 100%
}
.twitter-typeahead .tt-dropdown-menu {
    width: 100%;
    padding: 5px 0;
    margin-top: 5px;
    background-color: #fff;
    border: 2px solid #585f7a;
    border-radius: 6px
}
.twitter-typeahead .tt-suggestion p {
    padding: 6px 14px;
    margin: 0;
    font-size: 14px;
    line-height: 1.429
}
.twitter-typeahead .tt-suggestion:first-child p, .twitter-typeahead .tt-suggestion:last-child p {
    padding: 6px 14px
}
.twitter-typeahead .tt-suggestion.tt-is-under-cursor, .twitter-typeahead .tt-suggestion.tt-cursor {
    color: #fff;
    cursor: pointer;
    background-color: #16a085
}
.progress {
    height: 12px;
    background: #ebedef;
    border-radius: 32px;
    box-shadow: none
}
.progress-bar {
    line-height: 12px;
    background: #585f7a;
    box-shadow: none
}
.progress-bar-success {
    background-color: #2ecc71
}
.progress-bar-warning {
    background-color: #f1c40f
}
.progress-bar-danger {
    background-color: #e74c3c
}
.progress-bar-info {
    background-color: #3498db
}
.ui-slider {
    position: relative;
    height: 12px;
    margin-bottom: 20px;
    cursor: pointer;
    background: #ebedef;
    border-radius: 32px;
    box-shadow: none
}
.ui-slider-handle {
    position: absolute;
    z-index: 2;
    width: 18px;
    height: 18px;
    cursor: pointer;
    background-color: #16a085;
    border-radius: 50%;
    -webkit-transition: background .25s;
    transition: background .25s
}
.ui-slider-handle:hover, .ui-slider-handle:focus {
    background-color: #48c9b0;
    outline: 0
}
.ui-slider-handle:active {
    background-color: #16a085
}
.ui-slider-range {
    position: absolute;
    z-index: 1;
    display: block;
    height: 100%;
    background-color: #585f7a
}
.ui-slider-segment {
    width: 6px;
    height: 6px;
    background-color: #d9dbdd;
    border-radius: 50%
}
.ui-slider-value {
    float: right;
    margin-top: 12px;
    font-size: 13px
}
.ui-slider-value.first {
    float: left;
    clear: left
}
.ui-slider-horizontal .ui-slider-handle {
    top: -3px;
    margin-left: -9px
}
.ui-slider-horizontal .ui-slider-handle[style*="100"] {
    margin-left: -15px
}
.ui-slider-horizontal .ui-slider-range {
    border-radius: 30px 0 0 30px
}
.ui-slider-horizontal .ui-slider-segment {
    float: left;
    margin: 3px -6px 0 0
}
.ui-slider-vertical {
    width: 12px
}
.ui-slider-vertical .ui-slider-handle {
    top: auto;
    margin-bottom: -11px;
    margin-left: -3px
}
.ui-slider-vertical .ui-slider-range {
    bottom: 0;
    width: 100%;
    border-radius: 0 0 30px 30px
}
.ui-slider-vertical .ui-slider-segment {
    position: absolute;
    right: 3px
}
.pager {
    display: inline-block;
    font-size: 16px;
    font-weight: 700;
    color: #fff;
    background-color: #34495e;
    border-radius: 6px
}
.pager li:first-child > a, .pager li:first-child > span {
    border-left: none;
    border-radius: 6px 0 0 6px
}
.pager li > a, .pager li > span {
    padding: 9px 15px 10px;
    line-height: 1.313;
    color: #fff;
    text-decoration: none;
    white-space: nowrap;
    background: 0 0;
    border: none;
    border-left: 2px solid #2c3e50;
    border-radius: 0 6px 6px 0
}
.pager li > a:hover, .pager li > span:hover, .pager li > a:focus, .pager li > span:focus {
    background-color: #2c3e50
}
.pager li > a:active, .pager li > span:active {
    background-color: #2c3e50
}
.pager li > a [class*=fui-] + span, .pager li > span [class*=fui-] + span {
    margin-left: 8px
}
.pager li > a span + [class*=fui-], .pager li > span span + [class*=fui-] {
    margin-left: 8px
}
.pagination {
    position: relative;
    display: block;
    display: inline-block;
    padding: 0;
    color: #fff;
    word-spacing: -.5px;
    background: #d6dbdf;
    border-radius: 6px
}
@media (min-width: 768px) {
    .pagination {
        display: inline-block
    }
}
@media (max-width: 767px) {
    .pagination {
        height: 41px;
        padding: 0 55px 0 52px;
        overflow: auto;
        white-space: nowrap;
        border-radius: 6px
    }
}
.pagination li {
    display: inline-block;
    margin-right: -2px;
    word-spacing: normal;
    vertical-align: middle
}
.pagination li a {
    position: static
}
.pagination li.active > a, .pagination li.active > span {
    color: #fff;
    background-color: #585f7a;
    border-color: #dfe2e5
}
.pagination li.active > a, .pagination li.active > span, .pagination li.active > a:hover, .pagination li.active > span:hover, .pagination li.active > a:focus, .pagination li.active > span:focus {
    color: #fff;
    background-color: #585f7a;
    border-color: #dfe2e5
}
.pagination li.active.previous > a, .pagination li.active.next > a, .pagination li.active.previous > span, .pagination li.active.next > span {
    margin: 0
}
.pagination li.active.previous > a, .pagination li.active.next > a, .pagination li.active.previous > span, .pagination li.active.next > span, .pagination li.active.previous > a:hover, .pagination li.active.next > a:hover, .pagination li.active.previous > span:hover, .pagination li.active.next > span:hover, .pagination li.active.previous > a:focus, .pagination li.active.next > a:focus, .pagination li.active.previous > span:focus, .pagination li.active.next > span:focus {
    color: #fff;
    background-color: #585f7a
}
.pagination li:first-child > a, .pagination li:first-child > span {
    border-left: none;
    border-radius: 6px 0 0 6px
}
.pagination li:first-child.previous + li > a, .pagination li:first-child.previous + li > span {
    border-left-width: 0
}
.pagination li:last-child {
    margin-right: 0
}
.pagination li:last-child > a, .pagination li:last-child > span, .pagination li:last-child > a:hover, .pagination li:last-child > span:hover, .pagination li:last-child > a:focus, .pagination li:last-child > span:focus {
    border-radius: 0 6px 6px 0
}
.pagination li.previous > a, .pagination li.next > a, .pagination li.previous > span, .pagination li.next > span {
    min-width: auto;
    padding: 12px 17px;
    font-size: 16px;
    background-color: transparent;
    border-right: 2px solid #e4e7ea
}
.pagination li.next > a, .pagination li.next > span {
    border-right: none
}
.pagination li.disabled > a, .pagination li.disabled > span {
    color: #fff;
    cursor: not-allowed;
    background-color: rgba(255, 255, 255, .3);
    border-right-color: #dfe2e5
}
.pagination li.disabled > a:hover, .pagination li.disabled > span:hover, .pagination li.disabled > a:focus, .pagination li.disabled > span:focus, .pagination li.disabled > a:active, .pagination li.disabled > span:active {
    color: #fff;
    background-color: rgba(255, 255, 255, .4)
}
@media (max-width: 767px) {
    .pagination li.next, .pagination li.previous {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 10;
        background-color: #d6dbdf;
        border-radius: 0 6px 6px 0
    }
    .pagination li.previous {
        right: auto;
        left: 0;
        border-radius: 6px 0 0 6px
    }
}
.pagination li > a, .pagination li > span {
    display: inline-block;
    min-width: 41px;
    min-height: 41px;
    padding: 12px 10px;
    font-size: 14px;
    line-height: 16px;
    color: #fff;
    text-align: center;
    background: 0 0;
    border: none;
    border-left: 2px solid #e4e7ea;
    outline: 0;
    -webkit-transition: .25s ease-out;
    transition: .25s ease-out
}
.pagination li > a:hover, .pagination li > span:hover, .pagination li > a:focus, .pagination li > span:focus {
    color: #fff;
    background-color: #585f7a
}
.pagination li > a:active, .pagination li > span:active {
    color: #fff;
    background-color: #585f7a
}
.pagination > .btn.previous, .pagination > .btn.next {
    padding-right: 23px;
    padding-left: 23px;
    margin-right: 8px;
    font-size: 14px;
    line-height: 1.429
}
.pagination > .btn.previous [class*=fui-], .pagination > .btn.next [class*=fui-] {
    margin-top: -2px;
    margin-left: -2px;
    font-size: 16px
}
.pagination > .btn.next {
    margin-right: 0;
    margin-left: 8px
}
.pagination > .btn.next [class*=fui-] {
    margin-right: -2px;
    margin-left: 4px
}
@media (max-width: 767px) {
    .pagination > .btn {
        display: block;
        width: 50%;
        margin: 0
    }
    .pagination > .btn:first-child {
        border-bottom: 2px solid #dfe2e5;
        border-radius: 6px 0 0
    }
    .pagination > .btn:first-child.btn-primary {
        border-bottom-color: #48c9b0
    }
    .pagination > .btn:first-child.btn-danger {
        border-bottom-color: #ec7063
    }
    .pagination > .btn:first-child.btn-warning {
        border-bottom-color: #f4d03f
    }
    .pagination > .btn:first-child.btn-success {
        border-bottom-color: #58d68d
    }
    .pagination > .btn:first-child.btn-info {
        border-bottom-color: #5dade2
    }
    .pagination > .btn:first-child.btn-inverse {
        border-bottom-color: #5d6d7e
    }
    .pagination > .btn:first-child > [class*=fui] {
        margin-left: -20px
    }
    .pagination > .btn + ul {
        padding: 0;
        text-align: center;
        border-radius: 0 0 6px 6px
    }
    .pagination > .btn + ul + .btn {
        position: absolute;
        top: 0;
        right: 0;
        border-bottom: 2px solid #dfe2e5;
        border-radius: 0 6px 0 0
    }
    .pagination > .btn + ul + .btn.btn-primary {
        border-bottom-color: #48c9b0
    }
    .pagination > .btn + ul + .btn.btn-danger {
        border-bottom-color: #ec7063
    }
    .pagination > .btn + ul + .btn.btn-warning {
        border-bottom-color: #f4d03f
    }
    .pagination > .btn + ul + .btn.btn-success {
        border-bottom-color: #58d68d
    }
    .pagination > .btn + ul + .btn.btn-info {
        border-bottom-color: #5dade2
    }
    .pagination > .btn + ul + .btn.btn-inverse {
        border-bottom-color: #5d6d7e
    }
    .pagination > .btn + ul + .btn > [class*=fui] {
        margin-right: -20px
    }
    .pagination ul {
        display: block
    }
    .pagination ul > li > a {
        border-radius: 0
    }
}
.pagination-danger ul {
    background-color: #e74c3c
}
.pagination-danger ul li.previous > a {
    border-right-color: #ef897e
}
.pagination-danger ul li > a, .pagination-danger ul li > span {
    border-left-color: #ef897e
}
.pagination-danger ul li > a:hover, .pagination-danger ul li > span:hover, .pagination-danger ul li > a:focus, .pagination-danger ul li > span:focus {
    background-color: #ec7063
}
.pagination-danger ul li > a:active, .pagination-danger ul li > span:active {
    background-color: #c44133
}
.pagination-danger ul li.active > a, .pagination-danger ul li.active > span {
    background-color: #c44133
}
.pagination-success ul {
    background-color: #2ecc71
}
.pagination-success ul li.previous > a {
    border-right-color: #75dda1
}
.pagination-success ul li > a, .pagination-success ul li > span {
    border-left-color: #75dda1
}
.pagination-success ul li > a:hover, .pagination-success ul li > span:hover, .pagination-success ul li > a:focus, .pagination-success ul li > span:focus {
    background-color: #58d68d
}
.pagination-success ul li > a:active, .pagination-success ul li > span:active {
    background-color: #27ad60
}
.pagination-success ul li.active > a, .pagination-success ul li.active > span {
    background-color: #27ad60
}
.pagination-warning ul {
    background-color: #f1c40f
}
.pagination-warning ul li.previous > a {
    border-right-color: #f6d861
}
.pagination-warning ul li > a, .pagination-warning ul li > span {
    border-left-color: #f6d861
}
.pagination-warning ul li > a:hover, .pagination-warning ul li > span:hover, .pagination-warning ul li > a:focus, .pagination-warning ul li > span:focus {
    background-color: #f4d313
}
.pagination-warning ul li > a:active, .pagination-warning ul li > span:active {
    background-color: #cda70d
}
.pagination-warning ul li.active > a, .pagination-warning ul li.active > span {
    background-color: #cda70d
}
.pagination-info ul {
    background-color: #3498db
}
.pagination-info ul li.previous > a {
    border-right-color: #79bbe7
}
.pagination-info ul li > a, .pagination-info ul li > span {
    border-left-color: #79bbe7
}
.pagination-info ul li > a:hover, .pagination-info ul li > span:hover, .pagination-info ul li > a:focus, .pagination-info ul li > span:focus {
    background-color: #5dade2
}
.pagination-info ul li > a:active, .pagination-info ul li > span:active {
    background-color: #2c81ba
}
.pagination-info ul li.active > a, .pagination-info ul li.active > span {
    background-color: #2c81ba
}
.pagination-inverse ul {
    background-color: #34495e
}
.pagination-inverse ul li.previous > a {
    border-right-color: #798795
}
.pagination-inverse ul li > a, .pagination-inverse ul li > span {
    border-left-color: #798795
}
.pagination-inverse ul li > a:hover, .pagination-inverse ul li > span:hover, .pagination-inverse ul li > a:focus, .pagination-inverse ul li > span:focus {
    background-color: #415b76
}
.pagination-inverse ul li > a:active, .pagination-inverse ul li > span:active {
    background-color: #2c3e50
}
.pagination-inverse ul li.active > a, .pagination-inverse ul li.active > span {
    background-color: #2c3e50
}
.pagination-minimal > li:first-child {
    border-radius: 6px 0 0 6px
}
.pagination-minimal > li:first-child.previous + li > a, .pagination-minimal > li:first-child.previous + li > span {
    border-left-width: 5px
}
.pagination-minimal > li:last-child {
    border-radius: 0 6px 6px 0
}
.pagination-minimal > li.previous > a, .pagination-minimal > li.next > a, .pagination-minimal > li.previous > span, .pagination-minimal > li.next > span {
    padding: 12px 17px;
    margin: 0 9px 0 0;
    background: 0 0;
    border: none;
    border-right: 2px solid #e4e7ea;
    border-radius: 6px 0 0 6px
}
.pagination-minimal > li.previous > a, .pagination-minimal > li.next > a, .pagination-minimal > li.previous > span, .pagination-minimal > li.next > span, .pagination-minimal > li.previous > a:hover, .pagination-minimal > li.next > a:hover, .pagination-minimal > li.previous > span:hover, .pagination-minimal > li.next > span:hover, .pagination-minimal > li.previous > a:focus, .pagination-minimal > li.next > a:focus, .pagination-minimal > li.previous > span:focus, .pagination-minimal > li.next > span:focus {
    border-color: #e4e7ea !important
}
@media (max-width: 767px) {
    .pagination-minimal > li.previous > a, .pagination-minimal > li.next > a, .pagination-minimal > li.previous > span, .pagination-minimal > li.next > span {
        margin-right: 0
    }
}
.pagination-minimal > li.next {
    margin-left: 9px
}
.pagination-minimal > li.next > a, .pagination-minimal > li.next > span {
    margin: 0;
    border-right: none;
    border-left: 2px solid #e4e7ea;
    border-radius: 0 6px 6px 0
}
.pagination-minimal > li.active > a, .pagination-minimal > li.active > span {
    margin: 10px 5px 9px;
    color: #d6dbdf;
    background-color: #fff;
    border-color: #fff;
    border-width: 2px !important
}
.pagination-minimal > li.active > a:hover, .pagination-minimal > li.active > span:hover, .pagination-minimal > li.active > a:focus, .pagination-minimal > li.active > span:focus {
    color: #d6dbdf;
    background-color: #fff;
    border-color: #fff
}
.pagination-minimal > li.active.previous, .pagination-minimal > li.active.next {
    border-color: #e4e7ea
}
.pagination-minimal > li.active.previous {
    margin-right: 6px
}
.pagination-minimal > li > a, .pagination-minimal > li > span {
    min-width: 0;
    min-height: 16px;
    padding: 0 4px;
    margin: 7px 2px 6px;
    line-height: 16px;
    color: #fff;
    background: #fff;
    background-clip: padding-box;
    border: 5px solid #d6dbdf;
    border-radius: 50px;
    -webkit-transition: background .2s ease-out, border-color 0s ease-out, color .2s ease-out;
    transition: background .2s ease-out, border-color 0s ease-out, color .2s ease-out
}
.pagination-minimal > li > a:hover, .pagination-minimal > li > span:hover, .pagination-minimal > li > a:focus, .pagination-minimal > li > span:focus {
    color: #fff;
    background-color: #585f7a;
    border-color: #8f9fe2;
    -webkit-transition: background .2s ease-out, border-color .2s ease-out, color .2s ease-out;
    transition: background .2s ease-out, border-color .2s ease-out, color .2s ease-out
}
.pagination-minimal > li > a:active, .pagination-minimal > li > span:active {
    background-color: #16a085;
    border-color: #16a085
}
.pagination-plain {
    height: 57px;
    padding: 0;
    margin: 0 0 20px;
    font-size: 16px;
    font-weight: 700;
    list-style-type: none
}
.pagination-plain > li {
    display: inline
}
.pagination-plain > li.previous {
    padding-right: 23px
}
.pagination-plain > li.next {
    padding-left: 20px
}
.pagination-plain > li.active > a {
    color: #d3d7da
}
.pagination-plain > li > a {
    padding: 0 5px
}
@media (max-width: 480px) {
    .pagination-plain {
        overflow: hidden;
        text-align: center
    }
    .pagination-plain > li.previous {
        display: block;
        width: 50%;
        margin-bottom: 10px;
        text-align: left
    }
    .pagination-plain > li.next {
        float: right;
        width: 50%;
        margin-top: -64px;
        text-align: right
    }
}
@media (min-width: 768px) {
    .pagination-plain {
        height: auto
    }
}
.pagination-dropdown ul {
    left: 50%;
    width: auto;
    min-width: 67px;
    margin-left: -34px
}
.pagination-dropdown ul li {
    display: block;
    margin-right: 0
}
.pagination-dropdown ul li:first-child > a, .pagination-dropdown ul li:first-child > span {
    border-radius: 6px 6px 0 0
}
.pagination-dropdown ul li:last-child > a, .pagination-dropdown ul li:last-child > span {
    border-radius: 0 0 6px 6px !important
}
.pagination-dropdown ul li > a, .pagination-dropdown ul li > span {
    display: block;
    float: none;
    min-height: 0;
    padding: 8px 10px 7px;
    text-align: center;
    border-left: none
}
.pagination-dropdown.dropup {
    position: relative
}
.tooltip {
    z-index: 1070;
    font-size: 14px;
    line-height: 1.286
}
.tooltip.in {
    filter: alpha(opacity=100);
    opacity: 1
}
.tooltip.top {
    padding: 9px 0;
    margin-top: -5px
}
.tooltip.right {
    padding: 0 9px;
    margin-left: 5px
}
.tooltip.bottom {
    padding: 9px 0;
    margin-top: 5px
}
.tooltip.left {
    padding: 0 9px;
    margin-left: -5px
}
.tooltip-inner {
    max-width: 183px;
    padding: 12px 12px;
    line-height: 1.286;
    color: #fff;
    background-color: #34495e;
    border-radius: 6px
}
.tooltip.top .tooltip-arrow {
    margin-left: -9px;
    border-width: 9px 9px 0;
    border-top-color: #34495e
}
.tooltip.right .tooltip-arrow {
    margin-top: -9px;
    border-width: 9px 9px 9px 0;
    border-right-color: #34495e
}
.tooltip.left .tooltip-arrow {
    margin-top: -9px;
    border-width: 9px 0 9px 9px;
    border-left-color: #34495e
}
.tooltip.bottom .tooltip-arrow {
    margin-left: -9px;
    border-width: 0 9px 9px;
    border-bottom-color: #34495e
}
.caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 5px;
    vertical-align: middle;
    border-top: 8px solid;
    border-right: 6px solid transparent;
    border-left: 6px solid transparent;
    -webkit-transition: border-color .25s, color .25s;
    transition: border-color .25s, color .25s
}
.dropdown-menu, .select2-drop {
    z-index: 1000;
    min-width: 220px;
    padding: 0;
    margin-top: 9px;
    font-size: 14px;
    background-color: #f3f4f5;
    border: none;
    border-radius: 4px;
    box-shadow: none
}
.dropdown-menu .divider {
    height: 2px;
    margin: 3px 0;
    overflow: hidden;
    background-color: rgba(202, 206, 209, .5)
}
.dropdown-menu > li > a {
    padding: 8px 16px;
    line-height: 1.429;
    color: #606d7a
}
.dropdown-menu > li:first-child > a:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px
}
.dropdown-menu > li:last-child > a:first-child {
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px
}
.dropdown-menu.typeahead {
    display: none;
    width: auto;
    padding: 5px 0;
    margin-top: 5px;
    background-color: #fff;
    border: 2px solid #585f7a;
    border-radius: 6px
}
.dropdown-menu.typeahead li a {
    padding: 6px 14px
}
.dropdown-menu.typeahead li:first-child a, .dropdown-menu.typeahead li:last-child a {
    padding: 6px 14px;
    border-radius: 0
}
.dropdown-menu > li > a:hover, .dropdown-menu > li > a:focus {
    color: #55606c;
    background-color: rgba(202, 206, 209, .5)
}
.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {
    color: #fff;
    background-color: #585f7a
}
.dropdown-menu > .disabled > a, .dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {
    color: #bdc3c7;
    cursor: not-allowed;
    background-color: transparent
}
.dropdown-menu-right {
    right: 0;
    left: auto
}
.dropdown-menu-left {
    right: auto;
    left: 0
}
.dropdown-header {
    padding: 8px 16px;
    font-size: 13px;
    line-height: 1.538;
    color: rgba(52, 73, 94, .6);
    text-transform: uppercase
}
.dropdown-header:first-child {
    margin-top: 3px
}
.dropdown-backdrop {
    z-index: 990
}
.dropup .caret, .navbar-fixed-bottom .dropdown .caret {
    margin-bottom: .25em;
    border-bottom: 8px solid
}
.dropup .dropdown-menu, .navbar-fixed-bottom .dropdown .dropdown-menu {
    margin-top: 0;
    margin-bottom: 9px
}
.dropdown-menu-inverse {
    background-color: #34495e
}
.dropdown-menu-inverse .divider {
    height: 2px;
    margin: 3px 0;
    overflow: hidden;
    background-color: rgba(43, 60, 78, .5)
}
.dropdown-menu-inverse > li > a {
    color: rgba(255, 255, 255, .85)
}
.dropdown-menu-inverse > li > a:hover, .dropdown-menu-inverse > li > a:focus {
    color: rgba(255, 255, 255, .85);
    background-color: rgba(43, 60, 78, .5)
}
.dropdown-menu-inverse > .active > a, .dropdown-menu-inverse > .active > a:hover, .dropdown-menu-inverse > .active > a:focus {
    color: rgba(255, 255, 255, .85);
    background-color: #585f7a
}
.dropdown-menu-inverse > .disabled > a, .dropdown-menu-inverse > .disabled > a:hover, .dropdown-menu-inverse > .disabled > a:focus {
    color: rgba(255, 255, 255, .5)
}
.dropdown-menu-inverse > .disabled > a:hover, .dropdown-menu-inverse > .disabled > a:focus {
    background-color: transparent
}
.dropdown-menu-inverse .dropdown-header {
    color: rgba(255, 255, 255, .4)
}
@media (min-width: 768px) {
    .navbar-right .dropdown-menu {
        right: 0;
        left: auto
    }
    .navbar-right .dropdown-menu-left {
        right: auto;
        left: 0
    }
}
.select {
    position: relative;
    display: inline-block;
    width: auto;
    min-width: 220px;
    vertical-align: top
}
.form-group .select {
    width: 100%
}
.form-group .select > .select2-choice {
    width: 100%
}
.select.form-control, .select.select2-search input[type=text] {
    height: auto;
    padding: 0;
    border: none
}
.select2-choice {
    position: relative;
    display: inline-block;
    width: 100%;
    padding: 10px 39px 10px 15px;
    font-size: 15px;
    font-weight: 400;
    line-height: 1.4;
    border: none;
    border-radius: 4px;
    -webkit-transition: border .25s linear, color .25s linear, background-color .25s linear;
    transition: border .25s linear, color .25s linear, background-color .25s linear
}
.select2-choice:hover, .select2-choice:focus {
    outline: 0
}
.select2-choice:active {
    outline: 0;
    box-shadow: none
}
.select2-container-disabled .select2-choice {
    filter: alpha(opacity=70);
    opacity: .7
}
.select2-chosen {
    overflow: hidden;
    text-align: left
}
.select2-arrow {
    position: absolute;
    top: 42%;
    right: 16px;
    display: inline-block;
    border-color: #34495e transparent;
    border-style: solid;
    border-width: 8px 6px;
    border-bottom-style: none;
    -webkit-transform: scale(1.001);
    -ms-transform: scale(1.001);
    transform: scale(1.001)
}
.select2-arrow b {
    display: none
}
.btn-lg .select2-arrow {
    border-top-width: 8px;
    border-right-width: 6px;
    border-left-width: 6px
}
.select-default .select2-choice {
    color: #fff;
    background-color: #bdc3c7
}
.select-default .select2-choice:hover, .select-default .select2-choice.hover, .select-default .select2-choice:focus, .select-default .select2-choice:active {
    color: #fff;
    background-color: #cacfd2;
    border-color: #cacfd2
}
.select-default .select2-choice:active {
    background: #a1a6a9;
    border-color: #a1a6a9
}
.select2-container-disabled.select-default .select2-choice, .select2-container-disabled.select-default .select2-choice:hover, .select2-container-disabled.select-default .select2-choice:focus, .select2-container-disabled.select-default .select2-choice:active {
    background-color: #bdc3c7;
    border-color: #bdc3c7
}
.select-default .select2-choice .select2-arrow {
    border-top-color: #fff
}
.select-primary .select2-choice {
    color: #fff;
    background-color: #585f7a
}
.select-primary .select2-choice:hover, .select-primary .select2-choice.hover, .select-primary .select2-choice:focus, .select-primary .select2-choice:active {
    color: #fff;
    background-color: #48c9b0;
    border-color: #48c9b0
}
.select-primary .select2-choice:active {
    background: #16a085;
    border-color: #16a085
}
.select2-container-disabled.select-primary .select2-choice, .select2-container-disabled.select-primary .select2-choice:hover, .select2-container-disabled.select-primary .select2-choice:focus, .select2-container-disabled.select-primary .select2-choice:active {
    background-color: #bdc3c7;
    border-color: #8f9fe2
}
.select-primary .select2-choice .select2-arrow {
    border-top-color: #fff
}
.select-info .select2-choice {
    color: #fff;
    background-color: #3498db
}
.select-info .select2-choice:hover, .select-info .select2-choice.hover, .select-info .select2-choice:focus, .select-info .select2-choice:active {
    color: #fff;
    background-color: #5dade2;
    border-color: #5dade2
}
.select-info .select2-choice:active {
    background: #2c81ba;
    border-color: #2c81ba
}
.select2-container-disabled.select-info .select2-choice, .select2-container-disabled.select-info .select2-choice:hover, .select2-container-disabled.select-info .select2-choice:focus, .select2-container-disabled.select-info .select2-choice:active {
    background-color: #bdc3c7;
    border-color: #3498db
}
.select-info .select2-choice .select2-arrow {
    border-top-color: #fff
}
.select-danger .select2-choice {
    color: #fff;
    background-color: #e74c3c
}
.select-danger .select2-choice:hover, .select-danger .select2-choice.hover, .select-danger .select2-choice:focus, .select-danger .select2-choice:active {
    color: #fff;
    background-color: #ec7063;
    border-color: #ec7063
}
.select-danger .select2-choice:active {
    background: #c44133;
    border-color: #c44133
}
.select2-container-disabled.select-danger .select2-choice, .select2-container-disabled.select-danger .select2-choice:hover, .select2-container-disabled.select-danger .select2-choice:focus, .select2-container-disabled.select-danger .select2-choice:active {
    background-color: #bdc3c7;
    border-color: #e74c3c
}
.select-danger .select2-choice .select2-arrow {
    border-top-color: #fff
}
.select-success .select2-choice {
    color: #fff;
    background-color: #2ecc71
}
.select-success .select2-choice:hover, .select-success .select2-choice.hover, .select-success .select2-choice:focus, .select-success .select2-choice:active {
    color: #fff;
    background-color: #58d68d;
    border-color: #58d68d
}
.select-success .select2-choice:active {
    background: #27ad60;
    border-color: #27ad60
}
.select2-container-disabled.select-success .select2-choice, .select2-container-disabled.select-success .select2-choice:hover, .select2-container-disabled.select-success .select2-choice:focus, .select2-container-disabled.select-success .select2-choice:active {
    background-color: #bdc3c7;
    border-color: #2ecc71
}
.select-success .select2-choice .select2-arrow {
    border-top-color: #fff
}
.select-warning .select2-choice {
    color: #fff;
    background-color: #f1c40f
}
.select-warning .select2-choice:hover, .select-warning .select2-choice.hover, .select-warning .select2-choice:focus, .select-warning .select2-choice:active {
    color: #fff;
    background-color: #f4d313;
    border-color: #f4d313
}
.select-warning .select2-choice:active {
    background: #cda70d;
    border-color: #cda70d
}
.select2-container-disabled.select-warning .select2-choice, .select2-container-disabled.select-warning .select2-choice:hover, .select2-container-disabled.select-warning .select2-choice:focus, .select2-container-disabled.select-warning .select2-choice:active {
    background-color: #bdc3c7;
    border-color: #f1c40f
}
.select-warning .select2-choice .select2-arrow {
    border-top-color: #fff
}
.select-inverse .select2-choice {
    color: #fff;
    background-color: #34495e
}
.select-inverse .select2-choice:hover, .select-inverse .select2-choice.hover, .select-inverse .select2-choice:focus, .select-inverse .select2-choice:active {
    color: #fff;
    background-color: #415b76;
    border-color: #415b76
}
.select-inverse .select2-choice:active {
    background: #2c3e50;
    border-color: #2c3e50
}
.select2-container-disabled.select-inverse .select2-choice, .select2-container-disabled.select-inverse .select2-choice:hover, .select2-container-disabled.select-inverse .select2-choice:focus, .select2-container-disabled.select-inverse .select2-choice:active {
    background-color: #bdc3c7;
    border-color: #34495e
}
.select-inverse .select2-choice .select2-arrow {
    border-top-color: #fff
}
.select2-container.select-hg > .select2-choice {
    min-height: 53px;
    padding: 13px 20px;
    padding-right: 49px;
    font-size: 22px;
    line-height: 1.227;
    border-radius: 6px
}
.select2-container.select-hg > .select2-choice .filter-option {
    top: 13px;
    right: 40px;
    left: 20px
}
.select2-container.select-hg > .select2-choice .select2-arrow {
    right: 20px
}
.select2-container.select-hg > .select2-choice > [class^=fui-] {
    top: 2px
}
.select2-container.select-lg > .select2-choice {
    min-height: 45px;
    padding: 10px 19px;
    padding-right: 47px;
    font-size: 17px;
    line-height: 1.471;
    border-radius: 6px
}
.select2-container.select-lg > .select2-choice .filter-option {
    right: 38px;
    left: 18px
}
.select2-container.select-sm > .select2-choice {
    min-height: 36px;
    padding: 9px 13px;
    padding-right: 35px;
    font-size: 13px;
    line-height: 1.385;
    border-radius: 4px
}
.select2-container.select-sm > .select2-choice .filter-option {
    right: 33px;
    left: 13px
}
.select2-container.select-sm > .select2-choice .select2-arrow {
    right: 13px
}
.multiselect {
    position: relative;
    display: inline-block;
    width: auto;
    min-width: 220px;
    max-width: none;
    font-size: 0;
    text-align: left;
    vertical-align: top;
    background-color: #fff;
    border-radius: 6px
}
.form-group .multiselect {
    width: 100%
}
.form-group .multiselect > .select2-choice {
    width: 100%
}
.multiselect.form-control, .multiselect.select2-search input[type=text] {
    height: auto;
    padding: 6px 1px 1px 6px;
    border: 2px solid #ebedef
}
.select2-choices {
    position: relative;
    min-height: 26px;
    padding: 0;
    margin: 0;
    overflow: hidden;
    cursor: text
}
.select2-choices li {
    float: left;
    list-style: none
}
.select2-search-choice {
    position: relative;
    display: inline-block;
    height: 27px;
    padding: 6px 21px;
    margin: 0 5px 4px 0;
    overflow: hidden;
    font-size: 13px;
    line-height: 15px;
    color: #fff;
    vertical-align: middle;
    cursor: pointer;
    border-radius: 4px;
    -webkit-transition: .25s linear;
    transition: .25s linear
}
.select2-search-choice:hover {
    padding-right: 28px;
    padding-left: 14px;
    color: #fff
}
.select2-search-choice:hover .select2-search-choice-close {
    color: inherit;
    filter: none;
    opacity: 1;
    -webkit-filter: none
}
.select2-search-choice .select2-search-choice-close {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    width: 100%;
    padding-right: 10px;
    font-size: 12px;
    color: #fff;
    text-align: right;
    text-decoration: none;
    cursor: pointer;
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transition: opacity .25s linear;
    transition: opacity .25s linear
}
.select2-search-choice .select2-search-choice-close:after {
    font-family: Flat-UI-Icons;
    line-height: 27px;
    content: "\e609";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.select2-search-field input[type=text] {
    width: auto;
    min-width: 80px;
    max-width: inherit;
    height: 29px;
    padding: 0;
    margin: 0;
    font-size: 14px;
    color: #34495e;
    vertical-align: top;
    background-color: transparent;
    border: none;
    outline: 0;
    box-shadow: none
}
.select2-search-field:first-child input[type=text] {
    height: 23px;
    margin: 3px 0 5px
}
.select2-container-multi.multiselect-default {
    border-color: #bdc3c7
}
.select2-container-multi.multiselect-default .select2-search-choice {
    background-color: #bdc3c7
}
.select2-container-multi.multiselect-default .select2-search-choice:hover {
    background-color: #cacfd2
}
.select2-container-multi.multiselect-primary {
    border-color: #8f9fe2
}
.select2-container-multi.multiselect-primary .select2-search-choice {
    background-color: #585f7a
}
.select2-container-multi.multiselect-primary .select2-search-choice:hover {
    background-color: #48c9b0
}
.select2-container-multi.multiselect-info {
    border-color: #3498db
}
.select2-container-multi.multiselect-info .select2-search-choice {
    background-color: #3498db
}
.select2-container-multi.multiselect-info .select2-search-choice:hover {
    background-color: #5dade2
}
.select2-container-multi.multiselect-danger {
    border-color: #e74c3c
}
.select2-container-multi.multiselect-danger .select2-search-choice {
    background-color: #e74c3c
}
.select2-container-multi.multiselect-danger .select2-search-choice:hover {
    background-color: #ec7063
}
.select2-container-multi.multiselect-success {
    border-color: #2ecc71
}
.select2-container-multi.multiselect-success .select2-search-choice {
    background-color: #2ecc71
}
.select2-container-multi.multiselect-success .select2-search-choice:hover {
    background-color: #58d68d
}
.select2-container-multi.multiselect-warning {
    border-color: #f1c40f
}
.select2-container-multi.multiselect-warning .select2-search-choice {
    background-color: #f1c40f
}
.select2-container-multi.multiselect-warning .select2-search-choice:hover {
    background-color: #f4d313
}
.select2-container-multi.multiselect-inverse {
    border-color: #34495e
}
.select2-container-multi.multiselect-inverse .select2-search-choice {
    background-color: #34495e
}
.select2-container-multi.multiselect-inverse .select2-search-choice:hover {
    background-color: #415b76
}
.select2-drop {
    position: absolute;
    top: 100%;
    z-index: 9999;
    min-width: 220px;
    margin-top: 9px;
    font-size: 14px;
    visibility: visible;
    filter: none;
    border-radius: 4px;
    opacity: 1;
    -webkit-transition: none;
    transition: none;
    -webkit-filter: none
}
.select2-drop.select2-drop-above {
    margin-top: -9px
}
.select2-drop.select2-drop-auto-width {
    width: auto
}
.select2-drop.show-select-search .select2-search {
    display: block
}
.select2-drop.show-select-search .select2-search + .select2-results > li:first-child .select2-result-label {
    border-radius: 0
}
.select2-drop .select2-results {
    padding: 0;
    margin: 0;
    list-style: none
}
.select2-drop .select2-results > li:first-child > .select2-result-label {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px
}
.select2-drop .select2-results > li:last-child > .select2-result-label {
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px
}
.select2-drop .select2-result-sub {
    padding: 0;
    margin: 0;
    list-style: none
}
.select2-drop .select2-result-sub > li:last-child > .select2-result-label {
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px
}
.select2-drop .select2-no-results {
    padding: 8px 15px
}
.select2-drop .select2-result-label {
    padding: 8px 16px;
    line-height: 1.429;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-transition: background-color .25s, color .25s;
    transition: background-color .25s, color .25s
}
.select2-drop .select2-result-selectable .select2-result-label {
    color: rgba(52, 73, 94, .85);
    cursor: pointer
}
.select2-drop .select2-result-selectable .select2-result-label:focus, .select2-drop .select2-result-selectable .select2-result-label:hover, .select2-drop .select2-result-selectable .select2-result-label:active {
    color: inherit;
    background-color: #e1e4e7;
    outline: 0
}
.select2-drop .select2-disabled {
    color: rgba(52, 73, 94, .95);
    cursor: default;
    filter: alpha(opacity=40);
    opacity: .4
}
.select2-drop .select2-disabled:focus, .select2-drop .select2-disabled:hover, .select2-drop .select2-disabled:active {
    background: none !important
}
.select2-drop .select2-highlighted > .select2-result-label {
    color: #fff;
    background: #585f7a
}
.select2-drop .select2-result-with-children > .select2-result-label {
    margin-top: 5px;
    font-size: 13px;
    color: rgba(52, 73, 94, .6);
    text-transform: uppercase
}
.select2-drop .select2-result-with-children + .select2-result-with-children > .select2-result-label {
    margin-top: 11px
}
.select2-results {
    position: relative;
    max-height: 200px;
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-tap-highlight-color: transparent
}
.select2-search {
    display: none;
    width: 100%;
    padding: 8px 6px
}
.select2-search input[type=text] {
    width: 100%;
    height: auto !important
}
.select-inverse-dropdown {
    color: rgba(255, 255, 255, .75);
    background-color: #34495e
}
.select-inverse-dropdown .select2-results .select2-result-label {
    color: #fff
}
.select-inverse-dropdown .select2-results .select2-result-label:focus, .select-inverse-dropdown .select2-results .select2-result-label:hover, .select-inverse-dropdown .select2-results .select2-result-label:active {
    background: #2c3e50
}
.select-inverse-dropdown .select2-results.select2-disabled .select2-result-label:hover {
    color: #fff
}
.select-inverse-dropdown .select2-result-with-children > .select2-result-label {
    color: rgba(255, 255, 255, .6)
}
.select-inverse-dropdown .select2-result-with-children > .select2-result-label:hover {
    color: #fff;
    background: none !important
}
.select2-drop-multi {
    border-radius: 6px
}
.select2-drop-multi .select2-results {
    padding: 2px 0
}
.select2-drop-multi .select2-result {
    padding: 2px 4px
}
.select2-drop-multi .select2-result-label {
    border-radius: 4px
}
.select2-drop-multi .select2-selected {
    display: none
}
.select2-offscreen, .select2-offscreen:focus {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden !important;
    clip: rect(0 0 0 0) !important;
    border: 0 !important;
    outline: 0 !important
}
.select2-hidden-accessible {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0 0 0 0);
    border: 0
}
.select2-offscreen, .select2-offscreen:focus {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden !important;
    clip: rect(0 0 0 0) !important;
    border: 0 !important;
    outline: 0 !important
}
.select2-display-none {
    display: none
}
.select2-measure-scrollbar {
    position: absolute;
    top: -10000px;
    left: -10000px;
    width: 100px;
    height: 100px;
    overflow: scroll
}
.select2-drop-mask {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9998;
    width: auto;
    min-width: 100%;
    height: auto;
    min-height: 100%;
    padding: 0;
    margin: 0;
    background-color: #fff;
    filter: alpha(opacity=0);
    border: 0;
    opacity: 0
}
.tile {
    position: relative;
    padding: 14px;
    margin-bottom: 20px;
    text-align: center;
    background-color: #eff0f2;
    border-radius: 6px
}
.tile .tile-hot-ribbon {
    position: absolute;
    top: -4px;
    right: -4px;
    display: block;
    width: 82px
}
.tile p {
    margin-bottom: 33px;
    font-size: 15px
}
.tile-image {
    height: 100px;
    margin: 31px 0 27px;
    vertical-align: bottom
}
.tile-image.big-illustration {
    width: 112px;
    height: 111px;
    margin-top: 20px
}
.tile-title {
    margin: 0;
    font-size: 20px
}
.navbar {
    min-height: 53px;
    margin-bottom: 30px;
    font-size: 16px;
    border: none;
    border-radius: 6px
}
@media (min-width: 768px) {
    .navbar-header {
        float: left
    }
}
.navbar-collapse {
    padding-right: 21px;
    padding-left: 21px;
    box-shadow: none
}
.navbar-collapse .navbar-form:first-child {
    border: none
}
@media (min-width: 768px) {
    .navbar-collapse .navbar-nav.navbar-left:first-child {
        margin-left: -21px
    }
    .navbar-collapse .navbar-nav.navbar-left:first-child > li:first-child a {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px
    }
    .navbar-collapse .navbar-nav.navbar-right:last-child {
        margin-right: -21px
    }
    .navbar-collapse .navbar-nav.navbar-right:last-child > .dropdown:last-child > a {
        border-radius: 0 6px 6px 0
    }
    .navbar-fixed-top .navbar-collapse .navbar-form.navbar-right:last-child, .navbar-fixed-bottom .navbar-collapse .navbar-form.navbar-right:last-child {
        margin-right: 0
    }
}
@media (max-width: 767px) {
    .navbar-collapse .navbar-nav.navbar-right:last-child {
        margin-bottom: 3px
    }
}
.navbar .container, .navbar .container-fluid {
    padding-right: 21px;
    padding-left: 21px
}
.navbar .container > .navbar-header, .navbar .container-fluid > .navbar-header, .navbar .container > .navbar-collapse, .navbar .container-fluid > .navbar-collapse {
    margin-right: -21px;
    margin-left: -21px
}
@media (min-width: 768px) {
    .navbar .container > .navbar-header, .navbar .container-fluid > .navbar-header, .navbar .container > .navbar-collapse, .navbar .container-fluid > .navbar-collapse {
        margin-right: 0;
        margin-left: 0
    }
}
.navbar-static-top {
    z-index: 1000;
    border-width: 0;
    border-radius: 0
}
.navbar-fixed-top, .navbar-fixed-bottom {
    z-index: 1030;
    border-radius: 0
}
.navbar-fixed-top {
    border-width: 0
}
.navbar-fixed-bottom {
    margin-bottom: 0;
    border-width: 0
}
.navbar-brand {
    height: 53px;
    padding: 14px 21px;
    font-size: 24px;
    font-weight: 700;
    line-height: 1.042
}
.navbar-brand > [class*=fui-] {
    font-size: 19px;
    line-height: 1.263;
    vertical-align: top
}
@media (min-width: 768px) {
    .navbar > .container .navbar-brand, .navbar > .container-fluid .navbar-brand {
        margin-left: -21px
    }
}
.navbar-toggle {
    height: 53px;
    padding: 0 21px;
    margin: 0 0 0 21px;
    line-height: 53px;
    color: #34495e;
    border: none
}
.navbar-toggle:before {
    font-family: Flat-UI-Icons;
    font-size: 22px;
    font-style: normal;
    font-weight: 400;
    color: #16a085;
    content: "\e61a";
    -webkit-transition: color .25s linear;
    transition: color .25s linear;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.navbar-toggle:hover, .navbar-toggle:focus {
    outline: 0
}
.navbar-toggle:hover:before, .navbar-toggle:focus:before {
    color: #8f9fe2
}
.navbar-toggle .icon-bar {
    display: none
}
@media (min-width: 768px) {
    .navbar-toggle {
        display: none
    }
}
.navbar-nav {
    margin: 0
}
.navbar-nav > li > a {
    padding: 15px 21px;
    font-size: 16px;
    font-weight: 700;
    line-height: 23px
}
.navbar-nav > li > a:hover, .navbar-nav > li > a:focus, .navbar-nav .open > a:focus, .navbar-nav .open > a:hover {
    background-color: transparent
}
.navbar-nav [class^=fui-] {
    position: relative;
    top: 1px;
    line-height: 20px
}
.navbar-nav .visible-sm > [class^=fui-], .navbar-nav .visible-xs > [class^=fui-] {
    margin-left: 12px
}
@media (max-width: 767px) {
    .navbar-nav {
        margin: 0 -21px
    }
    .navbar-nav .open .dropdown-menu > li > a, .navbar-nav .open .dropdown-menu .dropdown-header {
        padding: 7px 15px 7px 31px !important
    }
    .navbar-nav .open .dropdown-menu > li > a {
        line-height: 23px
    }
    .navbar-nav > li > a {
        padding-top: 7px;
        padding-bottom: 7px
    }
}
.navbar-input {
    height: 35px;
    padding: 5px 10px;
    font-size: 13px;
    line-height: 1.4;
    border-radius: 6px
}
select.navbar-input {
    height: 35px;
    line-height: 35px
}
textarea.navbar-input, select[multiple].navbar-input {
    height: auto
}
.navbar-form {
    padding-top: 9px;
    padding-right: 19px;
    padding-bottom: 9px;
    padding-left: 19px;
    margin-top: 0;
    margin-bottom: 0;
    box-shadow: none
}
@media (max-width: 767px) {
    .navbar-form {
        width: auto;
        margin: 3px -21px
    }
}
.navbar-form .form-control, .navbar-form .input-group-addon, .navbar-form .btn, .navbar-form .select2-search input[type=text] {
    height: 35px;
    padding: 5px 10px;
    font-size: 13px;
    line-height: 1.4;
    border-radius: 6px
}
select.navbar-form .form-control, select.navbar-form .input-group-addon, select.navbar-form .btn, select.navbar-form .select2-search input[type=text] {
    height: 35px;
    line-height: 35px
}
textarea.navbar-form .form-control, textarea.navbar-form .input-group-addon, textarea.navbar-form .btn, select[multiple].navbar-form .form-control, select[multiple].navbar-form .input-group-addon, select[multiple].navbar-form .btn, textarea.navbar-form .select2-search input[type=text], select[multiple].navbar-form .select2-search input[type=text] {
    height: auto
}
.navbar-form .btn {
    margin: 0
}
.navbar-form .input-group .form-control:first-child, .navbar-form .input-group-addon:first-child, .navbar-form .input-group-btn:first-child > .btn, .navbar-form .input-group-btn:first-child > .dropdown-toggle, .navbar-form .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle), .navbar-form .input-group .select2-search input[type=text]:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}
.navbar-form .input-group .form-control:last-child, .navbar-form .input-group-addon:last-child, .navbar-form .input-group-btn:last-child > .btn, .navbar-form .input-group-btn:last-child > .dropdown-toggle, .navbar-form .input-group-btn:first-child > .btn:not(:first-child), .navbar-form .input-group .select2-search input[type=text]:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}
.navbar-form .form-control, .navbar-form .select2-search input[type=text] {
    display: table-cell;
    font-size: 15px;
    border-radius: 5px
}
.navbar-form .form-group ~ .btn {
    margin-left: 5px;
    font-size: 15px;
    border-radius: 5px
}
.navbar-form .form-group + .btn {
    margin-right: 5px
}
@media (min-width: 768px) {
    .navbar-form .input-group {
        width: 195px
    }
}
@media (max-width: 767px) {
    .navbar-form .form-group {
        margin-bottom: 7px
    }
    .navbar-form .form-group:last-child {
        margin-bottom: 0
    }
    .navbar-form .form-group + .btn {
        margin-left: 0
    }
}
.navbar-nav > li > .dropdown-menu {
    min-width: 100%;
    margin-top: 9px;
    border-radius: 4px
}
@media (max-width: 767px) {
    .navbar-nav > li.open > .dropdown-menu {
        margin-top: 0 !important
    }
}
.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px
}
.navbar-nav > .open > .dropdown-toggle, .navbar-nav > .open > .dropdown-toggle:focus, .navbar-nav > .open > .dropdown-toggle:hover {
    background-color: transparent
}
.navbar-text {
    padding-top: 15px;
    padding-bottom: 15px;
    margin-top: 0;
    margin-bottom: 0;
    font-size: 16px;
    line-height: 1.438;
    color: #34495e
}
@media (min-width: 768px) {
    .navbar-text {
        margin-right: 21px;
        margin-left: 21px
    }
    .navbar-text.navbar-right:last-child {
        margin-right: 0
    }
}
.navbar-btn {
    margin-top: 6px;
    margin-bottom: 6px
}
.navbar-btn.btn-sm {
    margin-top: 9px;
    margin-bottom: 8px
}
.navbar-btn.btn-xs {
    margin-top: 14px;
    margin-bottom: 14px
}
.navbar-unread, .navbar-new {
    position: absolute;
    top: 35%;
    right: 12px;
    z-index: 10;
    width: 6px;
    height: 6px;
    font-family: Lato, Helvetica, Arial, sans-serif;
    font-size: 0;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    background-color: #585f7a;
    border-radius: 50%
}
@media (max-width: 768px) {
    .navbar-unread, .navbar-new {
        position: static;
        float: right;
        margin: 0 0 0 10px
    }
}
.active .navbar-unread, .active .navbar-new {
    display: none;
    background-color: #fff
}
.navbar-new {
    width: auto;
    min-width: 18px;
    height: 18px;
    padding: 0 1px;
    margin: -6px -10px;
    font-size: 12px;
    line-height: 17px;
    background-color: #e74c3c;
    -webkit-font-smoothing: subpixel-antialiased
}
.navbar-default {
    background-color: #ecf0f1
}
.navbar-default .navbar-brand {
    color: #34495e
}
.navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus {
    color: #8f9fe2;
    background-color: transparent
}
.navbar-default .navbar-toggle:before {
    color: #34495e
}
.navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {
    background-color: transparent
}
.navbar-default .navbar-toggle:hover:before, .navbar-default .navbar-toggle:focus:before {
    color: #8f9fe2
}
.navbar-default .navbar-collapse, .navbar-default .navbar-form {
    border-color: #e5e9ea;
    border-width: 2px
}
.navbar-default .navbar-nav > li > a {
    color: #34495e
}
.navbar-default .navbar-nav > li > a:hover, .navbar-default .navbar-nav > li > a:focus {
    color: #8f9fe2;
    background-color: transparent
}
.navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:hover, .navbar-default .navbar-nav > .active > a:focus {
    color: #8f9fe2;
    background-color: transparent
}
.navbar-default .navbar-nav > .disabled > a, .navbar-default .navbar-nav > .disabled > a:hover, .navbar-default .navbar-nav > .disabled > a:focus {
    color: #ccc;
    background-color: transparent
}
.navbar-default .navbar-nav > .dropdown > a .caret {
    border-top-color: #34495e;
    border-bottom-color: #34495e
}
.navbar-default .navbar-nav > .active > a .caret {
    border-top-color: #8f9fe2;
    border-bottom-color: #8f9fe2
}
.navbar-default .navbar-nav > .dropdown > a:hover .caret, .navbar-default .navbar-nav > .dropdown > a:focus .caret {
    border-top-color: #8f9fe2;
    border-bottom-color: #8f9fe2
}
.navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:hover, .navbar-default .navbar-nav > .open > a:focus {
    color: #8f9fe2;
    background-color: transparent
}
.navbar-default .navbar-nav > .open > a .caret, .navbar-default .navbar-nav > .open > a:hover .caret, .navbar-default .navbar-nav > .open > a:focus .caret {
    border-top-color: #8f9fe2;
    border-bottom-color: #8f9fe2
}
@media (max-width: 767px) {
    .navbar-default .navbar-nav .open .dropdown-menu > li > a {
        color: #34495e
    }
    .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #8f9fe2;
        background-color: transparent
    }
    .navbar-default .navbar-nav .open .dropdown-menu > .active > a, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
        color: #8f9fe2;
        background-color: transparent
    }
    .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
        color: #ccc;
        background-color: transparent
    }
}
.navbar-default .navbar-form .form-control, .navbar-default .navbar-form .select2-search input[type=text] {
    border-color: transparent
}
.navbar-default .navbar-form .form-control::-moz-placeholder, .navbar-default .navbar-form .select2-search input[type=text]::-moz-placeholder {
    color: #aeb6bf;
    opacity: 1
}
.navbar-default .navbar-form .form-control:-ms-input-placeholder, .navbar-default .navbar-form .select2-search input[type=text]:-ms-input-placeholder {
    color: #aeb6bf
}
.navbar-default .navbar-form .form-control::-webkit-input-placeholder, .navbar-default .navbar-form .select2-search input[type=text]::-webkit-input-placeholder {
    color: #aeb6bf
}
.navbar-default .navbar-form .form-control:focus, .navbar-default .navbar-form .select2-search input[type=text]:focus {
    color: #8f9fe2;
    border-color: #8f9fe2
}
.navbar-default .navbar-form .input-group-btn .btn {
    color: #919ba4;
    border-color: transparent
}
.navbar-default .navbar-form .input-group.focus .form-control, .navbar-default .navbar-form .input-group.focus .input-group-btn .btn, .navbar-default .navbar-form .input-group.focus .select2-search input[type=text] {
    color: #8f9fe2;
    border-color: #8f9fe2
}
.navbar-default .navbar-text {
    color: #34495e
}
.navbar-default .navbar-link {
    color: #34495e
}
.navbar-default .navbar-link:hover {
    color: #8f9fe2
}
.navbar-default .btn-link {
    color: #34495e
}
.navbar-default .btn-link:hover, .navbar-default .btn-link:focus {
    color: #8f9fe2
}
.navbar-default .btn-link[disabled]:hover, fieldset[disabled] .navbar-default .btn-link:hover, .navbar-default .btn-link[disabled]:focus, fieldset[disabled] .navbar-default .btn-link:focus {
    color: #ccc
}
.navbar-inverse {
    background-color: #34495e
}
.navbar-inverse .navbar-brand {
    color: #fff
}
.navbar-inverse .navbar-brand:hover, .navbar-inverse .navbar-brand:focus {
    color: #8f9fe2;
    background-color: transparent
}
.navbar-inverse .navbar-toggle:before {
    color: #fff
}
.navbar-inverse .navbar-toggle:hover, .navbar-inverse .navbar-toggle:focus {
    background-color: transparent
}
.navbar-inverse .navbar-toggle:hover:before, .navbar-inverse .navbar-toggle:focus:before {
    color: #8f9fe2
}
.navbar-inverse .navbar-collapse {
    border-color: #2f4154;
    border-width: 2px
}
.navbar-inverse .navbar-nav > li > a {
    color: #fff
}
.navbar-inverse .navbar-nav > li > a:hover, .navbar-inverse .navbar-nav > li > a:focus {
    color: #8f9fe2;
    background-color: transparent
}
.navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:hover, .navbar-inverse .navbar-nav > .active > a:focus {
    color: #fff;
    background-color: #585f7a；
}
.navbar-inverse .navbar-nav > .disabled > a, .navbar-inverse .navbar-nav > .disabled > a:hover, .navbar-inverse .navbar-nav > .disabled > a:focus {
    color: #444;
    background-color: transparent
}
.navbar-inverse .navbar-nav > .dropdown > a:hover .caret, .navbar-inverse .navbar-nav > .dropdown > a:focus .caret {
    border-top-color: #8f9fe2;
    border-bottom-color: #8f9fe2
}
.navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:hover, .navbar-inverse .navbar-nav > .open > a:focus {
    color: #fff;
    background-color: #585f7a;
    border-left-color: transparent
}
.navbar-inverse .navbar-nav > .open > a .caret, .navbar-inverse .navbar-nav > .open > a:hover .caret, .navbar-inverse .navbar-nav > .open > a:focus .caret {
    border-top-color: #fff;
    border-bottom-color: #fff
}
.navbar-inverse .navbar-nav > .dropdown > a .caret {
    border-top-color: #4b6075;
    border-bottom-color: #4b6075
}
.navbar-inverse .navbar-nav > .open > .dropdown-menu {
    padding: 3px 4px;
    background-color: #34495e
}
.navbar-inverse .navbar-nav > .open > .dropdown-menu > li > a {
    padding: 6px 9px;
    color: #e1e4e7;
    border-radius: 4px
}
.navbar-inverse .navbar-nav > .open > .dropdown-menu > li > a:hover, .navbar-inverse .navbar-nav > .open > .dropdown-menu > li > a:focus {
    color: #fff;
    background-color: #585f7a
}
.navbar-inverse .navbar-nav > .open > .dropdown-menu > .divider {
    height: 2px;
    margin-right: -4px;
    margin-left: -4px;
    background-color: #2f4154
}
@media (max-width: 767px) {
    .navbar-inverse .navbar-nav > li > a {
        border-left-width: 0
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
        color: #fff
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #8f9fe2;
        background-color: transparent
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
        color: #fff;
        background-color: #585f7a
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
        color: #444;
        background-color: transparent
    }
    .navbar-inverse .navbar-nav .dropdown-menu .divider {
        background-color: #2f4154
    }
}
.navbar-inverse .navbar-form .form-control, .navbar-inverse .navbar-form .select2-search input[type=text] {
    color: #536a81;
    background-color: #293a4a;
    border-color: transparent
}
.navbar-inverse .navbar-form .form-control::-moz-placeholder, .navbar-inverse .navbar-form .select2-search input[type=text]::-moz-placeholder {
    color: #536a81;
    opacity: 1
}
.navbar-inverse .navbar-form .form-control:-ms-input-placeholder, .navbar-inverse .navbar-form .select2-search input[type=text]:-ms-input-placeholder {
    color: #536a81
}
.navbar-inverse .navbar-form .form-control::-webkit-input-placeholder, .navbar-inverse .navbar-form .select2-search input[type=text]::-webkit-input-placeholder {
    color: #536a81
}
.navbar-inverse .navbar-form .form-control:focus, .navbar-inverse .navbar-form .select2-search input[type=text]:focus {
    color: #8f9fe2;
    border-color: #8f9fe2
}
.navbar-inverse .navbar-form .btn {
    color: #fff;
    background-color: #585f7a
}
.navbar-inverse .navbar-form .btn:hover, .navbar-inverse .navbar-form .btn.hover, .navbar-inverse .navbar-form .btn:focus, .navbar-inverse .navbar-form .btn:active, .navbar-inverse .navbar-form .btn.active, .open > .dropdown-toggle.navbar-inverse .navbar-form .btn {
    color: #fff;
    background-color: #48c9b0;
    border-color: #48c9b0
}
.navbar-inverse .navbar-form .btn:active, .navbar-inverse .navbar-form .btn.active, .open > .dropdown-toggle.navbar-inverse .navbar-form .btn {
    background: #16a085;
    border-color: #16a085
}
.navbar-inverse .navbar-form .btn.disabled, .navbar-inverse .navbar-form .btn[disabled], fieldset[disabled] .navbar-inverse .navbar-form .btn, .navbar-inverse .navbar-form .btn.disabled:hover, .navbar-inverse .navbar-form .btn[disabled]:hover, fieldset[disabled] .navbar-inverse .navbar-form .btn:hover, .navbar-inverse .navbar-form .btn.disabled.hover, .navbar-inverse .navbar-form .btn[disabled].hover, fieldset[disabled] .navbar-inverse .navbar-form .btn.hover, .navbar-inverse .navbar-form .btn.disabled:focus, .navbar-inverse .navbar-form .btn[disabled]:focus, fieldset[disabled] .navbar-inverse .navbar-form .btn:focus, .navbar-inverse .navbar-form .btn.disabled:active, .navbar-inverse .navbar-form .btn[disabled]:active, fieldset[disabled] .navbar-inverse .navbar-form .btn:active, .navbar-inverse .navbar-form .btn.disabled.active, .navbar-inverse .navbar-form .btn[disabled].active, fieldset[disabled] .navbar-inverse .navbar-form .btn.active {
    background-color: #bdc3c7;
    border-color: #8f9fe2
}
.navbar-inverse .navbar-form .btn .badge {
    color: #8f9fe2;
    background-color: #fff
}
.navbar-inverse .navbar-form .input-group-btn .btn {
    color: #526a82;
    background-color: #293a4a;
    border-color: transparent
}
.navbar-inverse .navbar-form .input-group.focus .form-control, .navbar-inverse .navbar-form .input-group.focus .input-group-btn .btn, .navbar-inverse .navbar-form .input-group.focus .select2-search input[type=text] {
    color: #8f9fe2;
    border-color: #8f9fe2
}
@media (max-width: 767px) {
    .navbar-inverse .navbar-form {
        border-color: #2f4154;
        border-width: 2px 0
    }
}
.navbar-inverse .navbar-text {
    color: #fff
}
.navbar-inverse .navbar-text a {
    color: #fff
}
.navbar-inverse .navbar-text a:hover, .navbar-inverse .navbar-text a:focus {
    color: #8f9fe2
}
.navbar-inverse .navbar-btn {
    color: #fff;
    background-color: #585f7a
}
.navbar-inverse .navbar-btn:hover, .navbar-inverse .navbar-btn.hover, .navbar-inverse .navbar-btn:focus, .navbar-inverse .navbar-btn:active, .navbar-inverse .navbar-btn.active, .open > .dropdown-toggle.navbar-inverse .navbar-btn {
    color: #fff;
    background-color: #48c9b0;
    border-color: #48c9b0
}
.navbar-inverse .navbar-btn:active, .navbar-inverse .navbar-btn.active, .open > .dropdown-toggle.navbar-inverse .navbar-btn {
    background: #16a085;
    border-color: #16a085
}
.navbar-inverse .navbar-btn.disabled, .navbar-inverse .navbar-btn[disabled], fieldset[disabled] .navbar-inverse .navbar-btn, .navbar-inverse .navbar-btn.disabled:hover, .navbar-inverse .navbar-btn[disabled]:hover, fieldset[disabled] .navbar-inverse .navbar-btn:hover, .navbar-inverse .navbar-btn.disabled.hover, .navbar-inverse .navbar-btn[disabled].hover, fieldset[disabled] .navbar-inverse .navbar-btn.hover, .navbar-inverse .navbar-btn.disabled:focus, .navbar-inverse .navbar-btn[disabled]:focus, fieldset[disabled] .navbar-inverse .navbar-btn:focus, .navbar-inverse .navbar-btn.disabled:active, .navbar-inverse .navbar-btn[disabled]:active, fieldset[disabled] .navbar-inverse .navbar-btn:active, .navbar-inverse .navbar-btn.disabled.active, .navbar-inverse .navbar-btn[disabled].active, fieldset[disabled] .navbar-inverse .navbar-btn.active {
    background-color: #bdc3c7;
    border-color: #8f9fe2
}
.navbar-inverse .navbar-btn .badge {
    color: #8f9fe2;
    background-color: #fff
}
@media (min-width: 768px) {
    .navbar-embossed > .navbar-collapse {
        border-radius: 6px;
        box-shadow: inset 0 -2px 0 rgba(0, 0, 0, .15)
    }
    .navbar-embossed.navbar-inverse .navbar-nav .active > a, .navbar-embossed.navbar-inverse .navbar-nav .open > a {
        box-shadow: inset 0 -2px 0 rgba(0, 0, 0, .15)
    }
}
.navbar-lg {
    min-height: 76px
}
.navbar-lg .navbar-brand {
    height: 76px;
    padding-top: 26px;
    padding-bottom: 26px;
    line-height: 1
}
.navbar-lg .navbar-brand > [class*=fui-] {
    font-size: 24px;
    line-height: 1
}
.navbar-lg .navbar-nav > li > a {
    font-size: 15px;
    line-height: 1.6
}
@media (min-width: 768px) {
    .navbar-lg .navbar-nav > li > a {
        padding-top: 26px;
        padding-bottom: 26px
    }
}
.navbar-lg .navbar-toggle {
    height: 76px;
    line-height: 76px
}
.navbar-lg .navbar-form {
    padding-top: 20.5px;
    padding-bottom: 20.5px
}
.navbar-lg .navbar-text {
    padding-top: 26.5px;
    padding-bottom: 26.5px
}
.navbar-lg .navbar-btn {
    margin-top: 17.5px;
    margin-bottom: 17.5px
}
.navbar-lg .navbar-btn.btn-sm {
    margin-top: 20.5px;
    margin-bottom: 20.5px
}
.navbar-lg .navbar-btn.btn-xs {
    margin-top: 25.5px;
    margin-bottom: 25.5px
}
.bootstrap-switch {
    position: relative;
    display: inline-block;
    width: 80px;
    height: 29px;
    overflow: hidden;
    font-size: 15px;
    line-height: 29px;
    text-align: left;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border-radius: 30px;
    -webkit-mask-box-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNy4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSIwIDAgODAgMjkiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDgwIDI5IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxwYXRoIGQ9Ik04MCwxNC41YzAsOC02LjUsMTQuNS0xNC41LDE0LjVoLTUxQzYuNSwyOSwwLDIyLjUsMCwxNC41bDAsMEMwLDYuNSw2LjUsMCwxNC41LDBoNTFDNzMuNSwwLDgwLDYuNSw4MCwxNC41TDgwLDE0LjV6Ii8+DQo8L3N2Zz4NCg==) 0 0 stretch
}
.bootstrap-switch > div {
    display: inline-block;
    width: 132px;
    border-radius: 30px;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}
.bootstrap-switch > div > span {
    z-index: 1;
    display: inline-block;
    width: 66px;
    height: 100%;
    padding-top: 5px;
    padding-bottom: 5px;
    font-weight: 700;
    line-height: 19px;
    text-align: center;
    cursor: pointer;
    -webkit-transition: box-shadow .25s ease-out;
    transition: box-shadow .25s ease-out
}
.bootstrap-switch > div > span > [class^=fui-] {
    text-indent: 0
}
.bootstrap-switch > div > label {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 200;
    display: block;
    width: 100%;
    height: 100%;
    margin: 0;
    font-size: 0;
    text-indent: -9999px;
    cursor: pointer;
    filter: alpha(opacity=0);
    opacity: 0
}
.bootstrap-switch input[type=radio], .bootstrap-switch input[type=checkbox] {
    position: absolute !important;
    top: 0;
    left: 0;
    z-index: -1;
    margin: 0;
    filter: alpha(opacity=0);
    opacity: 0
}
.bootstrap-switch-handle-on {
    border-top-left-radius: 30px;
    border-bottom-left-radius: 30px
}
.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-default {
    box-shadow: inset 0 0 transparent, -16px 0 0 #bdc3c7
}
.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-default:before {
    background-color: #7f8c9a;
    border-color: #bdc3c7
}
.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-primary {
    box-shadow: inset 0 0 transparent, -16px 0 0 #34495e
}
.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-primary:before {
    background-color: #585f7a;
    border-color: #34495e
}
.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-success {
    box-shadow: inset 0 0 transparent, -16px 0 0 #2ecc71
}
.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-success:before {
    background-color: #fff;
    border-color: #2ecc71
}
.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-warning {
    box-shadow: inset 0 0 transparent, -16px 0 0 #f1c40f
}
.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-warning:before {
    background-color: #fff;
    border-color: #f1c40f
}
.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-info {
    box-shadow: inset 0 0 transparent, -16px 0 0 #3498db
}
.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-info:before {
    background-color: #fff;
    border-color: #3498db
}
.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-danger {
    box-shadow: inset 0 0 transparent, -16px 0 0 #e74c3c
}
.bootstrap-switch-off .bootstrap-switch-handle-on ~ .bootstrap-switch-handle-off.bootstrap-switch-danger:before {
    background-color: #fff;
    border-color: #e74c3c
}
.bootstrap-switch-handle-off {
    border-top-right-radius: 30px;
    border-bottom-right-radius: 30px
}
.bootstrap-switch-handle-off:before {
    position: absolute;
    top: 0;
    left: 51px;
    z-index: 100;
    display: inline-block;
    width: 29px;
    height: 29px;
    padding: 0;
    text-align: center;
    vertical-align: top;
    content: " ";
    background-clip: padding-box;
    border: 4px solid transparent;
    border-radius: 50%;
    -webkit-transition: border-color .25s ease-out, background-color .25s ease-out;
    transition: border-color .25s ease-out, background-color .25s ease-out
}
.bootstrap-switch-animate > div {
    -webkit-transition: margin-left .25s ease-out;
    transition: margin-left .25s ease-out
}
.bootstrap-switch-on > div {
    margin-left: 0
}
.bootstrap-switch-off > div {
    margin-left: -51px
}
.bootstrap-switch-disabled, .bootstrap-switch-readonly {
    cursor: default;
    filter: alpha(opacity=50);
    opacity: .5
}
.bootstrap-switch-disabled > div > span, .bootstrap-switch-readonly > div > span, .bootstrap-switch-disabled > div > label, .bootstrap-switch-readonly > div > label {
    cursor: default !important
}
.bootstrap-switch-focused {
    outline: 0
}
.bootstrap-switch-default {
    color: #fff;
    background-color: #bdc3c7
}
.bootstrap-switch-default ~ .bootstrap-switch-handle-off:before {
    background-color: #7f8c9a;
    border-color: #bdc3c7
}
.bootstrap-switch-on .bootstrap-switch-default ~ .bootstrap-switch-handle-off {
    box-shadow: inset 16px 0 0 #bdc3c7
}
.bootstrap-switch-primary {
    color: #8f9fe2;
    background-color: #34495e
}
.bootstrap-switch-primary ~ .bootstrap-switch-handle-off:before {
    background-color: #585f7a;
    border-color: #34495e
}
.bootstrap-switch-on .bootstrap-switch-primary ~ .bootstrap-switch-handle-off {
    box-shadow: inset 16px 0 0 #34495e
}
.bootstrap-switch-info {
    color: #fff;
    background-color: #3498db
}
.bootstrap-switch-info ~ .bootstrap-switch-handle-off:before {
    background-color: #fff;
    border-color: #3498db
}
.bootstrap-switch-on .bootstrap-switch-info ~ .bootstrap-switch-handle-off {
    box-shadow: inset 16px 0 0 #3498db
}
.bootstrap-switch-success {
    color: #fff;
    background-color: #2ecc71
}
.bootstrap-switch-success ~ .bootstrap-switch-handle-off:before {
    background-color: #fff;
    border-color: #2ecc71
}
.bootstrap-switch-on .bootstrap-switch-success ~ .bootstrap-switch-handle-off {
    box-shadow: inset 16px 0 0 #2ecc71
}
.bootstrap-switch-warning {
    color: #fff;
    background-color: #f1c40f
}
.bootstrap-switch-warning ~ .bootstrap-switch-handle-off:before {
    background-color: #fff;
    border-color: #f1c40f
}
.bootstrap-switch-on .bootstrap-switch-warning ~ .bootstrap-switch-handle-off {
    box-shadow: inset 16px 0 0 #f1c40f
}
.bootstrap-switch-danger {
    color: #fff;
    background-color: #e74c3c
}
.bootstrap-switch-danger ~ .bootstrap-switch-handle-off:before {
    background-color: #fff;
    border-color: #e74c3c
}
.bootstrap-switch-on .bootstrap-switch-danger ~ .bootstrap-switch-handle-off {
    box-shadow: inset 16px 0 0 #e74c3c
}
.bootstrap-switch-square .bootstrap-switch {
    border-radius: 4px;
    -webkit-mask-box-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNy4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSIwIDAgODAgMjkiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDgwIDI5IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxwYXRoIGQ9Ik04MCwyNWMwLDIuMi0xLjgsNC00LDRINGMtMi4yLDAtNC0xLjgtNC00VjRjMC0yLjIsMS44LTQsNC00aDcyYzIuMiwwLDQsMS44LDQsNFYyNXoiLz4NCjwvc3ZnPg0K) 0 0 stretch
}
.bootstrap-switch-square .bootstrap-switch > div {
    border-radius: 4px
}
.bootstrap-switch-square .bootstrap-switch .bootstrap-switch-handle-on {
    text-indent: -15px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px
}
.bootstrap-switch-square .bootstrap-switch .bootstrap-switch-handle-off {
    text-indent: 15px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px
}
.bootstrap-switch-square .bootstrap-switch .bootstrap-switch-handle-off:before {
    border: none;
    border-top-left-radius: 0;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 0
}
.bootstrap-switch-square .bootstrap-switch-off .bootstrap-switch-handle-off:before {
    border-top-left-radius: 2px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 2px
}
.share {
    position: relative;
    background-color: #eff0f2;
    border-radius: 6px
}
.share ul {
    padding: 15px;
    margin: 0;
    list-style-type: none
}
.share li {
    padding-top: 11px;
    font-size: 15px;
    line-height: 1.4
}
.share li:before, .share li:after {
    display: table;
    content: " "
}
.share li:after {
    clear: both
}
.share li:first-child {
    padding-top: 0
}
.share .toggle {
    float: right;
    margin: 0
}
.share .btn {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}
.share-label {
    float: left;
    width: 50%;
    padding-top: 5px;
    font-size: 15px;
    line-height: 1.4
}
.video-js {
    position: relative;
    width: 100% !important;
    height: auto !important;
    padding-bottom: 47px;
    overflow: hidden;
    font-size: 0;
    vertical-align: middle;
    background-color: transparent;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-top-radius: 6px
}
.video-js .vjs-tech {
    display: block;
    width: 100%;
    height: 100%
}
.video-js::-moz-full-screen {
    position: absolute
}
.video-js::-webkit-full-screen {
    width: 100% !important;
    height: 100% !important
}
.vjs-fullscreen {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10000;
    width: 100% !important;
    height: 100% !important;
    overflow: hidden;
    border-top-radius: 0
}
.vjs-fullscreen .vjs-control-bar {
    margin-top: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}
.vjs-fullscreen .vjs-tech {
    background-color: #000
}
.vjs-poster {
    position: relative;
    width: 100%;
    max-height: 100%;
    padding: 0;
    margin: 0 auto;
    cursor: pointer;
    border-top-radius: 6px
}
.vjs-control-bar {
    position: relative;
    height: 47px;
    margin-top: -1px;
    color: #fff;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background: #2c3e50;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px
}
.vjs-control-bar.vjs-fade-out {
    visibility: visible !important;
    opacity: 1 !important
}
.vjs-text-track-display {
    position: absolute;
    right: 1em;
    bottom: 4em;
    left: 1em;
    font-family: Lato, Helvetica, Arial, sans-serif;
    text-align: center
}
.vjs-text-track {
    display: none;
    margin-bottom: .1em;
    font-size: 1.4em;
    color: #fff;
    text-align: center;
    background-color: rgba(0, 0, 0, .5)
}
.vjs-subtitles {
    color: #fff
}
.vjs-captions {
    color: #fc6
}
.vjs-tt-cue {
    display: block
}
.vjs-fade-in {
    visibility: visible !important;
    opacity: 1 !important;
    -webkit-transition: visibility 0s linear 0s, opacity .3s linear;
    transition: visibility 0s linear 0s, opacity .3s linear
}
.vjs-fade-out {
    visibility: hidden !important;
    opacity: 0 !important;
    -webkit-transition: visibility 0s linear 1.5s, opacity 1.5s linear;
    transition: visibility 0s linear 1.5s, opacity 1.5s linear
}
.vjs-control {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 18px;
    text-align: center;
    vertical-align: middle;
    background-repeat: no-repeat;
    background-position: center
}
.vjs-control:focus {
    outline: 0
}
.vjs-control > div {
    background-repeat: no-repeat;
    background-position: center
}
.vjs-control-text {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0 0 0 0);
    border: 0
}
.vjs-play-control {
    width: 58px;
    height: 47px;
    cursor: pointer
}
.vjs-play-control > div {
    position: relative;
    height: 47px
}
.vjs-play-control > div:before, .vjs-play-control > div:after {
    position: absolute;
    top: 38%;
    left: 50%;
    margin: -.5em 0 0 -.5em;
    font-family: Flat-UI-Icons;
    font-size: 16px;
    color: #8f9fe2;
    -webkit-transition: color .25s, opacity .25s;
    transition: color .25s, opacity .25s;
    -webkit-font-smoothing: antialiased
}
.vjs-play-control > div:after {
    content: "\e615"
}
.vjs-play-control > div:before {
    content: "\e616"
}
.vjs-paused .vjs-play-control:hover > div:before {
    color: #16a085
}
.vjs-paused .vjs-play-control > div:after {
    filter: alpha(opacity=0);
    opacity: 0
}
.vjs-paused .vjs-play-control > div:before {
    filter: none;
    opacity: 1;
    -webkit-filter: none
}
.vjs-playing .vjs-play-control:hover > div:after {
    color: #16a085
}
.vjs-playing .vjs-play-control > div:after {
    filter: none;
    opacity: 1;
    -webkit-filter: none
}
.vjs-playing .vjs-play-control > div:before {
    filter: alpha(opacity=0);
    opacity: 0
}
.vjs-rewind-control {
    width: 5em;
    cursor: pointer !important
}
.vjs-rewind-control > div {
    width: 19px;
    height: 16px;
    margin: .5em auto 0;
    background: none transparent
}
.vjs-mute-control {
    float: right;
    margin: 14px 0;
    cursor: pointer !important
}
.vjs-mute-control:hover > div, .vjs-mute-control:focus > div {
    color: #57718b
}
.vjs-mute-control > div {
    height: 18px;
    color: #475d72
}
.vjs-mute-control > div:after, .vjs-mute-control > div:before {
    position: absolute;
    left: 50%;
    margin: 0 0 0 -.5em;
    font-family: Flat-UI-Icons;
    font-size: 16px;
    line-height: 18px;
    -webkit-transition: color .25s, opacity .25s;
    transition: color .25s, opacity .25s;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.vjs-mute-control > div:after {
    content: "\e617"
}
.vjs-mute-control > div:before {
    content: "\e618";
    filter: alpha(opacity=0);
    opacity: 0
}
.vjs-mute-control.vjs-vol-0 > div:after {
    filter: alpha(opacity=0);
    opacity: 0
}
.vjs-mute-control.vjs-vol-0 > div:before {
    filter: none;
    opacity: 1;
    -webkit-filter: none
}
.vjs-volume-control, .vjs-volume-level, .vjs-volume-handle, .vjs-volume-bar {
    display: none
}
.vjs-progress-control {
    position: absolute;
    top: 18px;
    right: 160px;
    left: 60px;
    width: auto;
    height: 12px;
    background: #425669;
    border-radius: 32px
}
.vjs-progress-holder {
    position: relative;
    height: 12px;
    padding: 0;
    margin: 0;
    cursor: pointer !important
}
.vjs-play-progress, .vjs-load-progress {
    display: block;
    height: 12px;
    padding: 0;
    margin: 0;
    border-radius: 32px
}
.vjs-play-progress {
    position: absolute;
    top: 0;
    left: -1px;
    background: #585f7a;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}
.vjs-load-progress {
    background: #d6dbdf
}
.vjs-load-progress[style*="100%"], .vjs-load-progress[style*="99%"] {
    border-radius: 32px
}
.vjs-seek-handle {
    position: absolute;
    top: 0;
    width: 18px;
    height: 18px;
    margin: -3px 0 0 -3px;
    background-color: #16a085;
    border-radius: 50%;
    -webkit-transition: background-color .25s;
    transition: background-color .25s
}
.vjs-seek-handle[style*="95."] {
    margin-left: 3px
}
.vjs-seek-handle[style="left: 0%;"] {
    margin-left: -2px
}
.vjs-seek-handle:hover, .vjs-seek-handle:focus {
    background-color: #148d75
}
.vjs-seek-handle:active {
    background-color: #117a65
}
.vjs-time-controls {
    position: absolute;
    width: auto;
    height: auto;
    font-family: Lato, Helvetica, Arial, sans-serif;
    font-size: 13px;
    font-weight: 300;
    line-height: normal
}
.vjs-time-divider {
    position: absolute;
    top: 11px;
    right: 114px;
    font-size: 14px;
    color: #5d6d7e
}
.vjs-remaining-time {
    display: none
}
.vjs-current-time {
    top: 16px;
    right: 122px
}
.vjs-duration {
    top: 16px;
    right: 85px;
    color: #5d6d7e
}
.vjs-fullscreen-control {
    float: right;
    margin: 14px 15px;
    cursor: pointer
}
.vjs-fullscreen-control:hover > div, .vjs-fullscreen-control:focus > div {
    color: #57718b
}
.vjs-fullscreen-control > div {
    height: 18px;
    color: #475d72
}
.vjs-fullscreen-control > div:before {
    position: absolute;
    left: 50%;
    margin: 0 0 0 -.5em;
    font-family: Flat-UI-Icons;
    font-size: 16px;
    line-height: 18px;
    content: "\e619";
    -webkit-transition: color .25s, opacity .25s;
    transition: color .25s, opacity .25s;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.vjs-menu-button {
    display: none !important
}
.vjs-loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    display: none;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    background: #ebedee;
    border-radius: 10px;
    -webkit-animation: sharp 2s ease infinite;
    animation: sharp 2s ease infinite
}
@-webkit-keyframes sharp {
    0% {
        background-color: #e74c3c;
        border-radius: 10px;
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    50% {
        background-color: #ebedee;
        border-radius: 0;
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg)
    }
    100% {
        background-color: #e74c3c;
        border-radius: 10px;
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}
@keyframes sharp {
    0% {
        background-color: #e74c3c;
        border-radius: 10px;
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    50% {
        background-color: #ebedee;
        border-radius: 0;
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg)
    }
    100% {
        background-color: #e74c3c;
        border-radius: 10px;
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}
.todo {
    margin-bottom: 20px;
    color: #798795;
    border-radius: 6px
}
.todo ul {
    padding: 0;
    margin: 0;
    list-style-type: none;
    background-color: #2c3e50;
    border-radius: 0 0 6px 6px
}
.todo li {
    position: relative;
    padding: 18px 42px 21px 25px;
    margin-top: 2px;
    font-size: 14px;
    line-height: 1.214;
    cursor: pointer;
    background: #34495e;
    background-size: 20px 20px;
    -webkit-transition: .25s;
    transition: .25s
}
.todo li:first-child {
    margin-top: 0
}
.todo li:last-child {
    padding-bottom: 21px;
    border-radius: 0 0 6px 6px
}
.todo li.todo-done {
    color: #8f9fe2;
    background: 0 0
}
.todo li.todo-done .todo-name {
    color: #8f9fe2
}
.todo li:after {
    position: absolute;
    top: 50%;
    right: 22px;
    display: block;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    content: " ";
    background: #fff;
    border-radius: 50%
}
.todo li.todo-done:after {
    font-family: Flat-UI-Icons;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    line-height: 21px;
    color: #2c3e50;
    text-align: center;
    text-transform: none;
    content: "\e60a";
    background: #585f7a;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.todo-search {
    position: relative;
    padding: 19px 25px 20px;
    color: #34495e;
    background: #585f7a;
    background-size: 16px 16px;
    border-radius: 6px 6px 0 0
}
.todo-search:before {
    position: absolute;
    top: 50%;
    left: 92%;
    display: inline-block;
    margin: -.5em 0 0 -1em;
    font-family: Flat-UI-Icons;
    font-size: 16px;
    line-height: 17px;
    content: "\e630"
}
.todo-search-field {
    padding: 5px 0;
    margin: 0;
    font-size: 19px;
    font-weight: 700;
    line-height: 23px;
    color: #34495e;
    text-indent: 0;
    background: 0 0;
    border: none;
    outline: 0;
    box-shadow: none
}
.todo-search-field::-moz-placeholder {
    color: #34495e;
    opacity: 1
}
.todo-search-field:-ms-input-placeholder {
    color: #34495e
}
.todo-search-field::-webkit-input-placeholder {
    color: #34495e
}
.todo-icon {
    float: left;
    padding: 11px 22px 0 0;
    font-size: 24px
}
.todo-content {
    padding-top: 1px;
    overflow: hidden
}
.todo-name {
    margin: 1px 0 3px;
    font-size: 17px;
    color: #fff
}
.pallete-item {
    float: left;
    width: 140px;
    margin: 0 0 20px 20px
}
.palette {
    padding: 15px;
    margin: 0;
    font-size: 14px;
    line-height: 1.214;
    color: #fff;
    text-transform: uppercase
}
.palette dt, .palette dd {
    line-height: 1.429
}
.palette dt {
    display: block;
    font-weight: 700;
    opacity: .8
}
.palette dd {
    margin-left: 0;
    font-weight: 300;
    opacity: .8;
    -webkit-font-smoothing: subpixel-antialiased
}
.palette-turquoise {
    background-color: #585f7a
}
.palette-green-sea {
    background-color: #16a085
}
.palette-emerald {
    background-color: #2ecc71
}
.palette-nephritis {
    background-color: #27ae60
}
.palette-peter-river {
    background-color: #3498db
}
.palette-belize-hole {
    background-color: #2980b9
}
.palette-amethyst {
    background-color: #9b59b6
}
.palette-wisteria {
    background-color: #8e44ad
}
.palette-wet-asphalt {
    background-color: #34495e
}
.palette-midnight-blue {
    background-color: #2c3e50
}
.palette-sun-flower {
    background-color: #f1c40f
}
.palette-orange {
    background-color: #f39c12
}
.palette-carrot {
    background-color: #e67e22
}
.palette-pumpkin {
    background-color: #d35400
}
.palette-alizarin {
    background-color: #e74c3c
}
.palette-pomegranate {
    background-color: #c0392b
}
.palette-clouds {
    background-color: #ecf0f1
}
.palette-silver {
    background-color: #bdc3c7
}
.palette-concrete {
    background-color: #95a5a6
}
.palette-asbestos {
    background-color: #7f8c8d
}
.palette-clouds {
    color: #bdc3c7
}
.palette-paragraph {
    font-size: 12px;
    line-height: 17px;
    color: #7f8c8d
}
.palette-paragraph span {
    color: #bdc3c7
}
.palette-headline {
    margin-top: -3px;
    font-size: 13px;
    font-weight: 700;
    color: #7f8c8d
}
.login {
    position: relative;
    padding: 38px 38px 267px;
    margin-bottom: 77px;
    color: #fff;
    background: url(../img/login/imac.png) 0 0 no-repeat;
    background-size: 940px 778px
}
.login-screen {
    min-height: 473px;
    padding: 123px 199px 33px 306px;
    background-color: #585f7a
}
.login-icon {
    position: absolute;
    top: 160px;
    left: 200px;
    width: 96px
}
.login-icon > img {
    display: block;
    width: 100%;
    margin-bottom: 6px
}
.login-icon > h4 {
    font-size: 17px;
    font-weight: 300;
    line-height: 34px;
    opacity: .95
}
.login-icon > h4 small {
    display: block;
    font-size: inherit;
    font-weight: 700;
    color: inherit
}
.login-form {
    position: relative;
    padding: 24px 23px 20px;
    background-color: #edeff1;
    border-radius: 6px
}
.login-form .control-group {
    position: relative;
    margin-bottom: 6px
}
.login-form .login-field {
    font-size: 17px;
    text-indent: 3px;
    border-color: transparent
}
.login-form .login-field:focus {
    border-color: #8f9fe2
}
.login-form .login-field:focus + .login-field-icon {
    color: #8f9fe2
}
.login-form .login-field-icon {
    position: absolute;
    top: 3px;
    right: 15px;
    font-size: 16px;
    color: #bfc9ca;
    -webkit-transition: all .25s;
    transition: all .25s
}
.login-link {
    display: block;
    margin-top: 15px;
    font-size: 13px;
    color: #bfc9ca;
    text-align: center
}
@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (-moz-min-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 3/2), only screen and (-o-min-device-pixel-ratio: 2/1), only screen and (-moz-min-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 2) {
    .login {
        background-image: url(../img/login/imac-2x.png)
    }
}
footer {
    padding: 0;
    font-size: 15px;
    color: #bac1c8;
    background-color: #edeff1
}
footer a {
    font-weight: 700;
    color: #9aa4af
}
footer p {
    margin-bottom: 10px;
    font-size: 15px;
    line-height: 20px
}
.footer-title {
    padding-top: 21px;
    margin: 0 0 22px;
    font-size: 24px;
    line-height: 40px
}
.footer-brand {
    display: block;
    width: 220px;
    margin-bottom: 26px
}
.footer-brand img {
    width: 216px
}
.footer-banner {
    min-height: 316px;
    padding: 0 30px 30px;
    margin-left: 42px;
    color: #d1f2eb;
    background-color: #585f7a
}
.footer-banner .footer-title {
    color: #fff
}
.footer-banner a {
    color: #b7f5e9;
    text-decoration: underline
}
.footer-banner a:hover {
    text-decoration: none
}
.footer-banner ul {
    padding: 0;
    margin: 0 0 26px;
    list-style-type: none
}
.footer-banner ul li {
    padding: 6px 0;
    line-height: 19px;
    border-top: 1px solid #1bc5a3
}
.footer-banner ul li:first-child {
    padding-top: 1px;
    border-top: none
}
.last-col {
    overflow: hidden
}
.ptn, .pvn, .pan {
    padding-top: 0
}
.ptx, .pvx, .pax {
    padding-top: 3px
}
.pts, .pvs, .pas {
    padding-top: 5px
}
.ptm, .pvm, .pam {
    padding-top: 10px
}
.ptl, .pvl, .pal {
    padding-top: 20px
}
.prn, .phn, .pan {
    padding-right: 0
}
.prx, .phx, .pax {
    padding-right: 3px
}
.prs, .phs, .pas {
    padding-right: 5px
}
.prm, .phm, .pam {
    padding-right: 10px
}
.prl, .phl, .pal {
    padding-right: 20px
}
.pbn, .pvn, .pan {
    padding-bottom: 0
}
.pbx, .pvx, .pax {
    padding-bottom: 3px
}
.pbs, .pvs, .pas {
    padding-bottom: 5px
}
.pbm, .pvm, .pam {
    padding-bottom: 10px
}
.pbl, .pvl, .pal {
    padding-bottom: 20px
}
.pln, .phn, .pan {
    padding-left: 0
}
.plx, .phx, .pax {
    padding-left: 3px
}
.pls, .phs, .pas {
    padding-left: 5px
}
.plm, .phm, .pam {
    padding-left: 10px
}
.pll, .phl, .pal {
    padding-left: 20px
}
.mtn, .mvn, .man {
    margin-top: 0
}
.mtx, .mvx, .max {
    margin-top: 3px
}
.mts, .mvs, .mas {
    margin-top: 5px
}
.mtm, .mvm, .mam {
    margin-top: 10px
}
.mtl, .mvl, .mal {
    margin-top: 20px
}
.mrn, .mhn, .man {
    margin-right: 0
}
.mrx, .mhx, .max {
    margin-right: 3px
}
.mrs, .mhs, .mas {
    margin-right: 5px
}
.mrm, .mhm, .mam {
    margin-right: 10px
}
.mrl, .mhl, .mal {
    margin-right: 20px
}
.mbn, .mvn, .man {
    margin-bottom: 0
}
.mbx, .mvx, .max {
    margin-bottom: 3px
}
.mbs, .mvs, .mas {
    margin-bottom: 5px
}
.mbm, .mvm, .mam {
    margin-bottom: 10px
}
.mbl, .mvl, .mal {
    margin-bottom: 20px
}
.mln, .mhn, .man {
    margin-left: 0
}
.mlx, .mhx, .max {
    margin-left: 3px
}
.mls, .mhs, .mas {
    margin-left: 5px
}
.mlm, .mhm, .mam {
    margin-left: 10px
}
.mll, .mhl, .mal {
    margin-left: 20px
}
/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */
@media print {
    .btn {
        border-style: solid;
        border-width: 2px
    }
    .dropdown-menu, .select2-drop {
        background: #fff !important;
        border: 2px solid #ddd
    }
    .input-group-rounded .input-group-btn + .form-control, .input-group-rounded .input-group-btn + .select2-search input[type=text], .input-group-rounded .input-group-btn + .select2-search input[type=text] {
        padding-left: 10px
    }
    .form-control, .select2-search input[type=text] {
        border: 2px solid #ddd !important
    }
    .bootstrap-switch {
        width: 84px;
        height: 33px;
        border: 2px solid #bdc3c7
    }
    .tooltip {
        border: 2px solid #bdc3c7
    }
    .progress, .ui-slider {
        background: #ddd !important
    }
    .progress-bar, .ui-slider-range, .ui-slider-handle {
        background: #bdc3c7 !important
    }
}