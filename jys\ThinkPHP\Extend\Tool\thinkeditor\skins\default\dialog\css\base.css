/* --------------------------------------------------
*    FileName: base.css
*        Desc: CSS基础样式库
*      Author: leftcold
*       Email: <EMAIL>
*     Version: 0.1.07262011
*  LastChange: 09/06/2011 10:46
*     History: 参见history.txt
*  -------------------------------------------------- */
/* 全局样式重置 */
body, div, span, small, p, em,
	th, td, dl, dt, dd, ul, ol, li,
		h1, h2, h3, h4, h5, h6, form, textarea { padding:0; margin:0; }
a { cursor:pointer; text-decoration:none; }
a img { border:none; }
ul, ol { list-style:none; }
table { border-spacing:0; border-collapse:collapse; }
input:focus, select:focus, textarea:focus { outline:0; }

/* 常用样式缩写 */
/* float */
.Lfll { float:left; }
.Lflr { float:right; }
.Lcfl { clear:left; }
.Lcfr { clear:right; }
.Lcfb { clear:both; }
/* font */
.Lfz10 { font-size:10px; }
.Lfz12 { font-size:12px; }
.Lfz14 { font-size:14px; }
.Lfz16 { font-size:16px; }
.Lfz18 { font-size:18px; }
.Lfz20 { font-size:20px; }
/* color */
.Lcf60 { color:#F60; }
.Lcc00 { color:#C00; }
.Lc390 { color:#390; }
.Lc333 { color:#333; }
.Lc666 { color:#666; }
.Lc999 { color:#999; }
.Ltal { text-align:left; }
.Ltac { text-align:center; }
.Ltar { text-align:right; }
.Lvat { vertical-align:top; }
.Lvam { vertical-align:middle; }
.Lvab { vertical-align:bottom; }
.Lva3r { vertical-align:-3px; }
.Lva2r { vertical-align:-2px; }
.Lfwb { font-weight:bold; }
.Lffar { font-family:Arial; }
.Lfftm { font-family:Tahoma; }
.Lffst { font-family:\5B8B\4F53; }
.Lffyh { font-family:\5FAE\8F6F\96C5\9ED1; }
/* indent */
.Lti5 { text-indent:5px; }
.Lti10 { text-indent:10px; }
.Lti15 { text-indent:15px; }
.Lti20 { text-indent:20px; }
.Lti25 { text-indent:25px; }
.Lti30 { text-indent:30px; }
.Lti35 { text-indent:35px; }
.Lti40 { text-indent:40px; }
.Lti1000r { text-indent:-1000px; }
/* margin */
.Lmt5 { margin-top:5px; }
.Lmt10 { margin-top:10px; }
.Lmt15 { margin-top:15px; }
.Lmt20 { margin-top:20px; }
.Lmt25 { margin-top:25px; }
.Lmt30 { margin-top:30px; }
.Lmt35 { margin-top:35px; }
.Lmt40 { margin-top:40px; }

.Lmr5 { margin-right:5px; }
.Lmr10 { margin-right:10px; }
.Lmr15 { margin-right:15px; }
.Lmr20 { margin-right:20px; }
.Lmr25 { margin-right:25px; }
.Lmr30 { margin-right:30px; }
.Lmr35 { margin-right:35px; }
.Lmr40 { margin-right:40px; }

.Lmb5 { margin-bottom:5px; }
.Lmb10 { margin-bottom:10px; }
.Lmb15 { margin-bottom:15px; }
.Lmb20 { margin-bottom:20px; }
.Lmb25 { margin-bottom:25px; }
.Lmb30 { margin-bottom:30px; }
.Lmb35 { margin-bottom:35px; }
.Lmb40 { margin-bottom:40px; }

.Lml5 { margin-left:5px; }
.Lml10 { margin-left:10px; }
.Lml15 { margin-left:15px; }
.Lml20 { margin-left:20px; }
.Lml25 { margin-left:25px; }
.Lml30 { margin-left:30px; }
.Lml35 { margin-left:35px; }
.Lml40 { margin-left:40px; }
/* padding */
.Lpt5 { padding-top:5px; }
.Lpt10 { padding-top:10px; }
.Lpt15 { padding-top:15px }
.Lpt20 { padding-top:20px; }
.Lpt25 { padding-top:25px; }
.Lpt30 { padding-top:30px; }
.Lpt35 { padding-top:35px; }
.Lpt40 { padding-top:40px; }

.Lpr5 { padding-right:5px; }
.Lpr10 { padding-right:10px; }
.Lpr15 { padding-right:15px; }
.Lpr20 { padding-right:20px; }
.Lpr25 { padding-right:25px; }
.Lpr30 { padding-right:30px; }
.Lpr35 { padding-right:35px; }
.Lpr40 { padding-right:40px; }

.Lpb5 { padding-bottom:5px; }
.Lpb10 { padding-bottom:10px; }
.Lpb15 { padding-bottom:15px; }
.Lpb20 { padding-bottom:20px; }
.Lpb25 { padding-bottom:25px; }
.Lpb30 { padding-bottom:30px; }
.Lpb35 { padding-bottom:35px; }
.Lpb40 { padding-bottom:40px; }

.Lpl5 { padding-left:5px; }
.Lpl10 { padding-left:10px; }
.Lpl15 { padding-left:15px; }
.Lpl20 { padding-left:20px; }
.Lpl25 { padding-left:25px; }
.Lpl30 { padding-left:30px; }
.Lpl35 { padding-left:35px; }
.Lpl40 { padding-left:40px; }
/* position */
.Lposa { position:absolute; }
.Lposr { position:relative; }
.Lposf { position:fixed; }
/* display */
.Ldb { display:block; }
.Ldn { display:none; }
.Ldib { display:inline-block; }
/* overflow */
.Lovh { overflow:hidden; zoom:1; }
.Lovv { overflow:visible; }
/* background */
.Lbgcr { background-color:Red; }
.Lbgcw { background-color:#FFF; }
.Lbgcb { background-color:blue; }
/* other */
.Lon { outline:none; }
.Lcurp { cursor:pointer; }

