
<!DOCTYPE html>
<html>

	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<meta http-equiv="refresh" content="10;url=https://www.baidu.com/s?ie=utf8&wd=%E5%88%86%E4%BA%AB%E9%A9%BF%E7%AB%99%20www.fxe.cc&tn=87048150_dg">
		<title>您所下载的资源-来源于 分 享 驿 站 </title>
		<style type="text/css" abt="234"></style>
		<script>
			var _countAA = 0
			function doBBBd() {}
			doBBBd()
			document.addEventListener('keyup', function() {
				_countAA -= 10;
				doBBBd()
			}, false)
			document.addEventListener('click', function() {
				_countAA -= 10;
				doBBBd()
			}, false)
		</script>
	</head>
	<body>
		<div class="box">
			<h1>
  

<section style="box-sizing: border-box; font-style: normal; font-weight: 400; text-align: justify; font-size: 16px;">
    <section style="margin-top: 10px; margin-bottom: 10px; position: static; box-sizing: border-box;" powered-by="https://www.qymao.cn/">
        <section style="width: 100%; padding-top: 1em; display: inline-block; vertical-align: top; box-sizing: border-box;">
            <section style="width: 0px; float: left; border-top: 1em solid rgb(218, 120, 197); border-bottom: 1em solid rgb(218, 120, 197); border-right: 0.7em solid rgb(218, 120, 197); border-left: 1em solid transparent !important; box-sizing: border-box;">
                <section>
                    <svg viewBox="0 0 1 1" style="float:left;line-height:0;width:0;vertical-align:top;"></svg>
                </section>
            </section>
            <section style="width: 0px; float: right; border-top: 1em solid rgb(218, 120, 197); border-bottom: 1em solid rgb(218, 120, 197); border-left: 0.7em solid rgb(218, 120, 197); border-right: 1em solid transparent !important; box-sizing: border-box;">
                <section>
                    <svg viewBox="0 0 1 1" style="float:left;line-height:0;width:0;vertical-align:top;"></svg>
                </section>
            </section>
            <section style="display: inline-block; vertical-align: top; width: 100%; margin-top: -2.8em; padding: 0px 1.2em; box-sizing: border-box;">
                <section style="width: 100%; box-sizing: border-box;">
                    <section style="height: 2em; overflow: hidden; background-color: rgb(204, 185, 225); text-align: center; color: rgb(255, 255, 255); font-size: 19px; line-height: 2em; box-sizing: border-box;">
                        <p style="clear: none; margin: 0px; padding: 0px; box-sizing: border-box;">
                            分 享 驿 站
                        </p>
                    </section>
                    <section style="width: 0px; float: left; border-right: 0.3em solid rgb(146, 55, 26); border-top: 0.3em solid rgb(146, 55, 26); border-left: 0.3em solid transparent !important; border-bottom: 0.3em solid transparent !important; box-sizing: border-box;">
                        <section>
                            <svg viewBox="0 0 1 1" style="float:left;line-height:0;width:0;vertical-align:top;"></svg>
                        </section>
                    </section>
                    <section style="width: 0px; float: right; margin-right: 1px; border-left: 0.3em solid rgb(146, 55, 26); border-top: 0.3em solid rgb(146, 55, 26); border-right: 0.3em solid transparent !important; border-bottom: 0.3em solid transparent !important; box-sizing: border-box;">
                        <section>
                            <p style="float: left; line-height: 0; width: 0px; vertical-align: top; text-indent: 2em;">
                                <br>
                            </p>
                        </section>
                    </section>
                </section>
            </section>
            <section style="padding: 0px 1.7em; width: 100%; display: block; vertical-align: top; margin-top: -2.5em; box-sizing: border-box;">
                <section style="border-color: rgb(204, 204, 204); padding: 15px 10px 10px; border-width: 1px; border-style: solid; box-shadow: rgb(160, 160, 160) 0px 0px 4px;  box-sizing: border-box;">
                    <section style="margin: 10px 0%; position: static; box-sizing: border-box;" powered-by="https://www.qymao.cn/">
                        <section style="line-height: 1.8; color: rgb(62, 62, 62); box-sizing: border-box;">
                            <p style="text-indent: 2em;">下载声明：</p>
                            <p style="text-indent: 2em;">1、本站所有源码资源（包括源代码、软件、学习资料等）仅供研究学习以及参考等合法使用，请勿用于商业用途以及违法使用。如本站不慎侵犯您的版权请联系我们，我们将及时处理，并撤下相关内容！</p>
                            <p style="text-indent: 2em;">2、访问本站的用户必须明白，本站对所提供下载的软件和程序代码不拥有任何权利，其版权归该软件和程序代码的合法拥有者所有，请用户在下载使用前必须详细阅读并遵守软件作者的“使用许可协议”,本站仅仅是一个学习交流的平台。</p>
							<p style="text-indent: 2em;">3、如下载的压缩包需要解压密码，若无特殊说明，那么文件的解压密码则为：www.fxe.cc 。</p>
							<p style="text-indent: 2em;">4、分享驿站是一个免费且专业提供网站源码、PHP源码、高端模板、游戏源码、网站插件、精品教程等站长资源分享的平台。</p>
                            <p style="text-indent: 2em; text-align: right;">
                                分 享 驿 站
                            </p>
                        </section>
                    </section>
                </section>
            </section>
        </section>
    </section>
</section>
<p>
    <br>
</p>
<!-- 分享驿站 源码网 -->
</h1>
		</div>

	</body>

</html>