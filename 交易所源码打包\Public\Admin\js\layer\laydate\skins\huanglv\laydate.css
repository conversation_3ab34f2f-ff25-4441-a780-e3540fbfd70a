/** 
 
 @Name： laydate皮肤：黄绿
 @Author： 杉之忆
 @Site：http://sentsin.com/layui/laydate
 
**/

.laydate-icon{border:1px solid #ccc; background-image:url(icon.png)}

.laydate_body .laydate_bottom #laydate_hms,
.laydate_body .laydate_time{border:1px solid #ccc;}

.laydate_body .laydate_box, 
.laydate_body .laydate_ym .laydate_yms,
.laydate_body .laydate_time{box-shadow: 2px 2px 5px rgba(0,0,0,.1);}

.laydate_body .laydate_box{border-top:none; border-bottom:none; background-color:#fff; color:#4B7C03;}
.laydate_body .laydate_box input{background:none!important; color:#fff;}
.laydate_body .laydate_box .laydate_void{color:#C0CEBB!important;}
.laydate_body .laydate_box a, .laydate_body .laydate_box a:hover{color:#4B7C03;}
.laydate_body .laydate_box a:hover{color:#666;}
.laydate_body .laydate_click{background-color:#78BA32!important; color:#fff!important;}
.laydate_body .laydate_top{border-top:1px solid #78BA32; background-color:#78BA32}
.laydate_body .laydate_ym{border:1px solid #78BA32; background-color:#78BA32;}
.laydate_body .laydate_ym .laydate_yms{border:1px solid #78BA32; background-color:#78BA32; color:#fff;}
.laydate_body .laydate_y .laydate_yms a{border-bottom:1px solid #78BA32;}
.laydate_body .laydate_y .laydate_yms .laydate_chdown{border-top:1px solid #78BA32; border-bottom:none;}
.laydate_body .laydate_choose{border-left:1px solid #78BA32;}
.laydate_body .laydate_chprev{border-left:none; border-right:1px solid #78BA32;}
.laydate_body .laydate_choose:hover, 
.laydate_body .laydate_y .laydate_yms a:hover{background-color:#8CCF1D;}
.laydate_body .laydate_chtop cite{border-bottom-color:#fff;}
.laydate_body .laydate_chdown cite, .laydate_body .laydate_ym label{border-top-color:#fff;}
.laydate_body .laydate_chprev cite{border-right-style:solid; border-right-color:#fff;}
.laydate_body .laydate_chnext cite{border-left-style:solid; border-left-color:#fff;}
.laydate_body .laydate_table{width: 240px!important; margin: 0!important; border:1px solid #ccc; border-top:none; border-bottom:none;}
.laydate_body .laydate_table td{border:none;  height:21px!important; line-height:21px!important; background-color:#fff; color:#4B7C03;}
.laydate_body .laydate_table .laydate_nothis{color:#999;}
.laydate_body .laydate_table thead{border-bottom:1px solid #ccc; height:21px!important; line-height:21px!important;}
.laydate_body .laydate_table thead th{}
.laydate_body .laydate_bottom{border:1px solid #ccc; border-top:none;}
.laydate_body .laydate_bottom #laydate_hms{background-color:#fff;}
.laydate_body .laydate_time{background-color:#fff;}
.laydate_body .laydate_time1{width: 226px!important; height: 152px!important;}
.laydate_body .laydate_bottom .laydate_sj{width:31px!important; border-right:1px solid #ccc; background-color:#fff;}
.laydate_body .laydate_bottom input{background-color:#fff; color:#4B7C03;}
.laydate_body .laydate_bottom .laydte_hsmtex{border-bottom:1px solid #ccc;}
.laydate_body .laydate_bottom .laydate_btn{border-right:1px solid #ccc;}
.laydate_body .laydate_bottom .laydate_v{color:#999}
.laydate_body .laydate_bottom .laydate_btn a{border: 1px solid #ccc; border-right:none; background-color:#fff;}
.laydate_body .laydate_bottom .laydate_btn a:hover{background-color:#F6F6F6; color:#4B7C03;}

.laydate_body .laydate_m .laydate_yms span:hover,
.laydate_body .laydate_time .laydate_hmsno span:hover,
.laydate_body .laydate_y .laydate_yms ul li:hover,
.laydate_body .laydate_table td:hover{background-color:#8CCF1D; color:#fff;}


