<include file="Public:header"/>
<div id="main-content">
    <div id="top-alert" class="fixed alert alert-error" style="display: none;">
        <button class="close fixed" style="margin-top: 4px;">&times;</button>
        <div class="alert-content">警告内容</div>
    </div>
    <div id="main" class="main">
        <div class="main-title-h">
            <span class="h1-title">市场配置(调用外部数据的交易对)</span>
        </div>

        <div class="cf">
            <div class="fl">
                <a class="btn btn-success" href="{:U('Config/ctmarketEdit')}">新 增</a>
                <button class="btn ajax-post btn-info" url="{:U('Config/ctmarketoStatus',array('type'=>'resume'))}" target-form="ids">启 用
                </button>
                <button class="btn ajax-post btn-warning" url="{:U('Config/ctmarketoStatus',array('type'=>'forbid'))}" target-form="ids">禁 用
                </button>
                <button class="btn ajax-post confirm btn-danger" url="{:U('Config/ctmarketoStatus',array('type'=>'del'))}" target-form="ids">删 除
                </button>
            </div>
        </div>
        <div class="data-table table-striped">
            <table class="">
                <thead>
					<tr>
						<th class="row-selected row-selected"><input class="check-all" type="checkbox"/></th>
						<th class="">ID</th>
						<th class="">市场交易对</th>
						<th class="">排序</th>
						<th class="">显示状态</th>
						<th class="">交易状态</th>
						<th class="" style="text-align: center;">操作</th>
					</tr>
                </thead>
                <tbody>
                <notempty name="list">
                    <volist name="list" id="vo">
                        <tr>
                            <td><input class="ids" type="checkbox" name="id[]" value="{$vo.id}"/></td>
                            <td>{$vo.id}</td>
                            <td>{$vo.title}</td>
                            <td>{$vo.sort}</td>
                            <td>
                                <eq name="vo.status" value="1">
                                    <b style="color: #028E16;">可用</b>
                                <else/>
                                    <b style="color: #F70408">禁用</b>
                                </eq>
                            </td>
                            
                            <td>
                                <eq name="vo.state" value="1">
                                    <b style="color: #028E16;">正常</b>
                                <else/>
                                    <b style="color: #F70408">禁止</b>
                                </eq>
                            </td>

                            <td style="text-align: center;">
								<a href="{:U('Config/ctmarketEdit?id='.$vo['id'])}" class="btn btn-primary btn-xs">编辑</a>
                            </td>
                        </tr>
                    </volist>
                    <else/>
                    <td colspan="12" class="text-center empty-info"><i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据</td>
                </notempty>
                </tbody>
            </table>
            <div class="page">
                <div>{$page}</div>
            </div>
        </div>
    </div>
</div>
<include file="Public:footer"/>
<block name="script">
    <script type="text/javascript" charset="utf-8">
        //导航高亮
        highlight_subnav("{:U('Config/ctmarket')}");
    </script>
</block>