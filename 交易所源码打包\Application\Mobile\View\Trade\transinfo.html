<!DOCTYPE html>
<html lang="zh-CN" style="background:#121420;">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/base2.css" />
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/nologed.css" />
	<link rel="stylesheet" href="/Public/Static/Icoinfont/iconfont.css">
	<script src="/Public/Static/Icoinfont/iconfont.js"></script>
	<title>{$webname}</title>
	<style>
	::-webkit-input-placeholder {color: #b5b5b5;font-size: 12px;}
	::-moz-placeholder {color: #b5b5b5;font-size: 12px;}
	input:focus{background:#fff;outline: 1px solid #fff;}
	a:hover,a:link,a:visited,a:active{color:#000000;text-decoration:none;}
	.no_header{position: fixed;z-index: 9999;padding:0px 10px;top:0px;background: #121420;}
	.txtl{line-height:50px;width:30%;}
	.contentbox{width:100%;background:#1b1d29;padding:10px;}
	.content_title{width:100%;height:40px;line-height:40px;margin-top:50px;position: fixed;z-index: 9999;top:0px;padding:0px 10px;background:#1b1d29;border-bottom:1px solid #272936;}
	.content_title_l{width:70%;height:40px;line-height:40px;float:left;}
	.content_title_r{width:30%;height:40px;line-height:40px;float:right;padding:0px 5px;}
	.tleft{text-align:left;}
	.tright{text-align:right;}
	.tradebox{width:100%;height:400px;background:#1b1d29;}
	.tradebox_l{width:58%;height:400px;float:left;}
	.tradebox_r{width:40%;height:400px;float:right;}
	.tradebox_l_btn{width:100%;height:36px;}
	.tradebox_l_buybtn{width:48%;height:36px;line-height:36px;float:left;text-align:center;border-radius:5px;}
	.tradebox_l_sellbtn{width:48%;height:36px;line-height:36px;float:right;text-align:center;border-radius:5px;}
	.bggreen{background:#0ecb81;}
	.green{color:#0ecb81;}
    .bgred{background:#f5465c;}
    .red{color:#f5465c;}
    .bghc{background:#212332;}
    .cfff{color:#fff;}
    .c000{color:#000;}
    .formbox{width:100%;height:350px;margin-top:15px;}
    .formbox_op{width:100%;height:30px;}
    .formbox_op_list{width:50%;height:30px;line-height:30px;float:left;text-align:center;}
    .inputbox{width:100%;height:36px;border:1px solid #212332;border-radius:5px;margin-top:10px;}
	.inputbox_float{width:60%;height:36px;float:left;background: #212332}
	.xjinput{width:90%;background:#212332;border:#212332;margin-top:7px;padding:0px 10px;}
	.input_bi{width:50%;height:26px;line-height:26px;float:left;text-align:center;margin-top:5px;}
	.borderright{border-right:1px solid #212332;}
	.bistyle{font-size:24px;font-weight:bold;}
	.blbox{width:100%;height:30px;margin-top:15px;}
	.blbox_1{width:20%;height:30px;float:left;margin-right:6%;}
	.blbox_2{width:20%;height:30px;float:left;}
	.blbox_3{width:100%;height:10px;}
	.bgf5{background:#f5f5f5;}
	.blbox_4{width:100%;height:20x;line-height:20px;text-align:center;}
	.tradebox_title{width:50%;height:20px;line-height:20px;float:left;}
	.tl{text-align:left;}
	.tr{text-align:right;}
	.tc{text-align:center;}
	.fl{float:left;}
	.fr{float:right;}
	.trade_listbox{width:100%;height:160px;padding:5px;overflow:hidden;}
	.trade_listpricebox{width:100%;height:40px;line-height:40px;padding:0px 10px;}
	.trade_list{width:50%;height:30px;line-height:30px;float:left;}
	.dongbox{position:fixed;z-index:9999;display:none;top:0px;width:100%;height:100vh;background:rgba(0,0,0,0.2);}
	.dong_con{width:80%;height:100vh;background:#212332;margin-top:0px;border-top-right-radius:0px;border-bottom-right-radius:0px;padding:20px 15px 10px 20px;float:left;}
	.dong_title{width:100%;height:40px;line-height:40px;}
	.dong_title_span{font-size:18px;font-weight:500;}
	.dong_find_box{width:100%;height:30px;background:#f5f5f5;border-radius:10px;}
	.dong_find_box_img{width:20%;height:30px;line-height:30px;float:left;text-align:center;}
	.dong_input_box{width:80%;height:30px;float:left;}
	.dong_symbox{width:90%;height:20px;border:none;background:#f5f5f5;margin-top:5px;}
	.dong_sel_box{width:100%;height:30px;border-bottom:1px solid #272936;}
	.dong_sel_span{display:block;width:35px;height:30px;line-height:30px;border-bottom:2px solid #FCD535;font-size:14px;text-align:center;font-size:14px;}
	.symbol_list{width:100%;height:80vh;overflow-y: scroll;margin-top:10px;background: #212332; }
	.sy_list_box{width:100%;height:50px;}
	.sy_list_boxl{width:35%;height:30px;line-height:30px;float:left;text-align:left;}
	.sy_list_boxr{width:30%;height:30px;line-height:30px;float:right;text-align:center;}
	.order_title{display: inline-block;margin-right: 20px;font-weight: 1000;}
	.FCD535{border-bottom: 2px solid #FCD535;}
	.fccbox{width:100%;height:15px;background:#272936;}
	.wtlistbox{width:100%;padding:0px 10px;background: #1b1d29}
	.o_title_box{width:50%;height:40px;line-height:40px;border-bottom:1px solid #272936;}
	.tlistbox{width:100%;clear:both;padding:10px 0px;}
	.tlistbox_1{width:100%;height:100px;border-bottom:1px solid #f5f5f5;}
	.tlistbox_2{width:100%;height:30px;}
	.tlistbox_3{width:80%;height:30px;line-height:30px;}
	.tlistbox_4{width:20%;height:30px;line-height:30px;}
	.tlistbox_5{padding:5px 10px;background:#fcfcfc;border-radius:5px;}
	.tlistbox_6{width:100%;height:60px;}
	.tlistbox_7{width:33.33%;height:60px;}
	.tlistbox_8{width:100%;height:30px;line-height:30px;}
	.tlistbox_9{width:100%;height:30px;line-height:20px;}

	.bi-plus::before{
		color: #707A8A;
	}
	.bi-dash::before {
		color: #707A8A;
	}

	#buynum::placeholder{
		color: #707A8A;
	}

	.blbox_3{
		background: #212332;
		border-radius: 5px;
	}
	.bggreen {
		background: #0ecb81;
	}

	.c000{
		color: #707A8A;
	}
	.bi-text-indent-left::before  {
		color: #e6e6e6;
	}
	.bi-journal-text::before {
		color: #e6e6e6;
	}

	.noordersvg {
		margin: 0 auto;
		padding-top: 100px;
	}

	input{
		outline: none;
	}
	.sy_list_boxl span {
		color: #e6e6e6 !important;
	}
	</style>
  </head>
  <body style="background:#1b1d29;">
	<div class="container-fluid " style="padding:0px;width:100vw;">
		<div class="no_header">
            <div class="fl allhg txtl">
				<span class="fe6" style="font-size:20px;font-weight:bold;">{:L('币币交易')}</span>
			</div>
			<div class="fl allhg" style="width:30%;"></div>
			<div class="fr allhg txtr" style="line-height:50px;width:10%;"></div>
		</div>
		<div class="content_title">
		    <div  class="content_title_l tleft">

				<svg class="bi bi-text-indent-left fch" id="centerbox" style="margin-top: -4px;" width="22" height="22" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M24 19H40" stroke="#e6e6e6" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/><path d="M24 10H40" stroke="#e6e6e6" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 38H40" stroke="#e6e6e6" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 28H40" stroke="#e6e6e6" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10L16 15L8 20V10Z" fill="#e6e6e6" stroke="#e6e6e6" stroke-width="3" stroke-linejoin="round"/></svg>
		        
		        <span class="fe6" style="font-size:18px;font-weight:bold;" id="marketbox">{$coinname}</span>
		        <span style="color:#f5465c;" id="changebox">--</span>
		    </div>
		    
		    <a href="/Trade/trans.html?sytx={$coinname}" >
		    <div  class="content_title_r tright">
				<svg t="1655970499774"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8534" width="28" height="28"><path d="M225.28 303.476364 184.32 303.476364 184.32 613.282909 225.28 613.282909 225.28 720.523636 245.76 720.523636 245.76 613.282909 286.72 613.282909 286.72 303.476364 245.76 303.476364 245.76 184.32 225.28 184.32 225.28 303.476364 225.28 303.476364 225.28 303.476364 225.28 303.476364ZM409.6 506.042182 368.64 506.042182 368.64 753.887418 409.6 753.887418 409.6 839.68 430.08 839.68 430.08 753.887418 471.04 753.887418 471.04 506.042182 430.08 506.042182 430.08 410.717091 409.6 410.717091 409.6 506.042182 409.6 506.042182 409.6 506.042182ZM378.88 517.12 460.8 517.12 460.8 742.4 378.88 742.4 378.88 517.12 378.88 517.12 378.88 517.12ZM778.24 424.391111 737.28 424.391111 737.28 595.968 778.24 595.968 778.24 655.36 798.72 655.36 798.72 595.968 839.68 595.968 839.68 424.391111 798.72 424.391111 798.72 358.4 778.24 358.4 778.24 424.391111 778.24 424.391111 778.24 424.391111ZM747.52 432.06 829.44 432.06 829.44 588.015555 747.52 588.015555 747.52 432.06 747.52 432.06 747.52 432.06ZM593.92 266.405495 552.96 266.405495 552.96 479.827782 593.92 479.827782 593.92 553.704727 614.4 553.704727 614.4 479.827782 655.36 479.827782 655.36 266.405495 614.4 266.405495 614.4 184.32 593.92 184.32 593.92 266.405495 593.92 266.405495 593.92 266.405495Z" p-id="8535" fill="#3db485"></path></svg>
		    </div>
		    </a>
		</div>
		
		
		<div class="contentbox" style="margin-top:90px;">
		    <div class="tradebox">
		        <div class="tradebox_l">
		            
		            <div class="tradebox_l_btn">
		                <div class="tradebox_l_buybtn bggreen" id="buybtn">
		                    <span class="fzmm cfff" id="buybtnspan">{:L('买入')}</span>
		                </div>
		                <div class="tradebox_l_sellbtn bghc" id="sellbtn">
		                    <span  class="fzmm c000" id="sellbtnspan">{:L('卖出')}</span>
		                </div>
		            </div>
		            
		            <!--买入框-->
		            <div class="formbox" id="buycoinbox" style="display:block;">
		                <div class="formbox_op">
		                    <div class="formbox_op_list" id="buyxjbtn">
		                        <span class="fzmm fcy" id="xjspan">{:L('限价委托')}</span>
		                    </div>
		                    <div class="formbox_op_list" id="buysjbtn">
		                        <span class="fzmm fcc" id="sjspan">{:L('市价委托')}</span>
		                    </div>
		                </div>
		                
		                <!--限价委托单价框-->
		                <div class="inputbox" id="xjbox">
		                    <div class="inputbox_float">
		                        <input type="text" id="newprice" class="fcc xjinput" value=""  />
		                    </div>
		                    <div class="inputbox_float" style="width:40%;">
		                        <div class="input_bi borderright" id="dash_buyprice">
		                            <i class="bi bi-dash bistyle"></i>
		                        </div>
		                        <div class="input_bi" id="plus_buyprice">
		                            <i class="bi bi-plus bistyle"></i>
		                        </div>
		                    </div>
		                </div>
		                
		                <div class="inputbox" style="background: #212332;display:none" id="sjbox">
		                    <div class="inputbox_float">
		                        <input type="text"  class="fcc xjinput" placeholder="{:L('按市场最优价')}"  />
		                    </div>
		                </div>
		                
		                <div class="inputbox" id="sjnumbox" style="display:none;border:none;"></div>
		                
		                <div class="inputbox" id="xjnumbox" style="display:block;">
		                    <div class="inputbox_float">
		                        <input type="text" id="buynum" oninput="buynumfc();" class="fcc xjinput" value="" placeholder="{:L('输入数量')}"  autocomplete="off" step="1"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,6})?/) ? this.value.match(/\d+(\.\d{0,6})?/)[0] : ''" />
		                    </div>
		                    <div class="inputbox_float" style="width:40%;">
		                        <div class="input_bi borderright" id="dash_buynum">
		                            <i class="bi bi-dash bistyle"></i>
		                        </div>
		                        <div class="input_bi" id="plus_buynum">
		                            <i class="bi bi-plus bistyle"></i>
		                        </div>
		                    </div>
		                </div>
		                
		                
		                <div class="blbox">
		                    <div class="blbox_1" onclick="buyblfc(1,25);">
		                        <div class="blbox_3 bgf5" id="buybl_1"></div>
		                        <div class="blbox_4">
		                            <span class="fzm fcc">25%</span>
		                        </div>
		                    </div>
		                    <div class="blbox_1" onclick="buyblfc(2,50);">
		                        <div class="blbox_3 bgf5" id="buybl_2"></div>
		                        <div class="blbox_4">
		                            <span class="fzm fcc">50%</span>
		                        </div>
		                    </div>
		                    <div class="blbox_1" onclick="buyblfc(3,75);">
		                        <div class="blbox_3 bgf5" id="buybl_3"></div>
		                        <div class="blbox_4">
		                            <span class="fzm fcc">75%</span>
		                        </div>
		                    </div>
		                    <div class="blbox_2" onclick="buyblfc(4,100);">
		                        <div class="blbox_3 bgf5" id="buybl_4"></div>
		                        <div class="blbox_4">
		                            <span class="fzm fcc">100%</span>
		                        </div>
		                    </div>
		                </div>
		                
		                <div style="width:100%;height:30px;margin-top:20px;">
		                    <div style="width:30%;height:30px;line-height:30px;float:left;">
		                        <span class="fzm fcc">{:L('可用')}</span>
		                    </div>
		                    <div style="width:70%;height:30px;line-height:30px;float:left;text-align:right;">
		                        <span class="fzm fcc">{$usdt_blance}</span>
		                        <span class="fzm fcc">USDT</span>
		                    </div>
		                </div>
		                
		                <div class="inputbox">
		                    <div class="inputbox_float">
		                        <input type="text" id="buyusdt" oninput="buyusdtfc();" class="fcc xjinput" value="" placeholder="{:L('交易额')}"  autocomplete="off" step="1"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,6})?/) ? this.value.match(/\d+(\.\d{0,6})?/)[0] : ''" />
		                    </div>
		                    <div class="inputbox_float" style="width:40%;text-align:center;line-height:36px;">
		                        <span class="fzmm fcc">USDT</span>
		                    </div>
		                </div>
		                
		                <if condition="$uid egt 1">
		                <div onclick="bb_buycoin(1);" style="width:100%;height:36px;line-height:36px;background:#0ecb81;text-align:center;color:#fff;border-radius:5px;margin-top:20px;">
		                    <span class="fzmm" style="color:#fff;">{:L('买入')}</span>
		                </div>
		                <else />
		                <a href="{:U('Login/index')}">
		                <div style="width:100%;height:36px;line-height:36px;background:#0ecb81;text-align:center;color:#fff;border-radius:5px;margin-top:20px;">
		                    <span class="fzmm" style="color:#fff;">{:L('登陆')}</span>
		                </div>
		                </a>
		                </if>
		                
		                
		            </div>
		            
		            
		            
		            <!--卖出框-->
		            <div class="formbox" id="sellcoinbox" style="display:none;">
		                <div class="formbox_op">
		                    <div class="formbox_op_list" id="sell_xjbtn">
		                        <span class="fzmm fcy" id="sell_xjspan">{:L('限价委托')}</span>
		                    </div>
		                    <div class="formbox_op_list" id="sell_sjbtn">
		                        <span class="fzmm fcc" id="sell_sjspan">{:L('市价委托')}</span>
		                    </div>
		                </div>
		                
		                <!--限价委托单价框-->
		                <div class="inputbox" id="sell_xjbox">
		                    <div class="inputbox_float">
		                        <input type="text" id="sell_newprice" class="fcc xjinput" value=""  />
		                    </div>
		                    <div class="inputbox_float" style="width:40%;">
		                        <div class="input_bi borderright" id="dash_sellprice">
		                            <i class="bi bi-dash bistyle"></i>
		                        </div>
		                        <div class="input_bi" id="plus_sellprice">
		                            <i class="bi bi-plus bistyle"></i>
		                        </div>
		                    </div>
		                </div>
		                
		                <div class="inputbox" style="background: #212332;display:none" id="sell_sjbox">
		                    <div class="inputbox_float">
		                        <input type="text"  class="fcc xjinput" placeholder="{:L('按市场最优价')}" style="background:#f5f5f5;"  />
		                    </div>
		                </div>
		                
		                <div class="inputbox" id="sell_sjnumbox" style="display:none;border:none;"></div>
		                
		                <div class="inputbox" id="sell_xjnumbox" style="display:block;">
		                    <div class="inputbox_float">
		                        <input type="text" id="sell_num" oninput="sellnumfc();" class="fcc xjinput" value="" placeholder="{:L('输入数量')}" autocomplete="off" step="1"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,6})?/) ? this.value.match(/\d+(\.\d{0,6})?/)[0] : ''" />
		                    </div>
		                    <div class="inputbox_float" style="width:40%;">
		                        <div class="input_bi borderright" id="dash_sellnum">
		                            <i class="bi bi-dash bistyle"></i>
		                        </div>
		                        <div class="input_bi" id="plus_sellnum">
		                            <i class="bi bi-plus bistyle"></i>
		                        </div>
		                    </div>
		                </div>
		                
		                
		                <div class="blbox">
		                    <div class="blbox_1" onclick="sellblfc(1,25);">
		                        <div class="blbox_3 bgf5" id="sellbl_1"></div>
		                        <div class="blbox_4">
		                            <span class="fzm fcc">25%</span>
		                        </div>
		                    </div>
		                    <div class="blbox_1" onclick="sellblfc(2,50);">
		                        <div class="blbox_3 bgf5" id="sellbl_2"></div>
		                        <div class="blbox_4">
		                            <span class="fzm fcc">50%</span>
		                        </div>
		                    </div>
		                    <div class="blbox_1" onclick="sellblfc(3,75);">
		                        <div class="blbox_3 bgf5" id="sellbl_3"></div>
		                        <div class="blbox_4">
		                            <span class="fzm fcc">75%</span>
		                        </div>
		                    </div>
		                    <div class="blbox_2" onclick="sellblfc(4,100);">
		                        <div class="blbox_3 bgf5" id="sellbl_4"></div>
		                        <div class="blbox_4">
		                            <span class="fzm fcc">100%</span>
		                        </div>
		                    </div>
		                </div>
		                
		                <div style="width:100%;height:30px;margin-top:20px;">
		                    <div style="width:30%;height:30px;line-height:30px;float:left;">
		                        <span class="fzm fcc">{:L('可用')}</span>
		                    </div>
		                    <div style="width:70%;height:30px;line-height:30px;float:left;text-align:right;">
		                        <span class="fzm fcc">{$coin_blance}</span>
		                        <span class="fzm fcc">{$symbol}</span>
		                    </div>
		                </div>
		                
		                <div class="inputbox" id="sellxjusdt">
		                    <div class="inputbox_float">
		                        <input type="text" id="sell_usdt" oninput="sellusdtfc();" class="fcc xjinput" value="" placeholder="{:L('交易额')}" autocomplete="off" step="1"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,6})?/) ? this.value.match(/\d+(\.\d{0,6})?/)[0] : ''" />
		                    </div>
		                    <div class="inputbox_float" style="width:40%;text-align:center;line-height:36px;">
		                        <span class="fzmm fcc">USDT</span>
		                    </div>
		                </div>
		                
		                <div class="inputbox" id="sellxjcoin" style="display:none;">
		                    <div class="inputbox_float">
		                        <input type="text" id="sell_coin" oninput="sellcoinfc();" class="fcc xjinput" value="" placeholder="{:L('交易额')}"  autocomplete="off" step="1"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,6})?/) ? this.value.match(/\d+(\.\d{0,6})?/)[0] : ''" />
		                    </div>
		                    <div class="inputbox_float" style="width:40%;text-align:center;line-height:36px;">
		                        <span class="fzmm fcc">{$symbol}</span>
		                    </div>
		                </div>
		                
		                
		                <if condition="$uid egt 1">
		                <div  onclick="bb_sellcoin(2);" style="width:100%;height:36px;line-height:36px;background:#f5465c;text-align:center;color:#fff;border-radius:5px;margin-top:20px;">
		                    <span class="fzmm" style="color:#fff;">{:L('卖出')}</span>
		                </div>
		                <else />
		                <a href="{:U('Login/index')}">
		                <div style="width:100%;height:36px;line-height:36px;background:#f5465c;text-align:center;color:#fff;border-radius:5px;margin-top:20px;">
		                    <span class="fzmm" style="color:#fff;">{:L('登陆')}</span>
		                </div>
		                </a>
		                </if>
		            
		            </div>
 
		        </div>
		        
		        <div class="tradebox_r">
		            <div style="width:100%;height:20px;padding:0px 5px;">
		                <div class="tradebox_title tl">
		                    <span class="fcc fzm">{:L('价格')}</span>
		                </div>
		                <div class="tradebox_title tr">
		                    <span class="fcc fzm">{:L('数量')}</span>
		                </div>
		            </div>
		            
		            <div class="trade_listbox" id="tradesellbox">
		                <div style="width:100%;height:30px;">
		                    <div class="trade_list tl">
		                        <span class="red fzm">--</span>
		                    </div>
		                    <div class="trade_list tr">
		                        <span class="red fzm">--</span>
		                    </div>
		                </div>
		            </div>
		            <div class="trade_listpricebox" id="closeprice">
		                <span>--</span>
		            </div>
		            <div class="trade_listbox" id="tradebuybox">
		                <div style="width:100%;height:30px;">
		                    <div class="trade_list tl">
		                        <span class="green fzm">--</span>
		                    </div>
		                    <div class="trade_list tr">
		                        <span class="green fzm">--</span>
		                    </div>
		                </div>
		            </div>
		        </div>
		    </div>
		    
		    
		</div>
		
		<div class="fccbox"></div>
		
		
		<div class="wtlistbox">
		    <div class="o_title_box fl">
		        <span class="fzmmm order_title fe6">{:L('限价委托')}</span>
		    </div>
		    
		    
		    <div class="o_title_box fr tr">
		        <a href="{:U('Trade/tradebill')}">
					<svg t="1655970093813"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6292" width="18" height="18"><path d="M170.666667 213.333333h682.666666v85.333334H170.666667V213.333333z m0 512h426.666666v85.333334H170.666667v-85.333334z m0-256h682.666666v85.333334H170.666667v-85.333334z" fill="#e6e6e6" p-id="6293"></path></svg>
		        <span class="fzmm fcc"></span>
		        </a>
		    </div>
		    
		    <empty name="list">
		    <div style="width:100%;height:200px;text-align:center;">
				<div class="noordersvg">
					<svg t="1655969877056"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5427" width="48" height="48"><path d="M682.666667 245.333333a10.666667 10.666667 0 0 0 10.666666 10.666667h189.913334c-0.913333-1.066667-1.86-2.12-2.866667-3.126667L685.793333 58.286667c-1.006667-1.006667-2.06-1.953333-3.126666-2.866667z" fill="#404656" p-id="5428"></path><path d="M640 245.333333V42.666667H181.333333a53.393333 53.393333 0 0 0-53.333333 53.333333v832a53.393333 53.393333 0 0 0 53.333333 53.333333h661.333334a53.393333 53.393333 0 0 0 53.333333-53.333333V298.666667h-202.666667a53.393333 53.393333 0 0 1-53.333333-53.333334z m-320 10.666667h170.666667a21.333333 21.333333 0 0 1 0 42.666667H320a21.333333 21.333333 0 0 1 0-42.666667z m384 512H320a21.333333 21.333333 0 0 1 0-42.666667h384a21.333333 21.333333 0 0 1 0 42.666667z m21.333333-234.666667a21.333333 21.333333 0 0 1-21.333333 21.333334H320a21.333333 21.333333 0 0 1 0-42.666667h384a21.333333 21.333333 0 0 1 21.333333 21.333333z" fill="#404656" p-id="5429"></path></svg>
					<br />
					<span class="fcc fzmmm">{:L('没有委托订单')}</span>
				</div>
		    </div>
		    <else />
		    <foreach name="list" item="vo">
		    <div class="tlistbox">
		        <div class="tlistbox_1">
		            <div class="tlistbox_2">
		                <div class="tlistbox_3 fl">
		                    <if condition="$vo.type eq 1">
		                    <span class="fzmmm green">{:L('购买')}</span>
		                    <elseif condition="$vo.type eq 2" />
		                    <span class="fzmmm red">{:L('出售')}</span>    
		                    </if>
		                    <span class="fzmmm fe6im" style="font-weight:bold;">{$vo.symbol}</span>
		                    <span class="fzm fcc">{$vo.addtime}</span>
		                </div>
		                
		                <div class="tlistbox_4 fr tr" onclick="clearorder({$vo.id});">
		                    <span class="tlistbox_5 fcy">{:L('取消')}</span>
		                </div>
		            </div>
		            <div class="tlistbox_6">
		                <div class="tlistbox_7 fl">
		                    <div class="tlistbox_8 tl">
		                        <span class="fzm fcc">{:L('价格')}(USDT)</span>
		                    </div>
		                    <div class="tlistbox_9 tl">
		                        <span class="fzmm fcc">{$vo.xjprice}</span>
		                    </div>
		                </div>
		                <if condition="$vo.type eq 1">
		                <div class="tlistbox_7 fl">
		                    <div class="tlistbox_8 tc">
		                        <span class="fzm fcc">{:L('数量')}(USDT)</span>
		                    </div>
		                    <div class="tlistbox_9 tc">
		                        <span class="fzmm fcc">{$vo.usdtnum}</span>
		                    </div>
		                </div>
		                <elseif condition="$vo.type eq 2" />
		                <div class="tlistbox_7 fl">
		                    <div class="tlistbox_8 tc">
		                        <span class="fzm fcc">{:L('数量')}({$vo.coin})</span>
		                    </div>
		                    <div class="tlistbox_9 tc">
		                        <span class="fzmm fcc">{$vo.coinnum}</span>
		                    </div>
		                </div>
		                </if>
		                
		                <div class="tlistbox_7 fl">
		                    <div class="tlistbox_8 tr">
		                        <span class="fzm fcc">{:L('实际成交')}({$vo.coin})</span>
		                    </div>
		                    <div class="tlistbox_9 tr">
		                        <span class="fzmm fcc">{$vo.coinnum}</span>
		                    </div>
		                </div>
		            </div>
		        </div>
		    </div>
		    </foreach>
		    </empty>
		</div>
		
		<div style="width:100%;height:120px;"></div>
		


	</div>
	
	
		<!---------交易选择弹窗---------->
	<div class="dongbox"  id="showdong" style="display:none;">
	    <div class="dong_con">
	        <div style="width:100%;position:relative;z-index:9999999;top:0px;background: #212332">
	            <div class="dong_title">
	                <span class="dong_title_span fe6" style="font-weight: bold;">{:L('全部')}</span>
	            </div>

	            <div class="dong_sel_box">

						<div class="sy_list_box">
							<div class="sy_list_boxl" style="text-align:center;">
								<span  class="fzmm fw f12 fcc"  style="color: #707A8A!important;margin-right:10px; ">{:L('交易对')}</span>
							</div>
							<div class="sy_list_boxl f12 fa8a fw" style="text-align:center;" >{:L('最新价格')}</div>
							<div class="sy_list_boxr f12 fa8a fw">{:L('24h涨跌')}</div>'+
						</div>
	            </div>
	        </div>
	        
	        
	        <div class="symbol_list" id="smybolbox">
	            <div style="width:100%;height:100px;line-height:100px;text-align:center;">
                    <span class="fzmm fcc">{:L('没有获取数据')}</span>
                </div>
	        </div>
	    </div>
	    
	    <div style="width:20%;height:100vh;float:left;" id="hidedong"></div>
	</div>


    <input type="hidden" id="buy_usermoney" value="{$usdt_blance}" />
    <input type="hidden" id="buy_usercoin" value="{$coin_blance}" />
    
    <input type="hidden" id="symbolbox" value="{$coinname}"  />
    <!--交易限价单价-->
	<input type="hidden" id="mprice" value="" />
	<!--交易限价买卖币的数量-->
	<input type="hidden" id="mnum" value="" />
	<!--交易限价买卖USDT的数量-->
	<input type="hidden" id="musdt" value="" />
	<!--购买类型，1限价2市价-->
	<input type="hidden" id="buytype" value="1" />
	<!--底部-->
	<div class="footer">

		<a href="{:U('Index/index')}">
			<div class="footer_op">
				<div class="f_op_t" style="line-height: 35px;" >
					<?php
					if($select == 'index') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-1-active.png" style="width: 25px;" >';
					}else{
					echo '<img src="/Public/Static/Icoinfont/icon/nav-1.png" style="width: 25px;" >';
					}
					?>
				</div>
				<div class="f_op_b">
					<span class="fzm fcch">{:L('首页')}</span>
				</div>
			</div>
		</a>

		<a href="{:U('Trade/index')}">
			<div class="footer_op">
				<div class="f_op_t" style="line-height:35px;">
					<?php
					if($select == 'trade') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-2-active.png" style="width: 25px;" >';
					}else{
					echo '<img src="/Public/Static/Icoinfont/icon/nav-2.png" style="width: 25px;" >';
					}
					?>
				</div>
				<div class="f_op_b">
					<span class="fzm fcch">{:L('行情')}</span>
				</div>
			</div>
		</a>

		<a href="/Trade/transinfo.html?symbol=BTC&type=buy">
			<div class="footer_op">
				<div class="f_op_t" style="line-height:35px;">
					<?php
					if($select == 'trans') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-3-active.png" style="width: 25px;" >';
					}else{
					echo '<img src="/Public/Static/Icoinfont/icon/nav-3.png" style="width: 25px;" >';
					}
					?>
				</div>
				<div class="f_op_b">
					<span class="fzm fcy">{:L('交易')}</span>
				</div>
			</div>
		</a>

		<a href="{:U('Contract/index')}">
			<div class="footer_op">
				<div class="f_op_t" style="line-height:35px;">
					<?php
					if($select == 'contract') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-4-active.png" style="width: 25px;" >';
					}else{
					echo '<img src="/Public/Static/Icoinfont/icon/nav-4.png" style="width: 25px;" >';
					}
					?>
				</div>
				<div class="f_op_b">
					<span class="fzm fcch">{:L('合约')}</span>
				</div>
			</div>
		</a>

		<a href="{:U('User/index')}">
			<div class="footer_op">
				<div class="f_op_t" style="line-height:35px;">
					<?php
					if($select == 'user') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-5-active.png" style="width: 25px;" >';
					}else{
					echo '<img src="/Public/Static/Icoinfont/icon/nav-5.png" style="width: 25px;" >';
					}
					?>
				</div>
				<div class="f_op_b">
					<span class="fzm fcch">{:L('资产')}</span>
				</div>
			</div>
		</a>


	</div>


</body>

<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
<script type="text/javascript" src="/Public/Static/js/layer/layer.js" ></script>
<script type="text/javascript">
    function clearorder(id){
        var oid = id;
        $.post("{:U('Trade/clearorder')}",
        {"oid":oid},
        function(data){
            if(data.code == 1){
                layer.msg(data.info);
                setTimeout(function(args){
                    window.location.reload();
                },2000);
            }else{
                layer.msg(data.info);return false;
            }    
        });
    }
</script>
<script type="text/javascript">
    function bb_sellcoin(type){
        var type = type;
        if(type <= 0){
            layer.msg("{:L('缺少重要参数')}");return false;
        }
        var symbol = $("#symbolbox").val();
        var mprice = $("#sell_newprice").val();
        var musdt = $("#musdt").val();
        var selltype = $("#buytype").val();
        if(selltype == 1){
            var mnum = $("#sell_num").val();
            if(mprice < 0){
                layer.msg("{:L('缺少重要参数')}");return false;
            }
            if(mnum <= 0){
                layer.msg("{:L('请输入出售数量')}");return false;
            }
        }else if(selltype == 2){
            var mnum = $("#sell_coin").val();
            if(mnum <= 0){
                layer.msg("{:L('请输入出售数量')}");return false;
            }
        }
        $.post("{:U('Trade/upbbsell')}",
        {'symbol':symbol,'mprice':mprice,'mnum':mnum,'musdt':musdt,'selltype':selltype,'type':type},
        function(data){
            if(data.code == 1){
                layer.msg(data.info);
                setTimeout(function(args){
                    window.location.reload();
                },2000);
            }else{
                layer.msg(data.info);return false; 
            }
        });
    }
</script>
<script type="text/javascript">
    function bb_buycoin(type){
        var type = type;
        if(type <= 0){
            layer.msg("{:L('缺少重要参数')}");return false;
        }
        var symbol = $("#symbolbox").val();
        var mprice = $("#newprice").val();
        var mnum = $("#mnum").val();
        var musdt = $("#musdt").val();
        var buytype = $("#buytype").val();
        if(buytype == 1){
            if(mnum <= 0){
                layer.msg("{:L('输入数量')}");return false;
            }
        }else if(buytype == 2){
            if(musdt <= 0){
                layer.msg("{:L('输入USDT数量')}");return false;
            }
        }
        $.post("{:U('Trade/upbbbuy')}",
        {'symbol':symbol,'mprice':mprice,'mnum':mnum,'musdt':musdt,'buytype':buytype,'type':type},
        function(data){
            if(data.code == 1){
                layer.msg(data.info);
                setTimeout(function(args){
                    window.location.reload();
                },2000);
            }else{
               layer.msg(data.info);return false; 
            }
        });
    }
</script>
<script type="text/javascript">
    function getallsmybol(){
        $.post("{:U('Ajaxtrade/getallcoin')}",
        function(data){
            if(data.code == 1){
                $("#smybolbox").empty();
                var html = '';
                if(data.data == '' || data.data == null){
                    html = '<div style="width:100%;height:100px;line-height:100px;text-align:center;">'+ 
                           '<span class="fzmm fcc">' + "{:L('没有获取数据')}" + '</span>'+
                           '</div>';
                    $("#smybolbox").append(html);
                    
                }else{
                    $.each(data.data,function(key,val){
                        html += '<a href="/Trade/transinfo.html?symbol='+ val.coin +'&type=buy">'+
                                '<div class="sy_list_box">'+
                                '<div class="sy_list_boxl">'+
                                '<span  class="fzmm fw fe6 fcc">'+ val.cname +'</span>'+
                                '</div>'+
                                '<div class="sy_list_boxl fe6 fw" style="text-align:center;">' + val.open + '</div>'+
                                '<div class="sy_list_boxr  fw">' + val.change +'</div>'+
                                '</div>'+
                                '</a>';
                    });
                    
                    $("#smybolbox").append(html);
                }
            }else{
                html =  '<div style="width:100%;height:100px;line-height:100px;text-align:center;">'+ 
                        '<span class="fzmm fcc">' + "{:L('没有获取数据')}" + '</span>'+
                        '</div>';
                $("#smybolbox").append(html);
            }
        });
    }
</script>
<script type="text/javascript">
    $("#hidedong").click(function(){
        $("#showdong").fadeOut("slow");
    });
    $("#centerbox").click(function(){
        getallsmybol()
        $("#showdong").fadeIn("slow");
        
        
    });
</script>
<script type="text/javascript">
    $(function(){
        gettradbuy();
        gettradsell();
        setInterval("gettradbuy()",2000);
        setInterval("gettradsell()",2000);
    });
</script>
<script type="text/javascript">
    function gettradsell(){
        var symbol = $("#symbolbox").val();
        $.post("{:U('Ajaxtrade/gettradsell')}",
        {'symbol':symbol},
        function(data){
            if(data.code == 1){
                $("#tradebuybox").empty();
                var html = '';
                if(data.data == '' || data.data == null){
                    html = '<div style="width:100%;height:100px;line-height:100px;text-align:center;">'+ 
                           '<span class="fzmm fcc">' + "{:L('没有获取数据')}" + '</span>'+
                           '</div>';
                    $("#tradebuybox").append(html);
                    
                }else{
                    $.each(data.data,function(key,val){
                        html += '<div style="width:100%;height:30px;">'+
		                        '<div class="trade_list tl">'+
		                        '<span class="red fzm">'+ val.price +'</span>'+
		                        '</div>'+
		                        '<div class="trade_list tr">'+
		                        '<span class="red fzm">'+ val.amount +'</span>'+
		                        '</div>'+
		                        '</div>';
                    });
                    $("#tradebuybox").append(html);
                }
            }
        });
    }
</script>
<script type="text/javascript">
    function gettradbuy(){
        var symbol = $("#symbolbox").val();
        $.post("{:U('Ajaxtrade/gettradbuy')}",
        {'symbol':symbol},
        function(data){
            if(data.code == 1){
                $("#tradesellbox").empty();
                var html = '';
                if(data.data == '' || data.data == null){
                    html = '<div style="width:100%;height:100px;line-height:100px;text-align:center;">'+ 
                           '<span class="fzmm fcc">' + "{:L('没有获取数据')}" + '</span>'+
                           '</div>';
                    $("#tradesellbox").append(html);
                    
                }else{
                    $.each(data.data,function(key,val){
                        html += '<div style="width:100%;height:30px;">'+
		                        '<div class="trade_list tl">'+
		                        '<span class="green fzm">'+ val.price +'</span>'+
		                        '</div>'+
		                        '<div class="trade_list tr">'+
		                        '<span class="green fzm">'+ val.amount +'</span>'+
		                        '</div>'+
		                        '</div>';
                    });
                    $("#tradesellbox").append(html);
                }
            }
        });
    }
</script>
<script type="text/javascript">
    $(function(){
        getcoinprice();
        setInterval("getcoinprice()",2000);
    });
</script>
<script type="text/javascript">
    function getcoinprice(){
        var symbol = $("#symbolbox").val();
        $.post("{:U('Ajaxtrade/getcoinprice')}",
        {'symbol':symbol},
        function(data){
            if(data.code == 1){
                $("#changebox").html(data.change);
                $("#closeprice").html(data.price);
            }else{
                console.log("{:L('未获取数据')}");
            }
        });
    }
</script>
<script type="text/javascript">
    $("#buybtn").click(function(){
        $("#buybtn").addClass("bggreen");
        $("#buybtn").removeClass("bghc");
        $("#sellbtn").addClass("bghc");
        $("#sellbtn").removeClass("bggreen");
        $("#buycoinbox").show();
        $("#sellcoinbox").hide();
        $("#buybtnspan").addClass("cfff");
        $("#buybtnspan").removeClass("c000");
        $("#sellbtnspan").addClass("c000");
        $("#sellbtnspan").removeClass("cfff");
    });
    $("#sellbtn").click(function(){
        $("#buybtn").removeClass("bgred");
        $("#buybtn").addClass("bghc");
        $("#sellbtn").removeClass("bghc");
        $("#sellbtn").addClass("bgred");
        $("#buycoinbox").hide();
        $("#sellcoinbox").show();
        $("#buybtnspan").addClass("c000");
        $("#buybtnspan").removeClass("cfff");
        $("#sellbtnspan").addClass("cfff");
        $("#sellbtnspan").removeClass("c000");
    });
</script>
<script type="text/javascript">
    //出售框
    $("#sell_xjbtn").click(function(){
        $("#sell_xjspan").addClass("fcy");
        $("#sell_xjspan").removeClass("fcc");
        $("#sell_sjspan").addClass("fcc");
        $("#sell_sjspan").removeClass("fcy");
        $("#sell_xjbox").show();
        $("#sell_sjbox").hide();
        $("#buytype").val(1);
        $("#sell_sjnumbox").hide();
        $("#sell_xjnumbox").show();
        $("#sellxjusdt").show();
        $("#sellxjcoin").hide();
        
        
    });
    $("#sell_sjbtn").click(function(){
        $("#sell_sjspan").addClass("fcy");
        $("#sell_sjspan").removeClass("fcc");
        $("#sell_xjspan").addClass("fcc");
        $("#sell_xjspan").removeClass("fcy");
        $("#sell_sjbox").show();
        $("#sell_xjbox").hide();
        $("#buytype").val(2);
        $("#sell_xjnumbox").hide();
        $("#sell_sjnumbox").show();
        $("#sellxjusdt").hide();
        $("#sellxjcoin").show();
    });

    //买入框
    $("#buyxjbtn").click(function(){
        $("#xjspan").addClass("fcy");
        $("#xjspan").removeClass("fcc");
        $("#sjspan").addClass("fcc");
        $("#sjspan").removeClass("fcy");
        $("#xjbox").show();
        $("#sjbox").hide();
        $("#buytype").val(1);
        $("#sjnumbox").hide();
        $("#xjnumbox").show();
    });
    $("#buysjbtn").click(function(){
        $("#sjspan").addClass("fcy");
        $("#sjspan").removeClass("fcc");
        $("#xjspan").addClass("fcc");
        $("#xjspan").removeClass("fcy");
        $("#sjbox").show();
        $("#xjbox").hide();
        $("#buytype").val(2);
        $("#xjnumbox").hide();
        $("#sjnumbox").show();
    });
</script>
<script type="text/javascript">
    function sellusdtfc(){
        var newprice = parseFloat($("#newprice").val()); //单价
        var buyusdt = parseFloat($("#sell_usdt").val()); //输入的USDT数量
        var usermoney = parseFloat($("#buy_usercoin").val());//币的数量
        var coinnum = parseFloat(buyusdt / newprice).toFixed(4);
        
        if(coinnum > usermoney){
            layer.msg("{:L('余额不足')}");return false;
        }else{
            $("#sell_num").val(coinnum);
            $("#musdt").val(buyusdt);
        }
    }
    function buyusdtfc(){
        var newprice = parseFloat($("#newprice").val());
        var buyusdt = parseFloat($("#buyusdt").val());
        var usermoney = parseFloat($("#buy_usermoney").val());
        var num = parseFloat(buyusdt / newprice).toFixed(4);
        if(buyusdt > usermoney){
            layer.msg("{:L('余额不足')}");return false;
        }else{
            var buynum = parseFloat(buyusdt / newprice).toFixed(4);
            $("#buynum").val(buynum);
            $("#musdt").val(buyusdt);
            $("#mnum").val(num);
        }
    }
</script>
<script type="text/javascript">
    function sellnumfc(){
        var newbuynum = $("#sell_num").val();
        var newprice = $("#sell_newprice").val();
        var buyusdt = parseFloat((newprice * newbuynum)).toFixed(4);
        var usermoney = parseFloat($("#buy_usercoin").val());
        if(newbuynum > usermoney){
            layer.msg("{:L('余额不足')}");return false;
        }else{
            $("#sell_usdt").val(buyusdt);
            $("#mnum").val(newbuynum);
            $("#musdt").val(buyusdt);
        }
        
    }
    function buynumfc(){
        var newbuynum = $("#buynum").val();
        var newprice = $("#newprice").val();
        var buyusdt = parseFloat((newprice * newbuynum)).toFixed(4);
        var usermoney = parseFloat($("#buy_usermoney").val());
        if(buyusdt > usermoney){
            layer.msg("{:L('余额不足')}");return false;
        }else{
            $("#buyusdt").val(buyusdt);
            $("#mnum").val(newbuynum);
            $("#musdt").val(buyusdt);
        }
        
    }
</script>
<script type="text/javascript">
    $(function(){
        var symbol = $("#symbolbox").val();
        $.post("{:U('Ajaxtrade/getnewprice')}",
        {'symbol':symbol},
        function(data){
            if(data.code == 1){
                $("#newprice").val(data.price);
                $("#mprice").val(data.price);
                $("#sell_newprice").val(data.price);
            }else{
                console.log("{:L('未获取数据')}");
            }
        });
    });
</script>
<script type="text/javascript">
    function sellcoinfc(){
        var sell_coin = parseFloat($("#sell_coin").val());
        if(sell_coin < 0){
            layer.msg("{:L('请输入正确数量')}");return false;
        }
        var buy_usercoin = parseFloat($("#buy_usercoin").val());
        if(sell_coin > buy_usercoin){
            layer.msg("{:L('余额不足')}");return false;
        }
    }
    
    
    
    function sellblfc(type,num){
        var type = type;
        var num = num;
        var usermoney = $("#buy_usercoin").val();
        var musdt = parseFloat((usermoney * num / 100)).toFixed(4);
        var newprice = $("#newprice").val();
        var buynum = parseFloat((musdt * newprice)).toFixed(4);
        
        $("#sell_usdt").val(buynum);
        $("#sell_num").val(musdt);
        
        $("#musdt").val(buynum);
        $("#mnum").val(musdt);
        $("#sell_coin").val(musdt);
        
        
        if(type == 1){
            $('#sellbl_1').addClass("bgred");
            $('#sellbl_1').removeClass("bgf5");
            $('#sellbl_2').addClass("bgf5");
            $('#sellbl_2').removeClass("bgred");
            $('#sellbl_3').addClass("bgf5");
            $('#sellbl_3').removeClass("bgred");
            $('#sellbl_4').addClass("bgf5");
            $('#sellbl_4').removeClass("bgred");
            
        }else if(type == 2){
            $('#sellbl_2').addClass("bgred");
            $('#sellbl_2').removeClass("bgf5");
            $('#sellbl_1').addClass("bgf5");
            $('#sellbl_1').removeClass("bgred");
            $('#sellbl_3').addClass("bgf5");
            $('#sellbl_3').removeClass("bgred");
            $('#sellbl_4').addClass("bgf5");
            $('#sellbl_4').removeClass("bgred");
        }else if(type == 3){
            $('#sellbl_3').addClass("bgred");
            $('#sellbl_3').removeClass("bgf5");
            $('#sellbl_1').addClass("bgf5");
            $('#sellbl_1').removeClass("bgred");
            $('#sellbl_2').addClass("bgf5");
            $('#sellbl_2').removeClass("bgred");
            $('#sellbl_4').addClass("bgf5");
            $('#sellbl_4').removeClass("bgred");
        }else if(type == 4){
            $('#sellbl_4').addClass("bgred");
            $('#sellbl_4').removeClass("bgf5");
            $('#sellbl_1').addClass("bgf5");
            $('#sellbl_1').removeClass("bgred");
            $('#sellbl_2').addClass("bgf5");
            $('#sellbl_2').removeClass("bgred");
            $('#sellbl_3').addClass("bgf5");
            $('#sellbl_3').removeClass("bgred");
        }
    }
    function buyblfc(type,num){
        var type = type;
        var num = num;
        var usermoney = $("#buy_usermoney").val();
        var musdt = parseFloat((usermoney * num / 100)).toFixed(4);
        var newprice = $("#newprice").val();
        var buynum = parseFloat((musdt / newprice)).toFixed(4);
        $("#buyusdt").val(musdt);
        $("#buynum").val(buynum);
        $("#musdt").val(musdt);
        $("#mnum").val(buynum);
        if(type == 1){
            $('#buybl_1').addClass("bggreen");
            $('#buybl_1').removeClass("bgf5");
            $('#buybl_2').addClass("bgf5");
            $('#buybl_2').removeClass("bggreen");
            $('#buybl_3').addClass("bgf5");
            $('#buybl_3').removeClass("bggreen");
            $('#buybl_4').addClass("bgf5");
            $('#buybl_4').removeClass("bggreen");
            
        }else if(type == 2){
            $('#buybl_2').addClass("bggreen");
            $('#buybl_2').removeClass("bgf5");
            $('#buybl_1').addClass("bgf5");
            $('#buybl_1').removeClass("bggreen");
            $('#buybl_3').addClass("bgf5");
            $('#buybl_3').removeClass("bggreen");
            $('#buybl_4').addClass("bgf5");
            $('#buybl_4').removeClass("bggreen");
        }else if(type == 3){
            $('#buybl_3').addClass("bggreen");
            $('#buybl_3').removeClass("bgf5");
            $('#buybl_1').addClass("bgf5");
            $('#buybl_1').removeClass("bggreen");
            $('#buybl_2').addClass("bgf5");
            $('#buybl_2').removeClass("bggreen");
            $('#buybl_4').addClass("bgf5");
            $('#buybl_4').removeClass("bggreen");
        }else if(type == 4){
            $('#buybl_4').addClass("bggreen");
            $('#buybl_4').removeClass("bgf5");
            $('#buybl_1').addClass("bgf5");
            $('#buybl_1').removeClass("bggreen");
            $('#buybl_2').addClass("bgf5");
            $('#buybl_2').removeClass("bggreen");
            $('#buybl_3').addClass("bgf5");
            $('#buybl_3').removeClass("bggreen");
        }
    }
</script>
<script type="text/javascript">
    
    $("#dash_sellprice").click(function(){
        var newprice =  parseFloat($("#sell_newprice").val());
        if(newprice > 0){
            var buyprice = (newprice - 0.001).toFixed(4);
        }else{
            var buyprice = 0;
        }
        $("#sell_newprice").val(buyprice);
        $("#mprice").val(buyprice);
    });
    
    $("#plus_sellprice").click(function(){
        var newprice = parseFloat($("#sell_newprice").val());
        var buyprice = (newprice + 0.001).toFixed(4);
        $("#sell_newprice").val(buyprice);
        $("#mprice").val(buyprice);
    });
    
    $("#dash_sellnum").click(function(){
        var buynum = parseFloat($("#sell_num").val());
        if(buynum > 0){
            var newbuynum = (buynum - 0.001).toFixed(4);
        }else{
            newbuynum = 0
        }
        $("#sell_num").val(newbuynum);
        $("#mnum").val(newbuynum);
        var newprice = $("#newprice").val();
        var buyusdt = (newprice * newbuynum).toFixed(4);
        $("#sell_usdt").val(buyusdt);
        $("#musdt").val(buyusdt);
        
    });
    $("#plus_sellnum").click(function(){
        var buynum = parseFloat($("#sell_num").val());
        if(buynum > 0){
            var newbuynum = (buynum + 0.001).toFixed(4);
        }else{
            var newbuynum = 0.01;
        }
        $("#sell_num").val(newbuynum);
        $("#mnum").val(newbuynum);
        var newprice = $("#newprice").val();
        var usermoney = parseFloat($("#buy_usercoin").val());
        var buyusdt = parseFloat((newprice * newbuynum)).toFixed(4);
        if(newbuynum > usermoney){
            layer.msg("{:L('余额不足')}");return false;
        }else{
            $("#sell_usdt").val(buyusdt);
            $("#musdt").val(buyusdt);
        }
    });

    
    $("#dash_buyprice").click(function(){
        var newprice =  parseFloat($("#newprice").val());
        if(newprice > 0){
            var buyprice = (newprice - 0.001).toFixed(4);
        }else{
            var buyprice = 0;
        }
        $("#newprice").val(buyprice);
        $("#mprice").val(buyprice);
    });
    $("#plus_buyprice").click(function(){
        var newprice = parseFloat($("#newprice").val());
        var buyprice = (newprice + 0.001).toFixed(4);
        $("#newprice").val(buyprice);
        $("#mprice").val(buyprice);
    });
    $("#dash_buynum").click(function(){
        var buynum = parseFloat($("#buynum").val());
        if(buynum > 0){
            var newbuynum = (buynum - 0.001).toFixed(4);
        }else{
            newbuynum = 0
        }
        $("#buynum").val(newbuynum);
        $("#mnum").val(newbuynum);
        var newprice = $("#newprice").val();
        var buyusdt = (newprice * newbuynum).toFixed(4);
        $("#buyusdt").val(buyusdt);
        $("#musdt").val(buyusdt);
        
    });
    $("#plus_buynum").click(function(){
        var buynum = parseFloat($("#buynum").val());
        if(buynum > 0){
            var newbuynum = (buynum + 0.001).toFixed(4);
        }else{
            var newbuynum = 0.01;
        }
        $("#buynum").val(newbuynum);
        $("#mnum").val(newbuynum);
        var newprice = $("#newprice").val();
        var usermoney = parseFloat($("#buy_usermoney").val());
        var buyusdt = parseFloat((newprice * newbuynum)).toFixed(4);
        if(buyusdt > usermoney){
            layer.msg("{:L('余额不足')}");return false;
        }else{
            $("#buyusdt").val(buyusdt);
            $("#musdt").val(buyusdt);
        }
    });
</script>
<script type="text/javascript">
    function goback(){
        window.history.go(-1);
    }
</script>

</html>



