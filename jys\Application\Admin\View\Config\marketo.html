<include file="Public:header"/>
<div id="main-content">
    <div id="top-alert" class="fixed alert alert-error" style="display: none;">
        <button class="close fixed" style="margin-top: 4px;">&times;</button>
        <div class="alert-content">警告内容</div>
    </div>
    <div id="main" class="main">
        <div class="main-title-h">
            <span class="h1-title">交易市场(平台机器人产生的交易数据)</span>
        </div>

        <div class="cf">
            <div class="fl">
                <a class="btn btn-success" href="{:U('Config/marketoEdit')}">新 增</a>
                <button class="btn ajax-post btn-info" url="{:U('Config/marketoStatus',array('type'=>'resume'))}" target-form="ids">启 用
                </button>
                <button class="btn ajax-post btn-warning" url="{:U('Config/marketoStatus',array('type'=>'forbid'))}" target-form="ids">禁 用
                </button>
                <button class="btn ajax-post confirm btn-danger" url="{:U('Config/marketoStatus',array('type'=>'del'))}" target-form="ids">删 除
                </button>
            </div>
            <div class="search-form fr cf">
                <div class="sleft">
                    <form name="formSearch" id="formSearch" method="get" name="form1" >
                        <select style=" width: 160px; float: left; margin-right: 10px;" name="field" class="form-control">
                            <option value="name" <eq name="Think.get.field" value="name">selected</eq>>市场名</option>
                        </select>
                        <input type="text" name="name" class="search-input form-control" value="{$Think.get.name}" placeholder="请输入查询内容" style="">
                        <a class="sch-btn" href="javascript:;" id="search"> <i class="btn-search"></i> </a>
                    </form>
                    <script>
                        //搜索功能
                        $(function () {
                            $('#search').click(function () {
                                $('#formSearch').submit();
                            });
                        });
                        //回车搜索
                        $(".search-input").keyup(function (e) {
                            if (e.keyCode === 13) {
                                $("#search").click();
                                return false;
                            }
                        });
                    </script>
                </div>
            </div>
        </div>
        <div class="data-table table-striped">
            <table class="">
                <thead>
					<tr>
						<th class="row-selected row-selected"><input class="check-all" type="checkbox"/></th>
						<th class="">ID</th>
						<th class="">市场名称</th>
						<th class="">小数位数</th>
						<th class="">机器人交易</th>
						<th class="">开启交易</th>
						<th class="">状态</th>
						<th class="" style="text-align: center;">操作</th>
					</tr>
                </thead>
                <tbody>
                <notempty name="list">
                    <volist name="list" id="vo">
                        <tr>
                            <td><input class="ids" type="checkbox" name="id[]" value="{$vo.id}"/></td>
                            <td>{$vo.id}</td>
                            <td>{$vo.name}</td>
                            <td>{$vo.round}</td>
                            <td>
                                <eq name="vo.shuadan" value="1"><b style="color: #028E16;">已开启</b><else/><b style="color: #F70408">未启动</b></eq>
                            </td>
                            <td>
                                <eq name="vo.trade" value="1"><b style="color: #028E16;">开启交易</b><else/><b style="color: #F70408">禁止交易</b></eq>
                            </td>
                            <td>
                                <eq name="vo.status" value="1"><b style="color: #028E16;">可用</b><else/><b style="color: #F70408">禁用</b></eq>
                            </td>

                            <td style="text-align: center;">
								<a href="{:U('Config/marketoEdit?id='.$vo['id'])}" class="btn btn-primary btn-xs">编辑</a>
								<a href="{:U('Config/marketoEdit2?id='.$vo['id'])}" class="btn btn-primary btn-xs">行情</a>
								<a href="{:U('Config/marketoEdit3?id='.$vo['id'])}" class="btn btn-primary btn-xs">机器人</a>
                            </td>
                        </tr>
                    </volist>
                    <else/>
                    <td colspan="12" class="text-center empty-info"><i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据</td>
                </notempty>
                </tbody>
            </table>
            <div class="page">
                <div>{$page}</div>
            </div>
        </div>
    </div>
</div>
<include file="Public:footer"/>
<block name="script">
    <script type="text/javascript" charset="utf-8">
        //导航高亮
        highlight_subnav("{:U('Config/marketo')}");
    </script>
</block>