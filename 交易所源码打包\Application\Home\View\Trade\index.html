<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width,minimum-scale=1,maximum-scale=1.0,initial-scale=1,user-scalable=no,viewport-fit=true" data-shuvi-head="true">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
	    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/base.css" />
	    <title>{$webname}</title>
	    <style>

			.table td, .table th {
				padding: 0.75rem;
				vertical-align: top;
				border-top: 1px solid #2c2d2e;
			}

			.xjinput {
				color: #fff !important;
			}
            .css-1wr4jig {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
            }
            .css-wp2li4 {
               box-sizing: border-box;
               margin: 0;
               min-width: 0;
               display: -webkit-box;
               display: -webkit-flex;
               display: -ms-flexbox;
               display: flex;
               overflow-x: hidden;
               -webkit-flex-direction: column;
               -ms-flex-direction: column;
               flex-direction: column;
            }
            .klinetitle{width:100%;height:48px;background:#1a1b1c;padding:0px 20px;}
	        .klinetitle_l{width:20%;height:50px;float:left;}
	        .klinetitle_r{width:60%;height:50px;float:left;}
			.newpricebox{height:28px;line-height:30px;margin-bottom:0px;color:#2ebd85;font-size:14px;text-align: left;}
			.changebox{height:20px;line-height:20px;margin-bottom:0px;color:#2ebd85;text-align: left;}
	        .klr3_box{width:100%;height:30px;}
	        .klr2_box{width:100%;height:25px;}
	        .klr3_box_dl{width:45%;height:30px;line-height:40px;float:left;}
	        .changebox f14 tccs{width:55%;height:30px;line-height:40px;}
	        .newpricebox f14 tccs{width:45%;height:25px;line-height:25px;float:left;text-align:left;}
	        .changebox f14 tccs{width:55%;height:25px;line-height:25px;float:right;text-align:right;}
	        .dong_con{width:80%;height:100vh;background:#1a1b1c;margin-top:0px;border-top-right-radius:0px;border-bottom-right-radius:0px;padding:20px 15px 10px 20px;float:left;}
	        .dong_title{width:100%;height:40px;line-height:40px;}
	        .dong_title_span{font-size:18px;font-weight:500;}
	        .dong_find_box{width:100%;height:30px;background:#f5f5f5;border-radius:10px;}
	        .dong_find_box_img{width:20%;height:30px;line-height:30px;float:left;text-align:center;}
	        .dong_input_box{width:80%;height:30px;float:left;}
	        .dong_symbox{width:90%;height:20px;border:none;background:#f5f5f5;margin-top:5px;}
	        .dong_sel_box{width:100%;height:30px;border-bottom:1px solid #151617;}
	        .dong_sel_span{display:block;width:35px;height:30px;line-height:30px;border-bottom:2px solid #3db485;font-size:14px;text-align:center;font-size:14px;}
	        .symbol_list{width:100%;height:890px;margin-top:10px;}
	        .no_header{position: fixed;z-index: 9999;padding:0px 10px;top:0px;background: #fff;}
	        .txtl{line-height:50px;width:30%;}
	        .contentbox{width:100%;background:#1a1b1c;padding:10px;}
	        .content_title{width:100%;height:40px;line-height:40px;margin-top:50px;position: fixed;z-index: 9999;top:0px;padding:0px 10px;background:#1a1b1c;border-bottom:1px solid #151617;}
	        .content_title_l{width:70%;height:40px;line-height:40px;float:left;}
	        .content_title_r{width:30%;height:40px;line-height:40px;float:right;padding:0px 5px;}
	        .tleft{text-align:left;}
	        .tright{text-align:right;}
	        .tradebox{width:100%;height:350px;background:#1a1b1c;}
	        .tradebox_l{width:58%;height:300px;float:left;}
	        .tradebox_r{width:40%;height:300px;float:right;}
	        .tradebox_l_btn{width:100%;height:36px;}
	        .tradebox_l_buybtn{width:48%;height:36px;line-height:36px;float:left;text-align:center;border-radius:5px;}
	        .tradebox_l_sellbtn{width:48%;height:36px;line-height:36px;float:right;text-align:center;border-radius:5px;}
	        .bggreen{background:#0ecb81;}
	        .green{color:#0ecb81;}
            .bgred{background:#f5465c;}
            .red{color:#f5465c;}
            .bghc{background:#f5f5f5;}
            .cfff{color:#fff;}
            .c000{color:#000;}
            .formbox{width:100%;height:350px;margin-top:15px;}
            .formbox_op{width:100%;height:30px;}
            .formbox_op_list{width:20%;height:30px;line-height:30px;float:left;text-align:center;cursor:pointer;}
            .inputbox{width:100%;height:36px;border:1px solid #707A8A;border-radius:5px;margin:10px 0px 20px;}

	        .inputbox_float{width:60%;height:36px;float:left;}

	        .xjinput{width:70%;background:#1a1b1c;border:#fff;margin-top:7px;padding:0px 10px;outline:none !important;}
	        .input_bi{width:50%;height:26px;line-height:26px;float:left;text-align:center;margin-top:5px;cursor:pointer;}
	        .borderright{border-right:1px solid #151617;}
	        .bistyle{font-size:16px;cursor: pointer;}
	        .blbox{width:100%;height:30px;margin-top:15px;}
	        .blbox_1{width:20%;height:30px;float:left;margin-right:6%;border-radius: 5px;}
	        .blbox_2{width:20%;height:30px;float:left;border-radius: 5px;}
	        .blbox_3{width:100%;height:10px;border-radius: 5px;}
	        .bgf5{background:#73797f;border-radius:5px;cursor: pointer;}
	        .blbox_4{width:100%;height:20x;line-height:20px;text-align:center;}
	        .tradebox_title{width:50%;height:20px;line-height:20px;float:left;}
	        .tl{text-align:left;}
	        .tr{text-align:right;}
	        .tc{text-align:center;}
	        .fl{float:left;}
	        .fr{float:right;}
	        .trade_listbox{width:100%;height:430px;padding:5px;overflow:hidden;}
	        .trade_listpricebox{width:100%;height:40px;line-height:40px;padding:0px 10px;}
	        .trade_list{width:50%;height:30px;line-height:30px;float:left;}
	        .dongbox{position:fixed;z-index:9999;display:none;top:0px;width:100%;height:100vh;background:rgba(0,0,0,0.2);}
	        .dong_con{width:80%;height:100vh;background:#1a1b1c;margin-top:0px;border-top-right-radius:0px;border-bottom-right-radius:0px;padding:20px 15px 10px 20px;float:left;}
	        .dong_title{width:100%;height:40px;line-height:40px;}
	        .dong_title_span{font-size:18px;font-weight:500;}
	        .dong_find_box{width:100%;height:30px;background:#f5f5f5;border-radius:10px;}
	        .dong_find_box_img{width:20%;height:30px;line-height:30px;float:left;text-align:center;}
	        .dong_input_box{width:80%;height:30px;float:left;}
	        .dong_symbox{width:90%;height:20px;border:none;background:#f5f5f5;margin-top:5px;}
	        .dong_sel_box{width:100%;height:30px;border-bottom:1px solid #151617;}
	        .dong_sel_span{display:block;width:35px;height:30px;line-height:30px;border-bottom:2px solid #3db485;font-size:14px;text-align:center;font-size:14px;}
	        .symbol_list{width:100%;margin-top:10px;}
	        .sy_list_box{width:100%;height:30px; border-bottom: 1px solid #2c2d2e;margin-top: 10px;}
	        .sy_list_boxl{width:35%;height:30px;line-height:30px;float:left;text-align:left;}
	        .sy_list_boxr{width:30%;height:30px;line-height:30px;float:right;text-align:right;}
	        .order_title{display: inline-block;margin-right: 20px;font-weight: 1000;}
	        .FCD535{border-bottom: 2px solid #3db485;}
	        .fccbox{width:100%;height:15px;background:#f5f5f5;}
	        .wtlistbox{width:100%;padding:0px 10px;} 
	        .o_title_box{width:50%;height:40px;line-height:40px;border-bottom:1px solid #151617;}
	        .tlistbox{width:100%;clear:both;padding:10px 0px;}
	        .tlistbox_1{width:100%;height:100px;border-bottom:1px solid #151617;}
	        .tlistbox_2{width:100%;height:30px;}
	        .tlistbox_3{width:80%;height:30px;line-height:30px;}
	        .tlistbox_4{width:20%;height:30px;line-height:30px;}
	        .tlistbox_5{padding:5px 10px;background:#fcfcfc;border-radius:5px;}
	        .tlistbox_6{width:100%;height:60px;}
	        .tlistbox_7{width:33.33%;height:60px;}
	        .tlistbox_8{width:100%;height:30px;line-height:30px;}
	        .tlistbox_9{width:100%;height:30px;line-height:20px;}

			.css-jmskxt {
				background: #151617 !important;
			}

			input:focus {
				background:no-repeat !important;
			}






	    </style>
	</head>
	<body>
	    <div class="App">
	        <div class="css-tq0shg">
	            <include file="Public:header"/>
	            <div class="css-ogtd7z">
	                <div class="css-jrzkh7">
	                    <div id="header_global_js_wxgy34nj" class="css-1aac2e"></div>
	                 </div>
	            </div>
	            <main class="css-1wr4jig">
                    <div style="width:100%;min-height:880px;background:#151617;padding:10px 0px 0px 0px;">
                        <div style="width:100%;min-height:900px;">
                            <div style="width:20%;min-height:900px;float:left;padding:0px 10px 0px 0px;">
                                <div style="width:100%;min-height:1009px;background:#1a1b1c;">
                                    <div style="width:100%;height:100%;padding:0px;padding:0px 10px;">
	                                    <div style="width:100%;top:0px;">

	                                        <div class="dong_sel_box">
	                                            <span class="fcc dong_sel_span fcgs">USDT</span>
	                                        </div>

											<a href="javascript:void(0)">
												<div class="sy_list_box">
													<div class="sy_list_boxl">
														<span  class="f14  fccs">{:L('币种')}</span>
													</div>
													<div class="sy_list_boxl  f14 fccs" style="text-align: center;" >{:L('24小时量')}</div>
													<div class="sy_list_boxr  f14 fccs" >{:L('涨幅')}</div>
												</div>
											</a>

	                                    </div>
	                                    
	                                    
	                                    <div class="symbol_list" id="smybolbox">
	                                        <div style="width:100%;height:100px;line-height:100px;text-align:center;">
                                                <span class="f14 fcc">{:L('没有获取数据')}</span>
                                            </div>
	                                    </div>
	                                </div>
                                    
                                </div>
                            </div>
                            <div style="width:60%;min-height:900px;background:#1a1b1c;float:left;">
                                <div style="width:100%;height:50px;border-bottom:3px solid #151617;padding-right: 40%">
                                    <div class="klinetitle">
                                        <div class="klinetitle_l">
			                        	    <p class="newpricebox fw f12 fcgs">{$coinname}</p>
			                        	    <p class="changebox fw f12 fcgs">{:L('币币交易')}</p>
			                        	</div>
			                        	<div class="klinetitle_l">
			                        	    <p class="newpricebox fw closeprice" id="newpricebox" style="font-size:22px!important;">--</p>
			                        	    <p class="changebox fw"  id="changebox">--</p>
			                        	</div>
			                        	<div class="klinetitle_r">
			                        	    <div class="col-4 klinetitle-s-box">
			                        	        <div class="newpricebox f12 fccs">
			                        	            {:L('最低')}
			                        	        </div>
			                        	        <div class="changebox f12 fccs" id="minmoney">
			                        	            --
			                        	        </div>
			                        	    </div>
			                        	    <div class="col-4 klinetitle-s-box">
			                        	        <div class="newpricebox f12 fccs">
			                        	            {:L('最高')}
			                        	        </div>
			                        	        <div class="changebox f12 fccs" id="maxmoney">
			                        	            --
			                        	        </div>
			                        	    </div>
			                        	    <div class="col-4 klinetitle-s-box">
			                        	        <div class="newpricebox f12 fccs">
			                        	            24h{:L('量')}
			                        	        </div>
			                        	        <div class="changebox f12 fccs" id="allvol">
			                        	            --
			                        	        </div>
			                        	    </div>
			                        	    
			                        	</div>
                			        </div>
                                </div>

                                <div style="width:100%;height:600px;padding:5px;">
                                    <div style="width:100%;height:600px;">
                                        <div style="width:100%;height:600px;">
			                                <iframe id="iframeid" width="100%" scrolling="no" height="600px" src="{:U('Trade/ordinary')}?market={$market}"  noresize="noresize" frameborder="0" >{:L('不支持IFRAME，请升级')}</iframe>
			                            </div>
                                    </div>
                                </div>
                                <div style="width:100%;height:360px;padding:0px 25px;border-top: 5px solid #151617;">
                                    <div style="width:100%;height:360px;">
                                        <div class="contentbox" style="width:100%;padding:0px;">
		                                    <div class="tradebox" style="width:100%;padding:0px;">
		                                        <div class="tradebox_l" style="width:100%;padding:0px;">
													<div>
														<!--买入框-->
														<div class="formbox col-6 klinetitle-s-box klinetitle-s-box-l" id="buycoinbox">

															<div style="width:100%;height:30px;margin-bottom:10px;">
																<div style="width:30%;height:30px;line-height:30px;float:left;">
																	<span class="f14 fcc">{:L('可用')}</span>
																</div>
																<div style="width:70%;height:30px;line-height:30px;float:left;text-align:right;">
																	<if condition="$uid elt 0">
																	<span class="f14 fcc">- - </span>
																	<else />
																	<span class="f14 fcc">{$usdt_blance}</span>
																	</if>
																	<span class="f14 fcc">USDT</span>
																</div>
															</div>


															<div class="formbox_op">
																<div class="formbox_op_list jy-btn-buy btn_bg_color fcc" id="buyxjbtn">
																	<span class="f12 btn_bg_color_tcg" id="xjspan" >{:L('限价委托')}</span>
																</div>
																<div class="formbox_op_list jy-btn-buy" id="buysjbtn">
																	<span class="f12 btn_bg_color_tch" id="sjspan">{:L('市价委托')}</span>
																</div>
															</div>

															<!--限价委托单价框-->
															<div class="inputbox" id="xjbox">
																<div class="inputbox_float">
																	<div class="klinetitle-s-box input-desc tcc tcc btn_bg_color_tch f12 ">
																		{:L('买入价')}
																	</div>
																	<input type="text" id="newprice" class="fcc xjinput fcf" value=""  />
																</div>
																<div class="inputbox_float" style="width:20%;float:right;">
																	<div class="input_bi borderright" id="dash_buyprice">
																		<i class="bi bi-dash bistyle"></i>
																	</div>
																	<div class="input_bi" id="plus_buyprice">
																		<i class="bi bi-plus bistyle"></i>
																	</div>
																</div>
															</div>

															<div class="inputbox" style="background: #2c2d2e;display:none" id="sjbox">
																<div class="inputbox_float">
																	<div class="klinetitle-s-box input-desc tcc fcf f12">

																	</div>
																	<input type="text"   class="fcc xjinput  sjxjinput" placeholder="{:L('按市场最优价')}" style="background:rgb(44, 45, 46);"  disabled="disabled"  />
																</div>
															</div>

															<div class="inputbox" id="sjnumbox" style="background: #2c2d2e;display:none;">
																<div class="inputbox_float">
																	<div class="klinetitle-s-box input-desc fcf f12">

																	</div>
																</div>
															</div>

															<div class="inputbox" id="xjnumbox" style="display:block;">
																<div class="inputbox_float">
																	<div class="klinetitle-s-box input-desc tcc tcc btn_bg_color_tch f12">
																		{:L('买入量')}
																	</div>
																	<input type="text" id="buynum" oninput="buynumfc();" class="fcc xjinput" value="" placeholder="{:L('输入数量')}"  autocomplete="off" step="1"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,6})?/) ? this.value.match(/\d+(\.\d{0,6})?/)[0] : ''" />
																</div>
																<div class="inputbox_float" style="width:20%;float:right;">
																	<div class="input_bi borderright" id="dash_buynum">
																		<i class="bi bi-dash bistyle"></i>
																	</div>
																	<div class="input_bi" id="plus_buynum">
																		<i class="bi bi-plus bistyle"></i>
																	</div>
																</div>
															</div>


															<div class="blbox">
																<div class="blbox_1" onclick="buyblfc(1,25);">
																	<div class="blbox_3 bgf5" id="buybl_1"></div>
																	<div class="blbox_4">
																		<span class="f12 fcc">25%</span>
																	</div>
																</div>
																<div class="blbox_1" onclick="buyblfc(2,50);">
																	<div class="blbox_3 bgf5" id="buybl_2"></div>
																	<div class="blbox_4">
																		<span class="f12 fcc">50%</span>
																	</div>
																</div>
																<div class="blbox_1" onclick="buyblfc(3,75);">
																	<div class="blbox_3 bgf5" id="buybl_3"></div>
																	<div class="blbox_4">
																		<span class="f12 fcc">75%</span>
																	</div>
																</div>
																<div class="blbox_2" onclick="buyblfc(4,100);">
																	<div class="blbox_3 bgf5" id="buybl_4"></div>
																	<div class="blbox_4">
																		<span class="f12 fcc">100%</span>
																	</div>
																</div>
															</div>


															<div class="inputbox">
																<div class="inputbox_float">
																	<div class="klinetitle-s-box input-desc tcc  tcc btn_bg_color_tch f12">
																		{:L('交易额')}
																	</div>
																	<input type="text" id="buyusdt" oninput="buyusdtfc();" class="fcc xjinput" value="" placeholder=""  autocomplete="off" step="1"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,6})?/) ? this.value.match(/\d+(\.\d{0,6})?/)[0] : ''" />
																</div>
																<div class="inputbox_float" style="width:10%;text-align:center;line-height:36px;float:right;">
																	<span class="f12 fcc">USDT</span>
																</div>
															</div>

															<if condition="$uid egt 1">
																<div onclick="bb_buycoin(1);" style="width:100%;height:36px;line-height:36px;background:#0ecb81;text-align:center;color:#fff;border-radius:5px;margin-top:10px;">
																	<span class="f12" style="color:#fff;">{:L('买入')}</span>
																</div>
																<else />
																<a href="{:U('Login/index')}">
																	<div style="width:100%;height:36px;line-height:36px;background:#0ecb81;text-align:center;color:#fff;border-radius:5px;margin-top:10px;">
																		<span class="f12" style="color:#fff;">{:L('登陆')}</span>
																	</div>
																</a>
															</if>

														</div>

														<!--卖出框-->
														<div class="formbox col-6 klinetitle-s-box" id="sellcoinbox">

															<div style="width:100%;height:30px;margin-bottom:10px;">
																<div style="width:30%;height:30px;line-height:30px;float:left;">
																	<span class="f14 fcc">{:L('可用')}</span>
																</div>
																<div style="width:70%;height:30px;line-height:30px;float:left;text-align:right;">
																	<if condition="$uid elt 0">
																		<span class="f14 fcc">- - </span>
																	<else />
																		<span class="f14 fcc">{$coin_blance}</span>
																	</if>
																	<span class="f14 fcc">{$symbolup}</span>
																</div>
															</div>

															<div class="formbox_op">
																<div class="formbox_op_list jy-btn btn_bg_color" id="sell_xjbtn">
																	<span class="f12 btn_bg_color_tcg" id="sell_xjspan">{:L('限价委托')}</span>
																</div>
																<div class="formbox_op_list jy-btn" id="sell_sjbtn">
																	<span class="f12 btn_bg_color_tch" id="sell_sjspan">{:L('市价委托')}</span>
																</div>
															</div>

															<!--限价委托单价框-->
															<div class="inputbox" id="sell_xjbox">
																<div class="inputbox_float">
																	<div class="klinetitle-s-box input-desc tcc btn_bg_color_tch f12">
																		{:L('卖出价')}
																	</div>
																	<input type="text" id="sell_newprice" class="fcc xjinput" value=""  />
																</div>
																<div class="inputbox_float" style="width:20%;float:right;">
																	<div class="input_bi borderright" id="dash_sellprice">
																		<i class="bi bi-dash bistyle"></i>
																	</div>
																	<div class="input_bi" id="plus_sellprice">
																		<i class="bi bi-plus bistyle"></i>
																	</div>
																</div>
															</div>

															<div class="inputbox" style="background: rgb(44, 45, 46);display:none" id="sell_sjbox">
																<div class="inputbox_float">
																	<div class="klinetitle-s-box input-desc tcc fcf f12">

																	</div>
																	<input type="text"  class="fcc xjinput sjxjinput" placeholder="{:L('按市场最优价')}" style="background:rgb(44, 45, 46);" disabled="disabled"  />
																</div>
															</div>

															<div class="inputbox" id="sell_sjnumbox" style="background: rgb(44, 45, 46);display:none;">
																<div class="inputbox_float">
																	<div class="klinetitle-s-box input-desc fcf f12">

																	</div>
																</div>
															</div>

															<div class="inputbox" id="sell_xjnumbox" style="display:block;">
																<div class="inputbox_float">
																	<div class="klinetitle-s-box input-desc tcc btn_bg_color_tch f12">
																		{:L('卖出量')}
																	</div>
																	<input type="text" id="sell_num" oninput="sellnumfc();" class="fcc xjinput" value="" placeholder="{:L('输入数量')}" autocomplete="off" step="1"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,6})?/) ? this.value.match(/\d+(\.\d{0,6})?/)[0] : ''" />
																</div>
																<div class="inputbox_float" style="width:20%;float:right;">
																	<div class="input_bi borderright" id="dash_sellnum">
																		<i class="bi bi-dash bistyle"></i>
																	</div>
																	<div class="input_bi" id="plus_sellnum">
																		<i class="bi bi-plus bistyle"></i>
																	</div>
																</div>
															</div>


															<div class="blbox">
																<div class="blbox_1" onclick="sellblfc(1,25);">
																	<div class="blbox_3 bgf5" id="sellbl_1"></div>
																	<div class="blbox_4">
																		<span class="f12 fcc">25%</span>
																	</div>
																</div>
																<div class="blbox_1" onclick="sellblfc(2,50);">
																	<div class="blbox_3 bgf5" id="sellbl_2"></div>
																	<div class="blbox_4">
																		<span class="f12 fcc">50%</span>
																	</div>
																</div>
																<div class="blbox_1" onclick="sellblfc(3,75);">
																	<div class="blbox_3 bgf5" id="sellbl_3"></div>
																	<div class="blbox_4">
																		<span class="f12 fcc">75%</span>
																	</div>
																</div>
																<div class="blbox_2" onclick="sellblfc(4,100);">
																	<div class="blbox_3 bgf5" id="sellbl_4"></div>
																	<div class="blbox_4">
																		<span class="f12 fcc">100%</span>
																	</div>
																</div>
															</div>



															<div class="inputbox" id="sellxjusdt">
																<div class="inputbox_float">
																	<div class="klinetitle-s-box input-desc tcc btn_bg_color_tch f12">
																		{:L('交易额')}
																	</div>
																	<input type="text" id="sell_usdt" oninput="sellusdtfc();" class="fcc xjinput" value="" placeholder="" autocomplete="off" step="1"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,6})?/) ? this.value.match(/\d+(\.\d{0,6})?/)[0] : ''" />
																</div>
																<div class="inputbox_float" style="width:10%;text-align:center;line-height:36px;float:right;">
																	<span class="f12 fcc">USDT</span>
																</div>
															</div>

															<div class="inputbox" id="sellxjcoin" style="display:none;">
																<div class="inputbox_float">
																	<div class="klinetitle-s-box input-desc tcc btn_bg_color_tch f12">
																		{:L('交易额')}
																	</div>
																	<input type="text" id="sell_coin" oninput="sellcoinfc();" class="fcc xjinput" value="" placeholder=""  autocomplete="off" step="1"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,6})?/) ? this.value.match(/\d+(\.\d{0,6})?/)[0] : ''" />
																</div>
																<div class="inputbox_float" style="width:10%;text-align:center;line-height:36px;float:right;float:right;">
																	<span class="f12 fcc">{$symbol}</span>
																</div>
															</div>


															<if condition="$uid egt 1">
																<div  onclick="bb_sellcoin(2);" style="width:100%;height:36px;line-height:36px;background:#f5465c;text-align:center;color:#fff;border-radius:5px;margin-top:10px;">
																	<span class="f12" style="color:#fff;">{:L('卖出')}</span>
																</div>
																<else />
																<a href="{:U('Login/index')}">
																	<div style="width:100%;height:36px;line-height:36px;background:#f5465c;text-align:center;color:#fff;border-radius:5px;margin-top:20px;">
																		<span class="f12" style="color:#fff;">{:L('登陆')}</span>
																	</div>
																</a>
															</if>

														</div>
													</div>

		            
		            
		            


		        </div>
		                                    </div>
		    
		                                </div>
                                        
                                        
                                        
                                        
                                        
                                    </div>
                                </div>
                                
                            </div>
                            <div style="width:20%;min-height:800px;float:left;padding:0px 0px 0px 10px;">
                                <div style="width:100%;min-height:1009px;background:#1a1b1c;">
                                    <div style="width:100%;height:40px;padding:0px 15px;">
                                        <div style="min-width:20%;height:40px;line-height:40px;padding: 0px 5px;">
                                            <span class="f12 fccs">{:L('盘口')}</span>
                                        </div>
                                        <div style="width:100%;margin-top:20px;">
                                            
                                            <div style="width:100%;">
		                                        <div style="width:100%;height:20px;padding:0px 5px;">
		                                            <div class="tradebox_title col-4 tcl">
		                                                <span class="fccs f12">{:L('价格')}</span>
		                                            </div>
		                                            <div class="tradebox_title tcc col-4">
		                                                <span class="fccs f12">{:L('数量')}</span>
		                                            </div>
													<div class="tradebox_title col-4 tcr">
														<span class="fccs f12">{:L('时间')}</span>
													</div>
		                                        </div>
		                                        
		                                        <div class="trade_listbox" id="tradesellbox">
		                                            <div style="width:100%;height:30px;">
		                                                <div class="trade_list col-4">
		                                                    <span class="red f12">--</span>
		                                                </div>
		                                                <div class="trade_list col-4 tcc">
		                                                    <span class="red f12">--</span>
		                                                </div>
														<div class="trade_list col-4">
															<span class="red f12">--</span>
														</div>
		                                            </div>
		                                        </div>
		                                        <div class="trade_listpricebox closeprice" id="closeprice">
		                                            <span>--</span>
		                                        </div>
		                                        <div class="trade_listbox" id="tradebuybox">
		                                            <div style="width:100%;height:30px;">
		                                                <div class="trade_list col-4 tcl">
		                                                    <span class="green f12">--</span>
		                                                </div>
		                                                <div class="trade_list col-4 tcc">
		                                                    <span class="green f12">--</span>
		                                                </div>
														<div class="trade_list col-4 tcr">
															<span class="green f12">--</span>
														</div>
		                                            </div>
		                                        </div>
		                                    </div>
                                            
                                            
                                        </div>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
					<div style="width:100%;float:left;min-height: 160px;border-top: 10px solid #151617;background: #fff">

						<div class="order-top">
							<div class="order-top-span order-top-current order-top-select fccs" onclick="order_top_select_action(1)">
								{:L('当前委托')}
							</div>
							<div class="order-top-span order-top-history fccs " onclick="order_top_select_action(2)">
								{:L('历史委托')}
							</div>
							<div class="refresh-icon">
								<svg width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M42 8V24" stroke="#303131" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/><path d="M6 24L6 40" stroke="#303131" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/><path d="M6 24C6 33.9411 14.0589 42 24 42C28.8556 42 33.2622 40.0774 36.5 36.9519" stroke="#303131" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/><path d="M42.0007 24C42.0007 14.0589 33.9418 6 24.0007 6C18.9152 6 14.3223 8.10896 11.0488 11.5" stroke="#303131" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/></svg>

							</div>

						</div>

						<div class="order-main" style="background: #1a1b1c">
							<if condition="$uid elt 0">
								<div class="table-history-more">
									<img src="/Public/Home/static/imgs/empty-dark.png" class="empty-svg" >
									<p class="fccs"> {:L('暂无订单')}</p>
								</div>
								<else />
								<div class="table-box order-main-table-current" >
									<table class="table fcf order-main-table ">

										<thead>
										<tr  class="fccs">
											<td>{:L('ID')}</td>
											<td>{:L('交易对')}</td>
											<td>{:L('方向')}</td>
											<td>{:L('状态')}</td>
											<td>{:L('委托额度')}</td>
											<td>{:L('交易限价')}</td>
											<td>{:L('委托时间')}</td>
											<td>{:L('操作')}</td>
										</tr>
										</thead>

										<tbody style="background: #151617">
										<foreach name="bblist1" item="vo">
											<tr class=" f12">

												<td>{$vo.id}</td>
												<td>{$vo.symbol}</td>
												<if condition="$vo.type eq 1">
													<td class="f14 fw fgreen">{:L('购买')}</td>
													<elseif condition="$vo.type eq 2" />
													<td class="f14 fw fred">{:L('出售')}</td>
												</if>
												<if condition="$vo.status eq 1">
													<td class=" f14">{:L('委托中')}</td>
													<elseif condition="$vo.status eq 2" />
													<td class=" f14">{:L('交易完成')}</td>
													<elseif condition="$vo.status eq 3" />
													<td class=" f14">{:L('已撤消')}</td>
												</if>

												<if condition="$vo.type eq 1">
													<td class=" f14">{$vo.usdtnum}</td>
													<elseif condition="$vo.type eq 2" />
													<td class=" f14">{$vo.coinnum}</td>
												</if>

												<td>{$vo.xjprice}</td>
												<td>{$vo.addtime}</td>
												<td><span class="fcy f14" onclick="clearorder({$vo.id});" style="background:#f5f5f5;padding:5px 10px;border-radius:5px;cursor: pointer;">{:L('撤消委托')}</span></td>
											</tr>
										</foreach>
										</tbody>
									</table>
									<div class="table-history-more">
										<empty name="bblist1">
											<img src="/Public/Home/static/imgs/empty-dark.png" class="empty-svg" >
											<p class="fccs"> 暂无订单</p>
											<else />
											<a href="{:U('Contract/bborder')}">
												<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"viewBox="0 0 384 512"fill="#e6ecf2"><path d="M192 384c-8.188 0-16.38-3.125-22.62-9.375l-160-160c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L192 306.8l137.4-137.4c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25l-160 160C208.4 380.9 200.2 384 192 384z"/></svg>
												<span class="fcf"> 查看更多</span>
											</a>
										</empty>
									</div>
								</div>



								<div class="table-box order-main-table-history">

									<table class="table fcf order-main-table ">
										<thead>
										<tr class="fccs">
											<td>类型</td>
											<td>交易对</td>
											<td>方向</td>
											<td>状态</td>
											<td>委托额度</td>
											<td>交易限价</td>
											<td>委托时间</td>
											<td>结单时间</td>
										</tr>
										</thead>
										<tbody style="background: #151617">
										<foreach name="bblist" item="vo">
											<tr class=" f12">
												<if condition="$vo.type eq 1">
													<td class="fcy f14">{:L('市价交易')}</td>
													<elseif condition="$vo.type eq 2" />
													<td class="fcy f14">{:L('限价委托')}</td>
												</if>
												<div class="bcontentop" style="width:8%">
													<td class=" f14">{$vo.symbol}</td>
												</div>

												<if condition="$vo.type eq 1">
													<td class="f14 fw fgreen">{:L('购买')}</td>
													<elseif condition="$vo.type eq 2" />
													<td class="f14 fw fred">{:L('出售')}</td>
												</if>

												<if condition="$vo.status eq 1">
													<td class=" f14">{:L('委托中')}</td>
													<elseif condition="$vo.status eq 2" />
													<td class=" f14">{:L('交易完成')}</td>
													<elseif condition="$vo.status eq 3" />
													<td class=" f14">{:L('已撤消')}</td>
												</if>
												<if condition="$vo.type eq 1">
													<td class=" f14">{$vo.usdtnum}</td>
													<elseif condition="$vo.type eq 2" />
													<td class=" f14">{$vo.coinnum}</td>
												</if>
												<td>{$vo.xjprice}</td>
												<td>{$vo.addtime}</td>
												<td>{$vo.tradetime}</td>
											</tr>
										</foreach>
										</tbody>
									</table>

									<div class="table-history-more">
									<empty name="bblist">
										<img src="/Public/Home/static/imgs/empty.e90e5075.svg" class="empty-svg" >
										<p class="fccs"> 暂无订单</p>
										<else />
											<a href="{:U('Contract/bbhistoryorder')}">
											<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"viewBox="0 0 384 512"fill="#e6ecf2"><path d="M192 384c-8.188 0-16.38-3.125-22.62-9.375l-160-160c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L192 306.8l137.4-137.4c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25l-160 160C208.4 380.9 200.2 384 192 384z"/></svg>
											<span class="fcf"> 查看更多</span>
										</a>
									</empty>
								</div>
								</div>
							</if>
						</div>
					</div>
	            </main>
	            
	            <input type="hidden" id="buy_usermoney" value="{$usdt_blance}" />
                <input type="hidden" id="buy_usercoin" value="{$coin_blance}" />
                
                <input type="hidden" id="symbolbox" value="{$coinname}"  />
                <!--交易限价单价-->
	            <input type="hidden" id="mprice" value="" />
	            <!--交易限价买卖币的数量-->
	            <input type="hidden" id="mnum" value="" />
	            <!--交易限价买卖USDT的数量-->
	            <input type="hidden" id="musdt" value="" />
	            <!--购买类型，1限价2市价-->
	            <input type="hidden" id="buytype" value="1" />
	            
	            
	            <include file="Public:footer"/>
	        </div>   
	    </div>


	</body>
	<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
    <script type="text/javascript" src="/Public/Home/static/js/layer/layer.js" ></script>
    <script type="text/javascript" src="/Public/Home/static/js/jquery.SuperSlide.2.1.1.js" ></script>
    <script type="text/javascript">
        function clearorder(id){
            var oid = id;
            $.post("{:U('Trade/clearorder')}",
            {"oid":oid},
            function(data){
                if(data.code == 1){
                    layer.msg(data.info);
                    setTimeout(function(args){
                        window.location.reload();
                    },2000);
                }else{
                    layer.msg(data.info);return false;
                }    
            });
        }

		$('.xjinput').on('click', function () {
			$('.inputbox').css('border', '1px solid #707A8A');
			$(this).parent().parent().css('border', '1px solid #3db485');
		})
    </script>
    <script type="text/javascript">
        function bb_sellcoin(type){
            var type = type;
            if(type <= 0){
                layer.msg("{:L('缺少重要参数')}");return false;
            }
            var symbol = $("#symbolbox").val();
            var mprice = $("#sell_newprice").val();
            var musdt = $("#musdt").val();
            var selltype = $("#buytype").val();
            if(selltype == 1){
                var mnum = $("#sell_num").val();
                if(mprice < 0){
                    layer.msg("{:L('缺少重要参数')}");return false;
                }
                if(mnum <= 0){
                    layer.msg("{:L('请输入出售数量')}");return false;
                }
            }else if(selltype == 2){
                var mnum = $("#sell_coin").val();
                if(mnum <= 0){
                    layer.msg("{:L('请输入出售数量')}");return false;
                }
            }
            $.post("{:U('Trade/upbbsell')}",
            {'symbol':symbol,'mprice':mprice,'mnum':mnum,'musdt':musdt,'selltype':selltype,'type':type},
            function(data){
                if(data.code == 1){
                    layer.msg(data.info);
                    setTimeout(function(args){
                        window.location.reload();
                    },2000);
                }else{
                    layer.msg(data.info);return false; 
                }
            });
        }
    </script>
    <script type="text/javascript">
        function bb_buycoin(type){
            var type = type;
            if(type <= 0){
                layer.msg("{:L('缺少重要参数')}");return false;
            }
            var symbol = $("#symbolbox").val();
            var mprice = $("#newprice").val();
            var mnum = $("#mnum").val();
            var musdt = $("#musdt").val();
            var buytype = $("#buytype").val();
            if(buytype == 1){
                if(mnum <= 0){
                    layer.msg("{:L('输入数量')}");return false;
                }
            }else if(buytype == 2){
                if(musdt <= 0){
                    layer.msg("{:L('输入USDT数量')}");return false;
                }
            }
            $.post("{:U('Trade/upbbbuy')}",
            {'symbol':symbol,'mprice':mprice,'mnum':mnum,'musdt':musdt,'buytype':buytype,'type':type},
            function(data){
                if(data.code == 1){
                    layer.msg(data.info);
                    setTimeout(function(args){
                        window.location.reload();
                    },2000);
                }else{
                   layer.msg(data.info);return false; 
                }
            });
        }
    </script>



    <script type="text/javascript">
        function getallsmybol(){
            $.post("{:U('Ajaxtrade/getallcoin')}",
            function(data){
                if(data.code == 1){
                    $("#smybolbox").empty();
                    var html = '';
                    if(data.data == '' || data.data == null){
                        html = '<div style="width:100%;height:100px;line-height:100px;text-align:center;">'+ 
                               '<span class="f12 fcf">' + "{:L('没有获取数据')}" + '</span>'+
                               '</div>';
                        $("#smybolbox").append(html);
                        
                    }else{
                        $.each(data.data,function(key,val){
                            html += '<a href="/Trade/index.html?symbol='+ val.coin +'&type=buy">'+
                                    '<div class="sy_list_box">'+
                                    '<div class="sy_list_boxl">'+
                                    '<span  class="f12 fcf">'+ val.cname +'</span>'+
                                    '</div>'+
                                    '<div class="sy_list_boxl fcf" style="text-align:center;font-size:12px;">' + val.open + '</div>'+
                                    '<div class="sy_list_boxr fcf" style="font-size:12px;">' + val.change +'</div>'+
                                    '</div>'+
                                    '</a>';
                        });
                        
                        $("#smybolbox").append(html);
                    }
                }else{
                    html =  '<div style="width:100%;height:100px;line-height:100px;text-align:center;">'+ 
                            '<span class="f12 fcc">' + "{:L('没有获取数据')}" + '</span>'+
                            '</div>';
                    $("#smybolbox").append(html);
                }
            });
        }
    </script>
    <script type="text/javascript">
        $(function(){
            getallsmybol();
            gettradbuy();
            gettradsell();
            setInterval("getallsmybol()",3000);
            setInterval("gettradbuy()",2000);
            setInterval("gettradsell()",2000);
        });
    </script>
    <script type="text/javascript">
        function gettradsell(){
            var symbol = $("#symbolbox").val();
            $.post("{:U('Ajaxtrade/gettradsellten')}",
            {'symbol':symbol},
            function(data){
                if(data.code == 1){
                    $("#tradebuybox").empty();
                    var html = '';
                    if(data.data == '' || data.data == null){
                        html = '<div style="width:100%;height:100px;line-height:100px;text-align:center;">'+ 
                               '<span class="f12 fcc">' + "{:L('没有获取数据')}" + '</span>'+
                               '</div>';
                        $("#tradebuybox").append(html);
                        
                    }else{
                        $.each(data.data,function(key,val){
                            html += '<div style="width:100%;height:30px;">'+
    		                        '<div class="trade_list col-4 tcl">'+
    		                        '<span class="red f12">'+ val.price +'</span>'+
    		                        '</div>'+
    		                        '<div class="trade_list col-4 tcc">'+
    		                        '<span class="red f12">'+ val.amount +'</span>'+
    		                        '</div>'+
									'<div class="trade_list col-4 tcr">'+
									'<span class="red f12">'+ val.time +'</span>'+
									'</div>'+
    		                        '</div>';
                        });
                        $("#tradebuybox").append(html);
                    }
                }
            });
        }
    </script>
    <script type="text/javascript">
        function gettradbuy(){
            var symbol = $("#symbolbox").val();
			console.log('2222');
            console.log(symbol);
            $.post("{:U('Ajaxtrade/gettradbuyten')}",
            {'symbol':symbol},
            function(data){
                if(data.code == 1){
                    $("#tradesellbox").empty();
                    var html = '';
                    if(data.data == '' || data.data == null){
                        html = '<div style="width:100%;height:100px;line-height:100px;text-align:center;">'+ 
                               '<span class="f12 fcc">' + "{:L('没有获取数据')}" + '</span>'+
                               '</div>';
                        $("#tradesellbox").append(html);
                        
                    }else{
                        $.each(data.data,function(key,val){
                            html += '<div style="width:100%;height:30px;">'+
    		                        '<div class="trade_list col-4 tcl">'+
    		                        '<span class="green f12">'+ val.price +'</span>'+
    		                        '</div>'+
    		                        '<div class="trade_list col-4 tcc">'+
    		                        '<span class="green f12">'+ val.amount +'</span>'+
    		                        '</div>'+
									'<div class="trade_list col-4 tcr">'+
									'<span class="green f12">'+ val.time +'</span>'+
									'</div>'+
    		                        '</div>';
                        });
                        $("#tradesellbox").append(html);
                    }
                }
            });
        }
    </script>
    <script type="text/javascript">
        $(function(){
            getcoinprice();
            setInterval("getcoinprice()",2000);
        });
    </script>
    <script type="text/javascript">
        function getcoinprice(){
            var symbol = $("#symbolbox").val();
            $.post("{:U('Ajaxtrade/getcoinprice')}",
            {'symbol':symbol},
            function(data){
                if(data.code == 1){
                    $("#changebox").html(data.change);
                    $(".closeprice").html(data.price);
                    $("#minmoney").html(data.low);
                    $("#maxmoney").html(data.high);
                    $("#allvol").html(data.amount);
                }else{
                    console.log("{:L('未获取数据')}");
                }
            });
        }
    </script>


    <script type="text/javascript">
        $("#buybtn").click(function(){
            $("#buybtn").addClass("bggreen");
            $("#buybtn").removeClass("bghc");
            $("#sellbtn").addClass("bghc");
            $("#sellbtn").removeClass("bggreen");
            $("#buycoinbox").show();
            $("#sellcoinbox").hide();
            $("#buybtnspan").addClass("cfff");
            $("#buybtnspan").removeClass("c000");
            $("#sellbtnspan").addClass("c000");
            $("#sellbtnspan").removeClass("cfff");
        });
        $("#sellbtn").click(function(){
            $("#buybtn").removeClass("bgred");
            $("#buybtn").addClass("bghc");
            $("#sellbtn").removeClass("bghc");
            $("#sellbtn").addClass("bgred");
            $("#buycoinbox").hide();
            $("#sellcoinbox").show();
            $("#buybtnspan").addClass("c000");
            $("#buybtnspan").removeClass("cfff");
            $("#sellbtnspan").addClass("cfff");
            $("#sellbtnspan").removeClass("c000");
        });
    </script>
    <script type="text/javascript">
        //出售框
        $("#sell_xjbtn").click(function(){

            $("#sell_xjspan").addClass("btn_bg_color_tcg");
            $("#sell_xjspan").removeClass("btn_bg_color_tch");
            $("#sell_sjspan").addClass("btn_bg_color_tch");
            $("#sell_sjspan").removeClass("btn_bg_color_tcg");

            $("#sell_xjbox").show();
            $("#sell_sjbox").hide();
            $("#buytype").val(1);
            $("#sell_sjnumbox").hide();
            $("#sell_xjnumbox").show();
            $("#sellxjusdt").show();
            $("#sellxjcoin").hide();
			$('.jy-btn').removeClass('btn_bg_color');
			$(this).addClass('btn_bg_color');
            
        });

        $("#sell_sjbtn").click(function(){

            $("#sell_sjspan").addClass("btn_bg_color_tcg");
            $("#sell_sjspan").removeClass("btn_bg_color_tch");
            $("#sell_xjspan").addClass("btn_bg_color_tch");
            $("#sell_xjspan").removeClass("btn_bg_color_tcg");

            $("#sell_sjbox").show();
            $("#sell_xjbox").hide();
            $("#buytype").val(2);
            $("#sell_xjnumbox").hide();
            $("#sell_sjnumbox").show();
            $("#sellxjusdt").hide();
            $("#sellxjcoin").show();
			$('.jy-btn').removeClass('btn_bg_color');
			$(this).addClass('btn_bg_color');
        });
    
        //买入框
        $("#buyxjbtn").click(function(){
            $("#xjspan").addClass("btn_bg_color_tcg");
            $("#xjspan").removeClass("btn_bg_color_tch");
            $("#sjspan").addClass("btn_bg_color_tch");
            $("#sjspan").removeClass("btn_bg_color_tcg");
            $("#xjbox").show();
            $("#sjbox").hide();
            $("#buytype").val(1);
            $("#sjnumbox").hide();
            $("#xjnumbox").show();
            $('.jy-btn-buy').removeClass('btn_bg_color');
            $(this).addClass('btn_bg_color');

        });
        $("#buysjbtn").click(function(){

            $("#sjspan").addClass("btn_bg_color_tcg");
            $("#sjspan").removeClass("btn_bg_color_tch");
            $("#xjspan").addClass("btn_bg_color_tch");
            $("#xjspan").removeClass("btn_bg_color_tcg");

            $("#sjbox").show();
            $("#xjbox").hide();
            $("#buytype").val(2);
            $("#xjnumbox").hide();
            $("#sjnumbox").show();
			$('.jy-btn-buy').removeClass('btn_bg_color');
			$(this).addClass('btn_bg_color');
        });
    </script>


    <script type="text/javascript">
        function sellusdtfc(){
            var newprice = parseFloat($("#newprice").val()); //单价
            var buyusdt = parseFloat($("#sell_usdt").val()); //输入的USDT数量
            var usermoney = parseFloat($("#buy_usercoin").val());//币的数量
            var coinnum = parseFloat(buyusdt / newprice).toFixed(4);
            
            if(coinnum > usermoney){
                layer.msg("{:L('余额不足')}");return false;
            }else{
            	if (coinnum == 'NaN') {
					coinnum = 0.000
				}
                $("#sell_num").val(coinnum);
                $("#musdt").val(buyusdt);
            }
        }
        function buyusdtfc(){
            var newprice = parseFloat($("#newprice").val());
            var buyusdt = parseFloat($("#buyusdt").val());
            var usermoney = parseFloat($("#buy_usermoney").val());
            var num = parseFloat(buyusdt / newprice).toFixed(4);
            if(buyusdt > usermoney){
                layer.msg("{:L('余额不足')}");return false;
            }else{
                var buynum = parseFloat(buyusdt / newprice).toFixed(4);
                if (buynum == 'NaN') {
					buynum = 0.000;
				}
                $("#buynum").val(buynum);
                $("#musdt").val(buyusdt);
                $("#mnum").val(num);
            }
        }
    </script>
    <script type="text/javascript">
        function sellnumfc(){
            var newbuynum = $("#sell_num").val();
            var newprice = $("#sell_newprice").val();
            var buyusdt = parseFloat((newprice * newbuynum)).toFixed(4);
            var usermoney = parseFloat($("#buy_usercoin").val());
            if(newbuynum > usermoney){
                layer.msg("{:L('余额不足')}");return false;
            }else{
                $("#sell_usdt").val(buyusdt);
                $("#mnum").val(newbuynum);
                $("#musdt").val(buyusdt);
            }
            
        }
        function buynumfc(){
            var newbuynum = $("#buynum").val();
            var newprice = $("#newprice").val();
            var buyusdt = parseFloat((newprice * newbuynum)).toFixed(4);
            var usermoney = parseFloat($("#buy_usermoney").val());
            if(buyusdt > usermoney){
                layer.msg("{:L('余额不足')}");return false;
            }else{
                $("#buyusdt").val(buyusdt);
                $("#mnum").val(newbuynum);
                $("#musdt").val(buyusdt);
            }
            
        }
    </script>
    <script type="text/javascript">
        $(function(){
            var symbol = $("#symbolbox").val();
            $.post("{:U('Ajaxtrade/getnewprice')}",
            {'symbol':symbol},
            function(data){
                if(data.code == 1){
                    $("#newprice").val(data.price);
                    $("#mprice").val(data.price);
                    $("#sell_newprice").val(data.price);
                }else{
                    console.log("{:L('未获取数据')}");
                }
            });
        });


		function order_top_select_action(type){
			$('.order-top-span').removeClass('order-top-select');

			if (type == 1) {
				$('.order-top-current').addClass('order-top-select');
				$('.order-main-table-current').show();
				$('.order-main-table-history').hide();
			}

			if (type == 2) {
				$('.order-top-history').addClass('order-top-select');
				$('.order-main-table-current').hide();
				$('.order-main-table-history').show();
			}

		}

		function clearorder(id){
			var oid = id;
			$.post("{:U('Trade/clearorder')}",
					{"oid":oid},
					function(data){
						if(data.code == 1){
							layer.msg(data.info);
							setTimeout(function(args){
								window.location.reload();
							},2000);
						}else{
							layer.msg(data.info);return false;
						}
					});
		}


    </script>
    <script type="text/javascript">
        function sellcoinfc(){
            var sell_coin = parseFloat($("#sell_coin").val());
            if(sell_coin < 0){
                layer.msg("{:L('请输入正确数量')}");return false;
            }
            var buy_usercoin = parseFloat($("#buy_usercoin").val());
            if(sell_coin > buy_usercoin){
                layer.msg("{:L('余额不足')}");return false;
            }
        }
        
        
        
        function sellblfc(type,num){
            var type = type;
            var num = num;
            var usermoney = $("#buy_usercoin").val();
            var musdt = parseFloat((usermoney * num / 100)).toFixed(4);
            var newprice = $("#newprice").val();
            var buynum = parseFloat((musdt * newprice)).toFixed(4);
            
            $("#sell_usdt").val(buynum);
            $("#sell_num").val(musdt);
            
            $("#musdt").val(buynum);
            $("#mnum").val(musdt);
            $("#sell_coin").val(musdt);
            
            
            if(type == 1){
                $('#sellbl_1').addClass("bgred");
                $('#sellbl_1').removeClass("bgf5");
                $('#sellbl_2').addClass("bgf5");
                $('#sellbl_2').removeClass("bgred");
                $('#sellbl_3').addClass("bgf5");
                $('#sellbl_3').removeClass("bgred");
                $('#sellbl_4').addClass("bgf5");
                $('#sellbl_4').removeClass("bgred");
                
            }else if(type == 2){
                $('#sellbl_2').addClass("bgred");
                $('#sellbl_2').removeClass("bgf5");
                $('#sellbl_1').addClass("bgf5");
                $('#sellbl_1').removeClass("bgred");
                $('#sellbl_3').addClass("bgf5");
                $('#sellbl_3').removeClass("bgred");
                $('#sellbl_4').addClass("bgf5");
                $('#sellbl_4').removeClass("bgred");
            }else if(type == 3){
                $('#sellbl_3').addClass("bgred");
                $('#sellbl_3').removeClass("bgf5");
                $('#sellbl_1').addClass("bgf5");
                $('#sellbl_1').removeClass("bgred");
                $('#sellbl_2').addClass("bgf5");
                $('#sellbl_2').removeClass("bgred");
                $('#sellbl_4').addClass("bgf5");
                $('#sellbl_4').removeClass("bgred");
            }else if(type == 4){
                $('#sellbl_4').addClass("bgred");
                $('#sellbl_4').removeClass("bgf5");
                $('#sellbl_1').addClass("bgf5");
                $('#sellbl_1').removeClass("bgred");
                $('#sellbl_2').addClass("bgf5");
                $('#sellbl_2').removeClass("bgred");
                $('#sellbl_3').addClass("bgf5");
                $('#sellbl_3').removeClass("bgred");
            }
        }
        function buyblfc(type,num){
            var type = type;
            var num = num;
            var usermoney = $("#buy_usermoney").val();
            var musdt = parseFloat((usermoney * num / 100)).toFixed(4);
            var newprice = $("#newprice").val();
            var buynum = parseFloat((musdt / newprice)).toFixed(4);
            $("#buyusdt").val(musdt);
            $("#buynum").val(buynum);
            $("#musdt").val(musdt);
            $("#mnum").val(buynum);
            if(type == 1){
                $('#buybl_1').addClass("bggreen");
                $('#buybl_1').removeClass("bgf5");
                $('#buybl_2').addClass("bgf5");
                $('#buybl_2').removeClass("bggreen");
                $('#buybl_3').addClass("bgf5");
                $('#buybl_3').removeClass("bggreen");
                $('#buybl_4').addClass("bgf5");
                $('#buybl_4').removeClass("bggreen");
                
            }else if(type == 2){
                $('#buybl_2').addClass("bggreen");
                $('#buybl_2').removeClass("bgf5");
                $('#buybl_1').addClass("bgf5");
                $('#buybl_1').removeClass("bggreen");
                $('#buybl_3').addClass("bgf5");
                $('#buybl_3').removeClass("bggreen");
                $('#buybl_4').addClass("bgf5");
                $('#buybl_4').removeClass("bggreen");
            }else if(type == 3){
                $('#buybl_3').addClass("bggreen");
                $('#buybl_3').removeClass("bgf5");
                $('#buybl_1').addClass("bgf5");
                $('#buybl_1').removeClass("bggreen");
                $('#buybl_2').addClass("bgf5");
                $('#buybl_2').removeClass("bggreen");
                $('#buybl_4').addClass("bgf5");
                $('#buybl_4').removeClass("bggreen");
            }else if(type == 4){
                $('#buybl_4').addClass("bggreen");
                $('#buybl_4').removeClass("bgf5");
                $('#buybl_1').addClass("bgf5");
                $('#buybl_1').removeClass("bggreen");
                $('#buybl_2').addClass("bgf5");
                $('#buybl_2').removeClass("bggreen");
                $('#buybl_3').addClass("bgf5");
                $('#buybl_3').removeClass("bggreen");
            }
        }
    </script>
    <script type="text/javascript">
        
        $("#dash_sellprice").click(function(){
            var newprice =  parseFloat($("#sell_newprice").val());
            if(newprice > 0){
                var buyprice = (newprice - 0.001).toFixed(4);
            }else{
                var buyprice = 0;
            }
            $("#sell_newprice").val(buyprice);
            $("#mprice").val(buyprice);
        });
        
        $("#plus_sellprice").click(function(){
            var newprice = parseFloat($("#sell_newprice").val());
            var buyprice = (newprice + 0.001).toFixed(4);
            $("#sell_newprice").val(buyprice);
            $("#mprice").val(buyprice);
        });
        
        $("#dash_sellnum").click(function(){
            var buynum = parseFloat($("#sell_num").val());
            if(buynum > 0){
                var newbuynum = (buynum - 0.001).toFixed(4);
            }else{
                newbuynum = 0
            }
            $("#sell_num").val(newbuynum);
            $("#mnum").val(newbuynum);
            var newprice = $("#newprice").val();
            var buyusdt = (newprice * newbuynum).toFixed(4);
            $("#sell_usdt").val(buyusdt);
            $("#musdt").val(buyusdt);
            
        });
        $("#plus_sellnum").click(function(){
            var buynum = parseFloat($("#sell_num").val());
            if(buynum > 0){
                var newbuynum = (buynum + 0.001).toFixed(4);
            }else{
                var newbuynum = 0.01;
            }
            $("#sell_num").val(newbuynum);
            $("#mnum").val(newbuynum);
            var newprice = $("#newprice").val();
            var usermoney = parseFloat($("#buy_usercoin").val());
            var buyusdt = parseFloat((newprice * newbuynum)).toFixed(4);
            if(newbuynum > usermoney){
                layer.msg("{:L('余额不足')}");return false;
            }else{
                $("#sell_usdt").val(buyusdt);
                $("#musdt").val(buyusdt);
            }
        });
    
        
        $("#dash_buyprice").click(function(){
            var newprice =  parseFloat($("#newprice").val());
            if(newprice > 0){
                var buyprice = (newprice - 0.001).toFixed(4);
            }else{
                var buyprice = 0;
            }
            $("#newprice").val(buyprice);
            $("#mprice").val(buyprice);
        });
        $("#plus_buyprice").click(function(){
            var newprice = parseFloat($("#newprice").val());
            var buyprice = (newprice + 0.001).toFixed(4);
            $("#newprice").val(buyprice);
            $("#mprice").val(buyprice);
        });
        $("#dash_buynum").click(function(){
            var buynum = parseFloat($("#buynum").val());
            if(buynum > 0){
                var newbuynum = (buynum - 0.001).toFixed(4);
            }else{
                newbuynum = 0
            }
            $("#buynum").val(newbuynum);
            $("#mnum").val(newbuynum);
            var newprice = $("#newprice").val();
            var buyusdt = (newprice * newbuynum).toFixed(4);
            $("#buyusdt").val(buyusdt);
            $("#musdt").val(buyusdt);
            
        });
        $("#plus_buynum").click(function(){
            var buynum = parseFloat($("#buynum").val());
            if(buynum > 0){
                var newbuynum = (buynum + 0.001).toFixed(4);
            }else{
                var newbuynum = 0.01;
            }
            $("#buynum").val(newbuynum);
            $("#mnum").val(newbuynum);
            var newprice = $("#newprice").val();
            var usermoney = parseFloat($("#buy_usermoney").val());
            var buyusdt = parseFloat((newprice * newbuynum)).toFixed(4);
            if(buyusdt > usermoney){
                layer.msg("{:L('余额不足')}");return false;
            }else{
                $("#buyusdt").val(buyusdt);
                $("#musdt").val(buyusdt);
            }
        });
    </script>

    <script type="text/javascript">
		$("#nav").slide({ 
			type:"menu",// 效果类型，针对菜单/导航而引入的参数（默认slide）
			titCell:".nLi", //鼠标触发对象
			targetCell:".sub", //titCell里面包含的要显示/消失的对象
			effect:"slideDown", //targetCell下拉效果
			delayTime:300 , //效果时间
			triggerTime:0, //鼠标延迟触发时间（默认150）
			returnDefault:true //鼠标移走后返回默认状态，例如默认频道是“预告片”，鼠标移走后会返回“预告片”（默认false）
		});
	</script>

    
</html>