<include file="Public:header"/>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title">用户资产管理</span>
			<div class="fr">
			    <button class="btn btn-warning" onClick="location.href='{:U('User/coin')}'">初始化搜索</button>
			</div>
		</div>
		<div class="cf">
			
			<div class="search-form fl cf">
				<div class="sleft">
					<form name="formSearch" id="formSearch" method="get" name="form1">

						<select style=" width: 160px; float: left; margin-right: 10px;" name="field" class="form-control">
							<option value="username"
							<eq name="Think.get.field" value="username">selected</eq>
							>用户名</option>
						</select>
						<input type="text" name="name" class="search-input form-control  " value="{$Think.get.name}" placeholder="请输入查询内容" style="">
						<a class="sch-btn" href="javascript:;" id="search"> <i class="btn-search"></i> </a>
					</form>
					<script>
						//搜索功能
						$(function () {
							$('#search').click(function () {
								$('#formSearch').submit();
							});
						});
						//回车搜索
						$(".search-input").keyup(function (e) {
							if (e.keyCode === 13) {
								$("#search").click();
								return false;
							}
						});
					</script>
				</div>
			</div>
		</div>
		<div class="data-table table-striped">
			<table class="">
				<thead>
				<tr>
					<th class="row-selected row-selected"><input class="check-all" type="checkbox"/></th>
					<th class="">ID</th>
					<th class="">用户名</th>
					<volist name="coinlist" id="v">
						<th class="">{$v.title}(<?php echo strtoupper($v['name']);?>)</th>
					</volist>
				</tr>
				</thead>
				<tbody>
				<notempty name="list">
					<volist name="list" id="vo">
						<tr>
							<td><input class="ids" type="checkbox" name="id[]" value="{$vo.id}"/></td>
							<td>{$vo.id}</td>
							<td>{$vo['username']}</td>
							
							<volist name="coinlist" id="v">
								<td>
								    可用：{$vo[$v['name']] * 1}<br>冻结：
									<php>echo $vo[$v['name'].'d'] * 1;</php>
									<br> 总计：
									<php>echo $vo[$v['name'].'d'] * 1 + $vo[$v['name']] * 1;</php>
									<br>
								</td>
							</volist>
						</tr>
					</volist>
					<else/>
					<td colspan="12" class="text-center empty-info"><i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据</td>
				</notempty>
				</tbody>
			</table>
			<div class="page">
				<div>{$page}</div>
			</div>
		</div>
	</div>
</div>
<include file="Public:footer"/>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('User/coin')}");
	</script>
</block>