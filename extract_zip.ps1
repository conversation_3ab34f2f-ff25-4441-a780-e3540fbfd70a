Add-Type -AssemblyName System.IO.Compression.FileSystem

try {
    $zipPath = "downloaded_file.zip"
    $extractPath = "."
    $password = "www.fxe.cc"
    
    # Try to open the ZIP file
    $zip = [System.IO.Compression.ZipFile]::OpenRead($zipPath)
    
    foreach ($entry in $zip.Entries) {
        $destinationPath = Join-Path $extractPath $entry.FullName
        
        # Create directory if it doesn't exist
        $destinationDir = Split-Path $destinationPath -Parent
        if (![string]::IsNullOrEmpty($destinationDir) -and !(Test-Path $destinationDir)) {
            New-Item -ItemType Directory -Path $destinationDir -Force | Out-Null
        }
        
        # Extract file
        if (!$entry.FullName.EndsWith('/')) {
            Write-Host "Extracting: $($entry.FullName)"
            [System.IO.Compression.ZipFileExtensions]::ExtractToFile($entry, $destinationPath, $true)
        }
    }
    
    $zip.Dispose()
    Write-Host "Extraction completed successfully!"
    
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "This might be a password-protected or corrupted ZIP file."
    
    # Try alternative method for password-protected files
    try {
        Add-Type -AssemblyName System.IO.Compression
        $fileStream = [System.IO.File]::OpenRead($zipPath)
        $zipArchive = [System.IO.Compression.ZipArchive]::new($fileStream)
        
        foreach ($entry in $zipArchive.Entries) {
            $destinationPath = Join-Path $extractPath $entry.FullName
            
            if (!$entry.FullName.EndsWith('/')) {
                Write-Host "Attempting to extract: $($entry.FullName)"
                $entryStream = $entry.Open()
                $destinationDir = Split-Path $destinationPath -Parent
                if (![string]::IsNullOrEmpty($destinationDir) -and !(Test-Path $destinationDir)) {
                    New-Item -ItemType Directory -Path $destinationDir -Force | Out-Null
                }
                $fileStream2 = [System.IO.File]::Create($destinationPath)
                $entryStream.CopyTo($fileStream2)
                $fileStream2.Close()
                $entryStream.Close()
            }
        }
        
        $zipArchive.Dispose()
        $fileStream.Close()
        Write-Host "Alternative extraction method completed!"
        
    } catch {
        Write-Host "Alternative method also failed: $($_.Exception.Message)"
        Write-Host "You may need to install 7-Zip, WinRAR, or another tool that supports password-protected ZIP files."
    }
}
