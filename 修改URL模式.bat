@echo off
chcp 65001 >nul
echo ========================================
echo          修改URL模式为兼容模式
echo ========================================
echo.

echo 如果伪静态无法正常工作，可以使用此脚本
echo 将URL模式改为兼容模式（不需要伪静态）
echo.
echo 当前URL模式：2 (重写模式，需要伪静态)
echo 兼容模式：1 (PATHINFO模式，不需要伪静态)
echo.
set /p choice="是否要修改为兼容模式？(y/n): "

if /i "%choice%"=="y" (
    echo.
    echo 正在修改配置文件...
    
    REM 备份原文件
    copy "C:\coin\jys\Application\Common\Conf\config.php" "C:\coin\jys\Application\Common\Conf\config.php.backup" >nul
    
    REM 这里需要手动修改，因为批处理难以精确替换PHP数组内容
    echo.
    echo 请手动修改以下文件：
    echo C:\coin\jys\Application\Common\Conf\config.php
    echo.
    echo 找到这一行：
    echo 'URL_MODEL' =^> 2,
    echo.
    echo 修改为：
    echo 'URL_MODEL' =^> 1,
    echo.
    echo 修改后的URL格式将是：
    echo http://localhost/index.php/Trade/tradelist
    echo 而不是：
    echo http://localhost/Trade/tradelist
    echo.
    echo 修改完成后重新访问网站即可
    
) else (
    echo 取消修改
)

echo.
pause
