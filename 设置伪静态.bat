@echo off
chcp 65001 >nul
echo ========================================
echo          设置伪静态规则
echo ========================================
echo.

echo [1/3] 检查Apache配置...

REM 检查Apache是否运行
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if not "%ERRORLEVEL%"=="0" (
    echo [错误] Apache服务未运行
    echo 请启动phpStudy并启动Apache服务
    pause
    exit /b 1
)

echo Apache服务运行中... ✓

echo [2/3] 创建虚拟主机配置...

REM 创建虚拟主机配置
(
echo ^<VirtualHost *:80^>
echo     DocumentRoot "C:/coin/jys"
echo     ServerName localhost
echo     ^<Directory "C:/coin/jys"^>
echo         Options Indexes FollowSymLinks
echo         AllowOverride All
echo         Require all granted
echo     ^</Directory^>
echo ^</VirtualHost^>
) > "C:\phpstudy_pro\Extensions\Apache2.4.39\conf\vhosts\exchange.conf"

echo 虚拟主机配置创建完成... ✓

echo [3/3] 重启Apache服务...
echo.
echo 请手动重启phpStudy中的Apache服务以应用配置
echo.
echo ========================================
echo            设置完成！
echo ========================================
echo.
echo 伪静态规则已设置：
echo - .htaccess文件已创建
echo - 虚拟主机配置已添加
echo - 需要重启Apache服务
echo.
echo 重启Apache后，以下URL应该可以访问：
echo - http://localhost/Trade/tradelist
echo - http://localhost/Admin
echo - http://localhost/Login
echo.
echo 如果仍然无法访问，请：
echo 1. 确认phpStudy中Apache已重启
echo 2. 检查Apache错误日志
echo 3. 尝试修改URL模式为兼容模式
echo.
pause
