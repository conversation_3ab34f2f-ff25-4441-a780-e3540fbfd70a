<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/base2.css" />
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/nologed.css" />
	<link rel="stylesheet" href="/Public/Static/Icoinfont/iconfont.css">
	<script src="/Public/Static/Icoinfont/iconfont.js"></script>
	<title>{$webname}</title>
	<style>
	::-webkit-input-placeholder {color: #b5b5b5;font-size: 12px;}
	::-moz-placeholder {color: #b5b5b5;font-size: 12px;}
	input:focus{background:#fff;outline: 1px solid #fff;}
	a:hover,a:link,a:visited,a:active{color:#000000;text-decoration:none;}
	.no_header{position: fixed;z-index: 9999;padding:0px 10px;top:0px;height: 45px;box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);}
	.txtl{line-height:50px;width:20%;text-align: center;border-bottom: 3px solid #FCD535;}
    .titlebox{width:100%;height:120px;background:#fff;margin-top:55px;border-top-left-radius:20px;border-top-right-radius:20px;}
    .allmoneybox{
        width: 96%;
        height: 200px;
        background: url(/Public/Static/img/icon_bg_zc.65aa32a1.png) no-repeat;
        background-size: 100% 200px;
        box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
        margin: 0px 2%;
        border-radius: 10px;
        margin-top: 10px;
    }
    .allmbox_1{width:100%;height:40px;line-height:40px;text-align:right;padding:0px 15px;}
    .allmbox_2{width:100%;height:20px;line-height:20px;text-align:left;padding:0px 15px;}
    .allmbox_3{width:100%;height:40px;line-height:40px;text-align:left;padding:0px 15px;}
    .allmbox_btn{width:100%;height:35px;line-height:35px;padding:0px 10px;}
    .allmbox_btn_1{width:100%;height:35px;float:left;margin-top: 50px;}
    .allmbox_cbtn{width:30%;height:35px;line-height:35px;text-align:center;float:left;border-radius:5px;}
    .allmbox_tbtn{width:30%;height:35px;line-height:35px;text-align:center;float:left;border-radius:5px;margin-left: 5%;}
    .btn_active{background: #fff;}
    .findbox{
        width: 100%;
        background: #121420;
        padding: 0 10px;
    }
    .findbox_1{width:100%;height:40px;line-height:40px;padding:0px 15px;}
    .findbox_2{width:60%;height:40px;line-height:40px;float:left;}
    .findbox_3{height:40px;border-bottom:2px solid #00b897;float: left;text-align:center;}
    .mlistbox{width:100%;}
    .listbox{
        width: 100%;
        height: 90px;
        background: #1b1d2a;
        padding: 5px 15px;
        margin-bottom: 5px;
        border-radius: 10px;
    }
    .listbox_title{width:100%;height:30px;line-height:30px;}
    .listbox_title_l{width:50%;height:30px;line-height:40px;float:left;text-align:left;}
    .listbox_title_r{width:50%;height:30px;line-height:40px;float:right;text-align:right;}
    .dongbox{position:fixed;z-index:9999999999;display:none;top:0px;width:100%;height:100vh;background:rgba(0,0,0,0.2);}
    .dongbox_cbox{width:75%;background:#fff;margin:50% auto;border-radius:15px;padding:10px;}
    .showbtn{width:100%;height:40px;line-height:40px;background: #0052fe;color: #fff;border-radius:5px;text-align:center;margin-top:10px;}

	</style>
  </head>
  <body>
	<div class="container-fluid " style="padding:0px;width:100vw;">

        <div class="allmoneybox">
            <div class="allmbox_1">
                <input type="hidden" id="st" value="1" />
                <i class="bi bi-eye " style="font-size:22px;color: #fff"  id="eyesid"></i>
            </div>
            <div class="allmbox_2">
                <span class="fzmm fe6im" style="color: #fff">{:L('账户总资产折合')}(USDT)</span>
                <i class="bi bi-exclamation-circle" style="font-size:16px;color: #fff;" id="showdongbtn"></i>
            </div>
            <div class="allmbox_3">
                <span class="fe6im zhehebox" style="font-size:24px;color: #fff" id="allzhehebox">--</span>
            </div>
            
            <div class="allmbox_btn">
                <div class="allmbox_btn_1">
                    <a href="{:U('User/czcoin')}">
                    <div class="allmbox_cbtn ">
                        <img src="/Public/Static/Icoinfont/icon/chongzhi.png" width="23" style="margin-right: 5px;" />
                        <span class="fe6im">{:L('充币')}</span>
                    </div>
                    </a>

                    <a href="{:U('User/txcoin')}">
                    <div class="allmbox_tbtn " >
                        <img src="/Public/Static/Icoinfont/icon/tixin.png" width="23" style="margin-right: 5px;"  />
                        <span class="fe6im">{:L('提币')}</span>
                    </div>
                    </a>

                    <a href="https://buy.ramp.network/">
                        <div class="allmbox_tbtn " >
                            <img src="/Public/Static/Icoinfont/icon/buy.png" width="23" style="margin-right: 5px;"  />
                            <span class="fe6im">{:L('买币')}</span>
                        </div>
                    </a>

                </div>
            </div>
            
        </div>
        
        <div class="findbox">
            <div class="findbox_1">
                <div class="findbox_2">
                    <div class="findbox_3">
                        <span class="fzmm fe6im">{:L('资产列表')}</span>
                    </div>
                </div>
            </div>
            
            <div class="mlistbox" id="moneylistbox">
                <foreach name="list" item="vo">
                <a href="/User/coininfo?cid={$vo.id}">
                    <div class="listbox">
                        <div class="listbox_title">
                            <div class="listbox_title_l">
                                <span class="fe6im"  style="font-size:16px;font-weight:500;"><?php echo strtoupper($vo['name']);?></span>
                            </div>
                            <div class="listbox_title_r">
                                <i class="bi bi-chevron-right fcc" style="font-size:14px;"></i>
                            </div>
                        </div>

                        <div style="width:100%;height:60px;">
                            <div style="width:33.33%;height:60px;float:left;">
                                <div style="width:100%;height:30px;line-height:40px;">
                                    <span class="fcc">{:L("可用")}</span>
                                </div>
                                <div style="width:100%;height:30px;line-height:20px;">
                                    <span class="fe6im f12" id="num_{$vo.name}">0.000000</span>
                                </div>
                            </div>
                            <div style="width:33.33%;height:60px;float:left;">
                                <div style="width:100%;height:30px;line-height:40px;">
                                    <span class="fcc">{:L("冻结")}</span>
                                </div>
                                <div style="width:100%;height:30px;line-height:20px;">
                                    <span class="fe6im f12" id="numd_{$vo.name}">0.000000</span>
                                </div>
                            </div>
                            <div style="width:33.33%;height:60px;float:left;">
                                <div style="width:100%;height:30px;line-height:40px;text-align:right;">
                                    <span class="fcc">{:L("折合")}(USDT)</span>
                                </div>
                                <div style="width:100%;height:30px;line-height:20px;text-align:right;">
                                    <span class="fe6im f12" id="zhehe_{$vo.name}">0.000000</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
                </foreach>
                
 
            </div>
        </div>
        
        <div style="width:100%;height:80px;"></div>

	</div>	
	
	
	<!---提醒弹窗--->
	<div class="dongbox" id="dongbox" style="display:none;">
	    <div class="dongbox_cbox">
	        <div style="width:100%;height:50px;line-height:50px;text-align:center;">
	            <i class="bi bi-exclamation-circle-fill fcy" style="font-size:36px;"></i>
	        </div>
	        <div style="width:100%;;padding:5px 10px;">
	            <span class="fzmm fe6im">{:L('由于资金统计存在延时，账户余额不一定是并非最新余额，如需准确余额请到相关账户页面进行查询')}</span>
	        </div>
	        <div class="showbtn" id="hidebtn">
	            <span class="fzmm fe6im">{:L('确定')}</span>
	        </div>
	    </div>
	    
	</div>
	

	<!--底部-->
    <div class="footer">

        <a href="{:U('Index/index')}">
            <div class="footer_op">
                <div class="f_op_t" style="line-height: 35px;" >
                    <?php
					if($select == 'index') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-1-active.png" style="width: 25px;" >';
                    }else{
                    echo '<img src="/Public/Static/Icoinfont/icon/nav-1.png" style="width: 25px;" >';
                    }
                    ?>
                </div>
                <div class="f_op_b">
                    <span class="fzm fcch">{:L('首页')}</span>
                </div>
            </div>
        </a>

        <a href="{:U('Trade/index')}">
            <div class="footer_op">
                <div class="f_op_t" style="line-height:35px;">
                    <?php
					if($select == 'trade') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-2-active.png" style="width: 25px;" >';
                    }else{
                    echo '<img src="/Public/Static/Icoinfont/icon/nav-2.png" style="width: 25px;" >';
                    }
                    ?>
                </div>
                <div class="f_op_b">
                    <span class="fzm fcch">{:L('行情')}</span>
                </div>
            </div>
        </a>

        <a href="/Trade/transinfo.html?symbol=BTC&type=buy">
            <div class="footer_op">
                <div class="f_op_t" style="line-height:35px;">
                    <?php
					if($select == 'trans') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-3-active.png" style="width: 25px;" >';
                    }else{
                    echo '<img src="/Public/Static/Icoinfont/icon/nav-3.png" style="width: 25px;" >';
                    }
                    ?>
                </div>
                <div class="f_op_b">
                    <span class="fzm fcch">{:L('交易')}</span>
                </div>
            </div>
        </a>

        <a href="{:U('Contract/index')}">
            <div class="footer_op">
                <div class="f_op_t" style="line-height:35px;">
                    <?php
					if($select == 'contract') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-4-active.png" style="width: 25px;" >';
                    }else{
                    echo '<img src="/Public/Static/Icoinfont/icon/nav-4.png" style="width: 25px;" >';
                    }
                    ?>
                </div>
                <div class="f_op_b">
                    <span class="fzm fcch">{:L('合约')}</span>
                </div>
            </div>
        </a>

        <a href="{:U('User/index')}">
            <div class="footer_op">
                <div class="f_op_t" style="line-height:35px;">
                    <?php
					if($select == 'user') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-5-active.png" style="width: 25px;" >';
                    }else{
                    echo '<img src="/Public/Static/Icoinfont/icon/nav-5.png" style="width: 25px;" >';
                    }
                    ?>
                </div>
                <div class="f_op_b">
                    <span class="fzm fcy">{:L('资产')}</span>
                </div>
            </div>
        </a>


    </div>


</body>

<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
<script type="text/javascript" src="/Public/Static/js/layer/layer.js" ></script>
<script type="text/javascript">
    function getmoney_usdt(){
        var coin = "usdt";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyusdt')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_btc(){
        var coin = "btc";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneybtc')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_eth(){
        var coin = "eth";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyeth')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_eos(){
        var coin = "eos";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyeos')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_doge(){
        var coin = "doge";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneydoge')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_bch(){
        var coin = "bch";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneybch')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_ltc(){
        var coin = "ltc";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyltc')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_trx(){
        var coin = "trx";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneytrx')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_xrp(){
        var coin = "xrp";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyxrp')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_iotx(){
        var coin = "iotx";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyiotx')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_fil(){
        var coin = "fil";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyfil')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_shib(){
        var coin = "shib";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyshib')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_flow(){
        var coin = "flow";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyflow')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_jst(){
        var coin = "jst";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyjst')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_itc(){
        var coin = "itc";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyitc')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_ht(){
        var coin = "ht";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyht')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_ogo(){
        var coin = "ogo";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyogo')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }
    function getmoney_usdz(){
        var coin = "usdz";
        var numid = "#num_" + coin;
        var numdid = "#numd_" + coin;
        var zheheid = "#zhehe_" + coin;
        $.post("{:U('User/getmoneyusdz')}",
        {'coin':coin},
        function(data){
            if(data.code == 1){
                $(numid).html(data.num);
                $(numdid).html(data.numd);
                $(zheheid).html(data.zhe);
            }else{
                console.log("error");
            }
        }
        );
    }

</script>
<script type="text/javascript">
    $(function(){
        setInterval("getmoney_usdz()",27000);
        setInterval("getmoney_ogo()",25000);
        setInterval("getmoney_ht()",23000);
        setInterval("getmoney_itc()",21000);
        setInterval("getmoney_jst()",19000);
        setInterval("getmoney_flow()",19000);
        setInterval("getmoney_shib()",17000);
        setInterval("getmoney_fil()",17000);
        setInterval("getmoney_iotx()",15000);
        setInterval("getmoney_xrp()",13000);
        setInterval("getmoney_trx()",11000);
        setInterval("getmoney_ltc()",9000);
        setInterval("getmoney_bch()",7000);
        setInterval("getmoney_doge()",5000);
        setInterval("getmoney_eos()",5000);
        setInterval("getmoney_eth()",3000);
        setInterval("getmoney_btc()",2000);
        getmoney_btc();
        setInterval("getallzhehe()",2000);
        getallzhehe();
        getmoney_usdt();
    });
</script>
<script type="text/javascript">
    function getallzhehe(){
        $.post("{:U('User/getallzhehe')}",
        function(data){
            if(data.code == 1){
                $("#allzhehebox").html(data.allzhehe);
            }
        });
    }
</script>
<!--
<script type="text/javascript">
    function getusermoney(){
        $.post("{:U('User/getmoneyinfo')}",
        function(data){
            if(data.code == 1){
                $("#moneylistbox").empty();
                var html = '';
                if(data.data == '' || data.data == null){
                    html = '<div style="width:100%;height:100px;line-height:100px;text-align:center;">'+ 
                           '<span class="fzmm fcc">' + "{:L('没有获取数据')}" + '</span>'+
                           '</div>';
                    $("#moneylistbox").append(html);
                    
                }else{
                    $.each(data.data,function(key,val){
                        html += '<a href="/User/coininfo?cid='+ val.cid +'">'+
                                '<div class="listbox">'+
                                '<div class="listbox_title">'+
                                '<div class="listbox_title_l">'+
                                '<span class="fcc" style="font-size:16px;font-weight:500;">' + val.cname + '</span>'+
                                '</div>'+
                                '<div class="listbox_title_r">'+
                                '<i class="bi bi-chevron-right fcc" style="font-size:14px;"></i>'+
                                '</div>'+
                                '</div>'+
                                '<div style="width:100%;height:60px;">'+
                                '<div style="width:33.33%;height:60px;float:left;">'+
                                '<div style="width:100%;height:30px;line-height:40px;">'+
                                '<span style="color:#cbcbcb;">{:l("可用")}</span>'+
                                '</div>'+
                                '<div style="width:100%;height:30px;line-height:20px;">'+
                                '<span class="fe6im">' + val.num + '</span>'+
                                '</div>'+
                                '</div>'+
                                '<div style="width:33.33%;height:60px;float:left;">'+
                                '<div style="width:100%;height:30px;line-height:40px;">'+
                                '<span style="color:#cbcbcb;">{:l("冻结")}</span>'+
                                '</div>'+
                                '<div style="width:100%;height:30px;line-height:20px;">'+
                                '<span class="fe6im">' + val.numd + '</span>'+
                                '</div>'+
                                '</div>'+
                                '<div style="width:33.33%;height:60px;float:left;">'+
                                '<div style="width:100%;height:30px;line-height:40px;text-align:right;">'+
                                '<span style="color:#cbcbcb;">{:l("折合")}(USDT)</span>'+
                                '</div>'+
                                '<div style="width:100%;height:30px;line-height:20px;text-align:right;">'+
                                '<span class="fe6im">' + val.zhe + '</span>'+
                                '</div>'+
                                '</div>'+
                                '</div>'+
                                '</div>'+
                                '</a>';
                    });
                    $("#moneylistbox").append(html);
                }
            }else{
                html =  '<div style="width:100%;height:100px;line-height:100px;text-align:center;">'+ 
                        '<span class="fzmm fcc">' + "{:L('没有获取数据')}" + '</span>'+
                        '</div>';
                $("#moneylistbox").append(html);
            }
        });
    }
</script>
---->

<script type="text/javascript">
    function goback(){
        window.history.go(-1);
    }
    $("#eyesid").click(function(){
        var st = $("#st").val();
        if(st == 1){
            $("#eyesid").removeClass("bi-eye");
            $("#eyesid").addClass("bi-eye-slash");
            $("#st").val(2);
            $(".zhehebox").html("******.******");
            $(".zhehebox").removeAttr("id");
            
        }else if(st == 2){
            $("#eyesid").addClass("bi-eye");
            $("#eyesid").removeClass("bi-eye-slash");
            $("#st").val(1);
            $(".zhehebox").attr("id","allzhehebox");
            
        }
    });
    $("#hidebtn").click(function(){
        $("#dongbox").hide();
    });
    $("#showdongbtn").click(function(){
        $("#dongbox").show();
    });
</script>

</html>



