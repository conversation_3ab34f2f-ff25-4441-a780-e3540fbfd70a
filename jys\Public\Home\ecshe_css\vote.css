@charset "utf-8";
/* CSS Document */

body{margin:0;padding:0;font-size:12px;line-height:22px;font-family:"Microsoft YaHei",Arial;-webkit-text-size-adjust:none;}
html,body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td,p{margin:0;padding:0;}
input,select,textarea{font-size:12px;line-height:16px;}img{border:0;}ul,li{list-style-type:none;}
a{color:#333;text-decoration:none;}
.tc{text-align:center;}.tl{text-align:left;}.tr{text-align:right;}
.dis{display:block;}.undis{display:none;}
.fl{float:left;}.fr{float:right;}.cl{clear:both;}.fb{font-weight:bold;}.fnb{font-weight:200;margin-left:-1px;}
.hr_1,.hr_10{font-size:1px;line-height:1px;clear:both;}
.hr_1{height:1px;}.hr_10{height:10px;}
input[type="text"]:focus,input[type="password"]:focus,textarea:focus{outline:none;}

.banner {
	height: 320px;
}
.banner .boxs {
	position: relative;
	margin: 0 auto;
	width: 1200px;
	height: 320px;
	overflow: hidden;
	color: #fff;
}
.banner .boxs .font_title {
	position: absolute;
	top: 50%;
	left: 50%;
	margin: 0 auto;
	width: 1000px;
	height: 100px;
	margin-left: -500px;
	margin-top: -50px;
	text-align: center;
}
.banner .boxs h3 {
	margin-bottom: 26px;
	line-height: 40px;
	font-size: 40px;
	font-weight: 500;
}
.banner .boxs p {
	font-size: 16px;
}


.main_boxs {
	margin: 0 auto;
	width: 1200px;

}


.introduce {
	margin-top: 30px;
	height: 100px;
	overflow: hidden;
	background-color: #fff;
}
.introduce a.btn {
	display: inline-block;
	margin-left: 25px;
    width: 150px;
    height: 45px;
    line-height: 45px;
    font-size: 15px;
	text-align: center;
	border-radius: 6px;
	border: #d9d9d9 solid 1px;
}

.list_financing {
	margin: 30px 0;
}
.list_financing li {
	position: relative;
	margin-bottom: 20px;
	width: 100%;
	height: 100px;
	background-color: #fff;
	box-sizing: border-box;
}
.list_financing li:hover {
    -webkit-box-shadow: 0 15px 30px rgba(0,0,0,0.03);
    box-shadow: 0 15px 30px rgba(0,0,0,0.03);
    -webkit-transform: translate3d(0, -2px, 0);
    transform: translate3d(0, -2px, 0);
}

.list_financing .fin-title {
	width: 100%;
	height: 80px;
	line-height: 80px;
	overflow: hidden;
	font-size: 25px;
	border-bottom: 1px dashed #dadada;
	text-align: center;
}
.list_financing .fin-title b {
	font-weight: 500;
}

.list_financing .fin-box {
	padding-top: 25px;
	width: 1000px;
	box-sizing: border-box;
}


.list_financing .fin-box .fin-sort-li {
	float: left;
	width: 100px;
	height: 45px;
	line-height: 45px;
	font-size:1.3rem;
	text-align: center;
}
.list_financing .fin-box .fin-sort-li span {
	display: block;
	margin-left: 25px;
	width: 50px;
	background-color: #efefef;
	box-sizing: border-box;
}

.list_financing .fin-box .fin-title-li {
	float: left;
	width: 30%;
	line-height: 45px;
}
.list_financing .fin-box .fin-title-li img {
	margin-right: 10px;
	height: 35px;
	vertical-align: middle;
}
.list_financing .fin-box .fin-title-li p {
	display: inline-block;
	
}
.list_financing .fin-box .fin-title-li h3 {
	display: inline-block;
	vertical-align: middle;
	margin-right: 10px;
	font-size: 1.3rem;
	text-transform: uppercase;
}
.list_financing .fin-box .fin-title-li span {
	vertical-align: middle;
	font-size: 0.8rem;
}

.list_financing .fin-box .fin-box-li {
	float: left;
	width: 20%;
	text-align:center;
}
.list_financing .fin-box .fin-box-li b {
	display: block;
	margin-bottom: 6px;
	height: 22px;
	font-size: 22px;
	font-weight: 500;
	color: #333;
	text-transform: uppercase;
}
.list_financing .fin-box .fin-box-li b.orange {
	color: #ff6c03;
}
.list_financing .fin-box .fin-box-li b .unit {
	font-size: 16px;
}
.list_financing .fin-box .fin-box-li p {
	font-size: 14px;
	color: #888;
}

.list_financing .fin-btn a {
	display: block;
	margin-top: 28px;
	margin-right: 25px;
    width: 150px;
    height: 45px;
	line-height: 45px;
    font-size: 15px;
    color: #fff;
    background: #bab9b9;
    border: none;
    outline: none;
    border-radius: 6px;
	text-align: center;
    cursor: auto;
    transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}
.list_financing .fin-btn .opens {
	cursor: pointer;
	background: linear-gradient(to right, #4286da, #01abab);
}
.list_financing .fin-btn .opens:hover {
	background: linear-gradient(to right, #5094e8, #07bbbb);
}


/* Floating window */
.float_win_pay {
	margin: 0 auto;
	min-height: 200px;
	padding-bottom: 15px;
    background: #fff;
    border-radius: 10px;
}
.float_win_pay .tan_title {
	padding: 0 40px;
    height: 60px;
	background-color: #394aa9;
	border-radius: 10px 10px 0 0;
}
.float_win_pay .tan_title h4 {
    font-weight: normal;
    color: #fff;
	font-size: 20px;
    line-height: 60px;
    float: left;
}
.float_win_pay .tan_title .close-btn {
    display: block;
    float: right;
    line-height: 60px;
	color: #fff;
	font-size: 1.2rem;
	font-weight: 600;
    cursor: pointer;
	border-radius: 1rem;
	transition: all 0.2s ease-in-out;
}

.float_win_pay .payment_content{
	min-width: 450px;
	margin: 30px 0 0 0;
	padding: 0 40px;
	box-sizing: border-box;
}
.float_win_pay .payment_content ul li{
	margin-bottom: 17px;
}
.float_win_pay .payment_content ul li .label-1{
	display: inline-block;
	padding-right: 15px;
	width: 30%;
	height: 42px;
    line-height: 42px;
    font-size: 14px;
	text-align: right;
	box-sizing: border-box;
}
.float_win_pay .payment_content ul li .input-1{
	padding-left: 10px;
    padding-right: 10px;
	width: 65.5%;
	height: 42px;
    line-height: 42px;
	font-size: 16px;
	color: #333;
	border:1px solid #D4D4D4;
    border-radius: 3px;
	transition: all,.3s;
	box-sizing: border-box;
}
.float_win_pay .payment_content ul li .input-2{
	padding-left: 10px;
    padding-right: 10px;
	width: 65.5%;
	height: 42px;
    line-height: 42px;
	font-size: 16px;
	color: #333;
	border: none;
    border-radius: 3px;
	transition: all,.3s;
	box-sizing: border-box;
	cursor: default;
}
.float_win_pay .payment_content ul li .vcode-1{
	display: inline-block;
	padding-left: 10px;
    padding-right: 10px;
	width: 35%;
	height: 42px;
    line-height: 42px;
	font-size: 16px;
	color: #333;
	border:1px solid #D4D4D4;
    border-radius: 3px;
	transition: all,.3s;
}
.float_win_pay .payment_content ul li .btns{
	margin: 0 auto;
	display: block;
	width: 200px;
	height: 50px;
	line-height: 50px;
	text-align: center;
	font-size: 16px;
	color: #fff;
	background-color: #4f64dc;
	border: 1px solid #6378f1;
	border-radius: 1000px;
    transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
	cursor: pointer;
}
.float_win_pay .payment_content ul li .btns:hover{
	color: #fff;
	background-color: #6378f1;
	border: 1px solid #6378f1;
}
.float_win_pay .payment_content ul li .code-num{
	display: inline-block;
	margin-left: 10px;
	width: 26%;
	height: 40px;
	line-height: 40px;
	text-align: center;
	font-size: 13px;
	color: #fff;
	background-color: #4f64dc;
	border: none;
	outline: none;
	border-radius: 4px;
    transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
	cursor: pointer;
}
.float_win_pay .payment_content ul li .code-num:hover{
	color: #fff;
	background-color: #6378f1;
}
.float_win_pay .payment_content ul li p.forget{
	font-size: 14px;
}
.float_win_pay .payment_content ul li p.forget a{
	color: #0093ff;
}


.white_style .tan_title{
	padding: 0 25px;
	background-color: #fff;
}
.white_style .tan_title h4{
	color: #333;
}
.white_style .tan_title .close-btn{
	color: #a5adb8;
}
.white_style .payment_content{
	margin-top: 10px;
    padding: 0 40px;
}

.white_style .imgname{
	text-align: center;
}
.white_style .imgname img{
	vertical-align: middle;
}
.white_style .imgname div h3{
	display: inline-block;
	margin-right: 5px;
	font-size: 16px;
	color: #333;
	vertical-align: middle;
	text-transform: uppercase;
}
.white_style .imgname div span{
	font-size: 12px;
	color: #8a9099;
	vertical-align: middle;
}

.white_style .input100{
	position: relative;
}
.white_style .input100 .input-1{
	width: 100%!important;
	text-align: center;
}
.white_style .input100 label{
	position: absolute;
	top: 50%;
	right: 0;
	-webkit-transform: translateY(-50%);
    transform: translateY(-50%);
	z-index: 1;
	font-size: 16px;
	color: #999;
	padding-right: 16px;
}

.white_style .explain p{
	color: #8a9099;
	text-transform: uppercase;
}
.white_style .explain p b{
	font-size: 13px;
	color: #000;
	margin-right: 5px;
}
.white_style .explain p b.bx{
	color: red;
}

.white_style .xzbank{
	padding-left: 20px;
	width: 100%;
	height: 42px;
	line-height: 42px;
	font-size: 16px;
	color: #333;
	border:1px solid #D4D4D4;
	border-radius: 3px;
	transition: all,.3s;
	box-sizing: border-box;
	text-align: center;
	text-align-last: center;
}


/* Currency */
.UserBox {
	margin: 0 auto;
	width: 1200px;
	overflow: hidden;
	height: 100%;
}

/** SettingRight **/
.SettingRight {
	overflow: hidden;
	padding: 10px;
	width: 78%;
	min-height: 300px;
	height: 100%;
	background-color: #fff;
	border: 1px solid #e8e8e8;
	box-sizing: border-box;
}

.SettingRight .titles{
	margin: 20px 7px;
	
	height: 20px;
	line-height: 20px;
	text-align: left;
}
.SettingRight .titles h3{
	min-width: 100px;
	padding-left: 10px;
	font-size: 18px;
	font-weight: 500;
	border-left: 3px solid #ffad0e;
}
.SettingRight .titles .btns{
	margin-top: -7px;
	display: block;
	width: 85px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	font-size: 13px;
	color: #fff;
	background-color: #4f64dc;
	border: 1px solid #6378f1;
	border-radius: 1000px;
	text-transform: uppercase;
    transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
	cursor: pointer;
}
.SettingRight .titles .btns:hover{
	color: #fff;
	background-color: #6378f1;
	border: 1px solid #6378f1;
}


.SettingRight .Column_Security{
	margin-bottom: 15px;
	overflow: hidden;
	text-align: center;
}

.SettingRight .Column_Security .sc_status{
    margin: 0 7px;
	padding: 30px 0;
	width: 31.5%;
	border: 1px solid #DEDEDE;
	border-radius: 6px;
}
.SettingRight .Column_Security .sc_status h3{
	line-height: 35px;
	font-size: 16px;
	color: #333;
}
.SettingRight .Column_Security .sc_status p{
	line-height: 25px;
	font-size: 13px;
	color: #888;
}
.SettingRight .Column_Security .sc_status .btns{
	margin:0 auto;
	margin-top: 15px;
	display: block;
	width: 112px;
	height: 40px;
	line-height: 40px;
	font-size: 15px;
	color: #666;
    background-color: #fff;
	border: 1px solid #DEDEDE;
	border-radius: 1000px;
	transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
	cursor: pointer;
}
.SettingRight .Column_Security .sc_status .btnson{
	margin:0 auto;
	margin-top: 15px;
	display: block;
	width: 112px;
	height: 40px;
	line-height: 40px;
	font-size: 15px;
	color: #fff;
	background-color: #4f64dc;
	border: 1px solid #6378f1;
	border-radius: 1000px;
	text-transform: uppercase;
    transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
	cursor: pointer;
}
.SettingRight .Column_Security .sc_status .btnson:hover,.SettingRight .Column_Security .sc_status .btns:hover{
	color: #fff;
	background-color: #6378f1;
	border: 1px solid #6378f1;
}
.SettingRight .Column_Security .sc_status .btnjz{
	margin:0 auto;
	margin-top: 15px;
	display: block;
	width: 112px;
	height: 40px;
	line-height: 40px;
	font-size: 15px;
	color: #666;
    background-color: #DEDEDE;
	border: 1px solid #DEDEDE;
	border-radius: 1000px;
	transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
	cursor:not-allowed;
}

/* LogonLog */
.SettingRight .Column_LogonLog{
	margin: 0 7px;
	margin-bottom: 20px;
}
.SettingRight .Column_LogonLog table{
	width: 100%;
	border: 1px solid #DEDEDE;
	border-bottom: none;
	font-size: 13px;
	color: #666;
}
.SettingRight .Column_LogonLog table .title{
	background-color: #f6f6f6;
}
.SettingRight .Column_LogonLog table th,.SettingRight .Column_LogonLog table td{
	border-bottom: 1px solid #DEDEDE;
}
.SettingRight .Column_LogonLog table th{
	height: 38px;
}
.SettingRight .Column_LogonLog table td{
	height: 38px;
	text-align: center;
}
.SettingRight .Column_LogonLog table .btns{
    margin: 25px auto;
	display: block;
	width: 180px;
	height: 40px;
	line-height: 40px;
	color: #666;
	font-size: 14px;
	border: 1px solid #CDCDCD;
	border-radius: 1000px;
}
.SettingRight .Column_LogonLog table .btns:hover{
	color: #fff;
	background-color: #6378f1;
	border: 1px solid #6378f1;
}


.SettingRight  .select {
	margin-top: -12px;
	margin-right: 15px;
    display: inline-block;
    *display: inline;
    *zoom: 1;
    width: 188px;
    padding-left: 10px;
    height: 38px;
    line-height: 38px;
    border: 1px solid #ccc;
    position: relative;
    background: url(../images/selbut.png) 175px center no-repeat;
}

.SettingRight  .select img {
    width: 22px;
    vertical-align: middle;
}

.SettingRight  .selul {
    width: 160px;
    height: 38px;
    border: none;
    background: #fff;
    outline: none;
    font-size: 14px;
    color: #666;
}

.SettingRight  .howmuch {
    margin-left: 10px;
    font-size: 14px;
    color: #666;
    line-height: 40px;
    vertical-align: top;
}

.SettingRight  .howleft {
    font-size: 18px;
    color: #333;
    font-weight: 900;
    line-height: 40px;
    vertical-align: top;
}
