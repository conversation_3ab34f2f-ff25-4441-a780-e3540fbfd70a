<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width,minimum-scale=1,maximum-scale=1.0,initial-scale=1,user-scalable=no,viewport-fit=true" data-shuvi-head="true">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
	    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/base.css" />
	    <title>{$webname}</title>
	    <style>
            .css-1wr4jig {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
            }
            .css-wp2li4 {
               box-sizing: border-box;
               margin: 0;
               min-width: 0;
               display: -webkit-box;
               display: -webkit-flex;
               display: -ms-flexbox;
               display: flex;
               overflow-x: hidden;
               -webkit-flex-direction: column;
               -ms-flex-direction: column;
               flex-direction: column;
            }
            .css-19zx9ks {
               box-sizing: border-box;
               margin: 0;
               min-width: 0;
               display: -webkit-box;
               display: -webkit-flex;
               display: -ms-flexbox;
               display: flex;
               -webkit-align-items: center;
               -webkit-box-align: center;
               -ms-flex-align: center;
               align-items: center;
               height: 48px;
               font-size: 12px;
               color: #474D57;
               display: -webkit-box;
               display: -webkit-flex;
               display: -ms-flexbox;
               display: flex;
               min-height: 48px;
               height: -webkit-fit-content;
               height: -moz-fit-content;
               height: fit-content;
               -webkit-flex-wrap: wrap;
               -ms-flex-wrap: wrap;
               flex-wrap: wrap;
               z-index: 99;
               padding-top: 16px;
               padding-bottom: 10px;
               padding-left: 17%;
               padding-right: 17%;
               line-height: 1.25;
               box-shadow: 0px 2px 4px rgb(0 0 0 / 4%);
            }
            .css-o4bcl2 {
               box-sizing: border-box;
               margin: 0;
               min-width: 0;
               color: #00b897;
               -webkit-text-decoration: none;
               text-decoration: none;
               color: #000000;
            }
            .css-19zx9ks a {
               -webkit-flex-wrap: nowrap;
               -ms-flex-wrap: nowrap;
               flex-wrap: nowrap;
               -webkit-flex-shrink: 0;
               -ms-flex-negative: 0;
               flex-shrink: 0;
               margin-bottom: 6px;
            }
            .css-19zx9ks a:hover {
               color: #C99400;
               -webkit-text-decoration: underline;
               text-decoration: underline;
            }
            .css-wl17qh {
               box-sizing: border-box;
               margin: 100%px;
               min-width: 0px;
               display: flex;
               height: fit-content;
               min-height: 349px;
            }
            .css-1s5qj1n {
               box-sizing: border-box;
               margin: 0;
               min-width: 0;
               position: relative;
               margin-bottom: 32px;
               padding-top: 40px;
               padding-right: 24px;
               padding-left: 17%;
               width: 69%;
            }
            .css-101kg5g {
               box-sizing: border-box;
               margin: 0;
               min-width: 0;
               font-size: 40px;
               color: #1E2329;
               font-weight: 700;
            }
            .css-vurnku {
               box-sizing: border-box;
               margin: 0;
               min-width: 0;
            }
            .css-wmvdm0 {
               box-sizing: border-box;
               margin: 0;
               min-width: 0;
               display: -webkit-box;
               display: -webkit-flex;
               display: -ms-flexbox;
               display: flex;
               -webkit-align-items: center;
               -webkit-box-align: center;
               -ms-flex-align: center;
               align-items: center;
               margin-top: 44px;
               margin-bottom: 11px;
            }
            .css-ktxhrn {
               box-sizing: border-box;
               margin: 0;
               min-width: 0;
               margin-bottom: 32px;
            }
            .css-qinc3w {
               box-sizing: border-box;
               margin: 0;
               min-width: 0;
               color: #00b897;
               display: block;
               margin-bottom: 16px;
               width: -webkit-fit-content;
               width: -moz-fit-content;
               width: fit-content;
               max-width: 100%;
               overflow: hidden;
               text-overflow: ellipsis;
               white-space: nowrap;
               color: #474D57;
               font-size: 14px;
               -webkit-text-decoration: none;
               text-decoration: none;
            }
            .css-6f91y1 {
               box-sizing: border-box;
               margin: 0;
               min-width: 0;
               margin-left: 48px;
            }
            .css-qinc3w:hover {
               color: #C99400;
               -webkit-text-decoration: underline;
               text-decoration: underline;
            }
            .css-ogtd7z {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-transition: all 1s;
                transition: all 1s;
                -webkit-box-pack: center;
                -webkit-justify-content: center;
                -ms-flex-pack: center;
                justify-content: center;
                background-color: #FEF1F2;
            }
            .css-jrzkh7 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                background-color: #181A20;
            }
            .css-1aac2e {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                margin-left: auto;
                margin-right: auto;
                padding-left: 24px;
                padding-right: 24px;
                max-width: 1248px;
                background-color: #FEF1F2;
            }
            .css-pnjgxq {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                height: 219px;
                background-color: #181A20;
            }
            .css-163lzuo {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                margin-top: 24px;
                margin-bottom: 24px;
                font-weight: 700;
                font-size: 36px;
                color: #EAECEF;
            }
            .css-8pdsjp {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-inline-box;
                display: -webkit-inline-flex;
                display: -ms-inline-flexbox;
                display: inline-flex;
                position: relative;
                height: 32px;
                margin-top: 4px;
                margin-bottom: 0px;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                line-height: 1.6;
                border: 1px solid transparent;
                border-color: #EAECEF;
                border-radius: 4px;
                margin-top: 0;
                margin-bottom: 0;
                width: 327px;
                height: 32px;
                padding-left: 11px;
                padding-right: 6px;
                border-radius: 4px;
                background-color: #2B3139;
                border: none;
            }
            .css-8pdsjp {
                width: 610px;
                height: 48px;
                padding-left: 16px;
                padding-right: 12.5px;
            }
            .css-16fg16t {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                width: 100%;
                height: 100%;
                padding: 0;
                outline: none;
                border: none;
                background-color: inherit;
                opacity: 1;
            }
            .css-8pdsjp input {
                color: #1E2329;
                font-size: 14px;
                padding-left: 8px;
                padding-right: 8px;
            }
            .css-8pdsjp input {
                padding-left: 0;
                font-size: 12px;
                color: #EAECEF;
                border-radius: 10px;
            }
            .css-8pdsjp .bn-input-suffix {
                -webkit-flex-shrink: 0;
                -ms-flex-negative: 0;
                flex-shrink: 0;
                margin-left: 4px;
                margin-right: 4px;
                font-size: 14px;
            }
            .css-1qppfsi {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                color: #EAECEF;
                width: 24px;
                height: 24px;
                font-size: 24px;
                fill: #1E2329;
                fill: #EAECEF;
                width: 1em;
                height: 1em;
                vertical-align: bottom;
            }
            ::-webkit-input-placeholder { /* WebKit browsers */
              color: #474d57;
              font-size: 14px;
            }
    
            ::-moz-placeholder { /* Mozilla Firefox 19+ */
              color: #474d57;
              font-size: 14px;
            }
            input:focus{background:#2B3139;outline: 1px solid #2B3139;}
            
            
	    </style>
	</head>
	<body>
	    <div class="App">
	        <div class="css-tq0shg">
	            <include file="Public:header"/>
	            <div class="css-ogtd7z">
	                <div class="css-jrzkh7">
	                    <div id="header_global_js_wxgy34nj" class="css-1aac2e"></div>
	                 </div>
	            </div>
	            <main class="css-1wr4jig" style="background: #fff;">
	                <div class="css-pnjgxq">
	                    <div data-bn-type="text" class="css-163lzuo">{:L('请输入您的问题')}</div>
	                    <div class=" css-8pdsjp">
	                        <input  value="" placeholder="{:L('在这儿搜索')}" maxlength="50" class="css-16fg16t">
	                        <div class="bn-input-suffix css-vurnku">
	                            <i class="bi bi-search css-1qppfsi"></i>
	                       </div>
	                   </div>
	                </div>
	                <div class="css-wp2li4">
	                    <div class="mirror-bread-crumb css-19zx9ks">
	                        <a data-bn-type="link"  class="css-o4bcl2">{:L('帮助中心')}</a>
	                    </div>
	                    <div class="css-wl17qh">
	                        <div class="css-1s5qj1n">
	                            <h1 data-bn-type="text" class="css-101kg5g">{:L('公告中心')}</h1>
	                            <div class="css-vurnku">
	                                <div class="css-wmvdm0"></div>
	                                <div class="css-6f91y1">
	                                    <div class="css-ktxhrn">
	                                        <foreach name="list" item="vo">
	                                            
	                                        <a href="{:U('Index/gginfo')}?id={$vo.id}" class="css-qinc3w" style="cursor: pointer;">{$vo.title} </a>
	                                        
	                                        </foreach>
	                                    </div>
	                                </div>
	                            </div>
	                        </div>
	                    </div>
	                </div>
	            </main>

	            <include file="Public:footer"/>
	        </div>   
	    </div>
	    
	</body>
	<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
    <script type="text/javascript" src="/Public/Home/static/js/layer/layer.js" ></script>
    <script type="text/javascript" src="/Public/Home/static/js/jquery.SuperSlide.2.1.1.js" ></script>

    <script type="text/javascript">
		$("#nav").slide({ 
			type:"menu",// 效果类型，针对菜单/导航而引入的参数（默认slide）
			titCell:".nLi", //鼠标触发对象
			targetCell:".sub", //titCell里面包含的要显示/消失的对象
			effect:"slideDown", //targetCell下拉效果
			delayTime:300 , //效果时间
			triggerTime:0, //鼠标延迟触发时间（默认150）
			returnDefault:true //鼠标移走后返回默认状态，例如默认频道是“预告片”，鼠标移走后会返回“预告片”（默认false）
		});
	</script>

    
</html>