* {
  margin: 0;
  padding: 0;
}
html,
body {
	min-width: 100%;
	min-height: 100vh;
	background-color: #2A2A2A;
}
body {
  position: relative;
}
.box {
	width: 900px;
	height: 50px;
	background-color: cadetblue;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	margin: auto;
	overflow: hidden;
	color: #fff;
	border-radius: 5px;
}
.box ul {
  position: absolute;
  top: 0;
  left: 0;
}
.box ul li {
  line-height: 50px;
  list-style: none;
  padding: 0 30px;
  box-sizing: border-box;
  cursor: pointer;
}
