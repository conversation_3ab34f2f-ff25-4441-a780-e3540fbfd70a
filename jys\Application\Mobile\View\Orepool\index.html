<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/base2.css" />
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/nologed.css" />
	<link rel="stylesheet" href="/Public/Static/Icoinfont/iconfont.css">
	<script src="/Public/Static/Icoinfont/iconfont.js"></script>
	<title>{$webname}</title>
	<style>
	::-webkit-input-placeholder {color: #b5b5b5;font-size: 12px;}
	::-moz-placeholder {color: #b5b5b5;font-size: 12px;}
	input:focus{background:#ebecf0;outline: 1px solid #ebecf0;}
	a:hover,a:link,a:visited,a:active{color:#000000;text-decoration:none;}
	.no_header{position: fixed;z-index: 9999;background:#121420;padding:0px 10px;top:0px;}
	.txtl{line-height:50px;width:10%;}
	.oreimgbox{width:100%;height:150px;margin-top:50px;}
	.oplist{width:100%;height:50px;background:#fff;padding:0px 15px;border-radius: 30px 30px 0 0;}
	.listbox{height:50px;line-height:50px;float:left;}
	.emptybox{width:30px;height:50px;float:left;}
	.hsborder{border-bottom:3px solid #00b897;}
	.btmbox{width:100%;height:60px;background:#fff;}
	.orebox{width:100%;margin:0px auto;background:#f7f9fc;margin-bottom:20px;box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);padding: 10px;}
	.progress-bar{color: #000;background: linear-gradient(to right, #f77062 , #fe5196);}
	.progress{height:0.9rem;background-color: #f5f5f5;border-radius: .5rem;}
	.obbox{width:33.33%;height:80px;float:left;}
	.obbox_h{width:100%;height:40px;line-height:40px;}
	</style>
  </head>
  </head>
  <body>
	<div class="container-fluid " style="padding:0px;width:100vw;">
		<div class="no_header">
			<div class="fl allhg txtl">
				<i class="bi bi-arrow-left fcc fw" onclick="goback()" style="font-size: 24px;"></i>
			</div>

			<div class="fl allhg" id="centerbox" style="width:80%;text-align:center;line-height:50px;">
				<span class="fcc fzmmm">DeFi</span>
			</div>
            
            <a href="{:U('Orepool/profitlist')}">
			<div class="fr allhg txtr" style="line-height:50px;width:10%;">
				<svg t="1656750606237"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4326" width="20" height="20"><path d="M914.9 158.4H183.5c-15.2 0-27.6-12.4-27.6-27.6v-4.1c0-15.2 12.4-27.6 27.6-27.6h731.4c15.2 0 27.6 12.4 27.6 27.6v4.1c0.1 15.2-12.3 27.6-27.6 27.6zM914.9 819.9H183.5c-15.2 0-27.6-12.4-27.6-27.6v-4.1c0-15.2 12.4-27.6 27.6-27.6h731.4c15.2 0 27.6 12.4 27.6 27.6v4.1c0.1 15.2-12.3 27.6-27.6 27.6zM574.7 489.2H176.6c-11.4 0-20.7-9.3-20.7-20.7v-18.1c0-11.4 9.3-20.7 20.7-20.7h398.1c11.4 0 20.7 9.3 20.7 20.7v18.1c0 11.4-9.3 20.7-20.7 20.7z" fill="#00b897" p-id="4327"></path></svg>
			</div>
			</a>
		</div>

		<div class="oreimgbox">

			<div class="fl col-8 pl20 pt20">
				<p class="fe6im ti12 f12">{:L('即享挖矿奖励')}</p>
				<p class="fe6im ti12 f12">{:L('随存随取')}</p>
				<p class="fe6im ti12 f12">{:L('灵活操作')}</p>
			</div>
			<div class="fr  col-4">
				<img src="/Upload/public/{$webkj}" class="d-block" style="width:170px;float: right;" />
			</div>
		</div>

		<div class="oplist">
			<div class="listbox hsborder" id="zlbtn">
				<span class="fzmm fcy" id="zlbtn_span">{:L('总览')}</span>
			</div>
			<div class="emptybox"></div>
			<div class="listbox" id="dzbtn">
				<span class="fzmm fcc" id="dzbtn_span">{:L('独资')}</span>
			</div>
			<div class="emptybox"></div>
			<div class="listbox" id="gxbtn">
				<span class="fzmm fcc" id="gxbtn_span">{:L('共享')}</span>
			</div>
			<div class="emptybox"></div>
			<div  class="listbox" id="myorebtn">
				<span class="fzmm fcc"  id="myorebtn_span">{:L('我的矿机')}</span>
			</div>
		</div>


        <!--全部矿机-->
		<div id="zlbox" style="width:100%;min-height:600px;background:#fff;padding:10px 0px;">
			<foreach name="alist" item="av">
			<div class="orebox">
				<div style="width:100%;height:120px;">
					<div style="width:30%;height:120px;float:left;text-align: center;margin-top: -20px;">
						<img src="/Upload/public/{$av.imgs}" style="width:100%;"/>
					</div>
					<div style="width:70%;height:120px;float:left;padding:5px;">
						<p class="fzmmm fcc fw" style="margin-bottom:2px;">{$av.title}</p>
						<p class="fzmm fcc" style="margin-bottom:2px;">{:L('上市时间')}：<?php echo date("Y-m-d",strtotime($av['addtime']));?></p>
						<p class="fzmm fcc" style="margin-bottom:2px;">{:L('产出币种')}：<?php echo strtoupper($av['outcoin']);?></p>
						<a href="{:U('Orepool/kjinfo')}?oid={$av.id}">
						<p class="fzmm fcc" style="margin-bottom:0px;text-align:right;">
							<span style="background: #0ecb81;color: #fff;padding: 10px;border-radius: 5px;">{:L('立即购买')}</span>
						</p>
						</a>
					</div>
				</div>
				<div style="width:100%;">
					<div class="progress">
					  <div class="progress-bar" role="progressbar" style="width:<?php echo ($av['ycnum'] + $av['sellnum']) / $av['allnum'] * 100;?>%;" aria-valuenow="<?php echo ($av['ycnum'] + $av['sellnum']) / $av['allnum'] * 100;?>" aria-valuemin="0" aria-valuemax="100"><?php echo ($av['ycnum'] + $av['sellnum']) / $av['allnum'] * 100;?>%</div>
					</div>
				</div>
				<div style="width:100%;height:80px;margin-top:15px;padding:0px 15px;">
					<div class="obbox" style="width:40%;">
						<div class="obbox_h">
							<span class="fzmm fcc">{:L('矿机单价')}</span>
						</div>
						<div class="obbox_h">
							<span class="fzmm fcc">{$av.pricenum} <?php echo strtoupper($av['pricecoin']);?></span>
						</div>
					</div>
					<div class="obbox" style="width:30%;">
						<div class="obbox_h" style="text-align:center;">
							<span class="fzmm fcc">{:L('矿机产权')}</span>
						</div>
						<div class="obbox_h" style="text-align:center;">
							<span class="fzmm fcc">{$av.cycle} {:L('天')}</span>
						</div>
					</div>
					<div class="obbox" style="width:30%;">
						<div class="obbox_h" style="text-align:right;">
							<span class="fzmm fcc">{:L('矿机类型')}</span>
						</div>
						<div class="obbox_h" style="text-align:right;">
						    <if condition="$av.type eq 1">
						    <span class="fzmmm fcy" style="font-weight:bold;">{:L('独资机型')}</span>
						    <elseif condition="$av.type eq 2" />
						    <span class="fzmmm fcy" style="font-weight:bold;">{:L('共享机型')}</span>
						    </if>
						</div>
					</div>
				</div>
			</div>
            </foreach>
			<div class="btmbox"></div>
		</div>
        

        <!--独资矿机-->
		<div id="dzbox" style="width:100%;padding:10px 0px;display:none;background:#fff;">
			
			<foreach name="blist" item="bv">
			<div class="orebox">
				<div style="width:100%;height:120px;">
					<div style="width:30%;height:120px;float:left;text-align: center;margin-top: -20px;">
						<img src="/Upload/public/{$bv.imgs}" style="width:100%;"/>
					</div>

					<div style="width:70%;height:120px;float:left;padding:5px;">
						<p class="fzmmm fcc fw" style="margin-bottom:2px;">{$bv.title}</p>
						<p class="fzmm fcc" style="margin-bottom:2px;">{:L('上市时间')}：<?php echo date("Y-m-d",strtotime($bv['addtime']));?></p>
						<p class="fzmm fcc" style="margin-bottom:2px;">{:L('产出币种')}：<?php echo strtoupper($bv['outcoin']);?></p>
						<a href="{:U('Orepool/kjinfo')}?oid={$bv.id}">
						<p class="fzmm fcc" style="margin-bottom:0px;text-align:right;" onclick="buykuangji({$bv.id})">
							<span style="background: #0ecb81;color: #fff;padding: 10px;border-radius: 5px;">{:L('立即购买')}</span>
						</p>
						</a>
					</div>

				</div>
				
				<div style="width:100%;">
					<div class="progress">
					  <div class="progress-bar" role="progressbar" style="width:<?php echo $bv['ycnum'] / $bv['allnum'] * 100;?>%;" aria-valuenow="<?php echo $bv['ycnum'] / $bv['allnum'] * 100;?>" aria-valuemin="0" aria-valuemax="100"><?php echo $bv['ycnum'] / $bv['allnum'] * 100;?>%</div>
					</div>
				</div>

				<div style="width:100%;height:80px;margin-top:15px;padding:0px 15px;">
					<div class="obbox" style="width:40%;">
						<div class="obbox_h">
							<span class="fzmm fcc">{:L('矿机单价')}</span>
						</div>
						<div class="obbox_h">
							<span class="fzmm fcc">{$bv.pricenum} <?php echo strtoupper($bv['pricecoin']);?></span>
						</div>
					</div>
					<div class="obbox" style="width:30%;">
						<div class="obbox_h" style="text-align:center;">
							<span class="fzmm fcc">{:L('矿机产权')}</span>
						</div>
						<div class="obbox_h" style="text-align:center;">
							<span class="fzmm fcc">{$bv.cycle} {:L('天')}</span>
						</div>
					</div>
					<div class="obbox" style="width:30%;">
						<div class="obbox_h" style="text-align:right;">
							<span class="fzmm fcc">{:L('矿机类型')}</span>
						</div>
						<div class="obbox_h" style="text-align:right;">
							<if condition="$bv.type eq 1">
						    <span class="fzmmm fcy" style="font-weight:bold;">{:L('独资机型')}</span>
						    <elseif condition="$bv.type eq 2" />
						    <span class="fzmmm fcy" style="font-weight:bold;">{:L('共享机型')}</span>
						    </if>
						</div>
					</div>
				</div>
			</div>
			</foreach>
			
			<div class="btmbox"></div>
		</div>

        <!--共享矿机-->
		<div id="gxbox" style="width:100%;min-height:600px;padding:10px 0px;display:none;background:#fff;">
			
			<foreach name="clist" item="cv">
			<div class="orebox">
				<div style="width:100%;height:120px;">
					<div style="width:30%;height:120px;float:left;text-align: center;margin-top: -20px;">
						<img src="/Upload/public/{$cv.imgs}" style="width:100%;"/>
					</div>
					<div style="width:70%;height:120px;float:left;padding:5px;">
						<p class="fzmmm fcc fw" style="margin-bottom:2px;">{$cv.title}</p>
						<p class="fzmm fcc" style="margin-bottom:2px;">{:L('上市时间')}：<?php echo date("Y-m-d",strtotime($cv['addtime']));?></p>
						<p class="fzmm fcc" style="margin-bottom:2px;">{:L('产出币种')}：<?php echo strtoupper($cv['outcoin']);?></p>
						<a href="{:U('Orepool/kjinfo')}?oid={$cv.id}">
						<p class="fzmm fcc" style="margin-bottom:0px;text-align:right;" onclick="buykuangji({$cv.id})">
							<span style="background: #0ecb81;color: #fff;padding: 10px;border-radius: 5px;">{:L('立即购买')}</span>
						</p>
						</a>
					</div>
				</div>
				<div style="width:100%;">
					<div class="progress">
					  <div class="progress-bar" role="progressbar" style="width:<?php echo $cv['ycnum'] / $cv['allnum'] * 100;?>%;" aria-valuenow="<?php echo $cv['ycnum'] / $cv['allnum'] * 100;?>" aria-valuemin="0" aria-valuemax="100"><?php echo $cv['ycnum'] / $cv['allnum'] * 100;?>%</div>
					</div>
				</div>
				<div style="width:100%;height:80px;margin-top:15px;padding:0px 15px;">
					<div class="obbox" style="width:40%;">
						<div class="obbox_h">
							<span class="fzmm fcc">{:L('矿机单价')}</span>
						</div>
						<div class="obbox_h">
							<span class="fzmm fcc">{$bv.pricenum} <?php echo strtoupper($bv['pricecoin']);?></span>
						</div>
					</div>
					<div class="obbox" style="width:30%;">
						<div class="obbox_h" style="text-align:center;">
							<span class="fzmm fcc">{:L('矿机产权')}</span>
						</div>
						<div class="obbox_h" style="text-align:center;">
							<span class="fzmm fcc">{$bv.cycle} {:L('天')}</span>
						</div>
					</div>
					<div class="obbox" style="width:30%;">
						<div class="obbox_h" style="text-align:right;">
							<span class="fzmm fcc">{:L('矿机类型')}</span>
						</div>
						<div class="obbox_h" style="text-align:right;">
							<if condition="$cv.type eq 1">
						    <span class="fzmmm fcy" style="font-weight:bold;">{:L('独资机型')}</span>
						    <elseif condition="$cv.type eq 2" />
						    <span class="fzmmm fcy" style="font-weight:bold;">{:L('共享机型')}</span>
						    </if>
						</div>
					</div>
				</div>
			</div>
			</foreach>
			
			<div class="btmbox"></div>
		</div>

        <!--我的矿机-->
		<div id="myorebox" style="width:100%;min-height:600px;padding:10px 0px;display:none;background:#fff;">
			<empty name="mylist">
			<div class="orebox">
			    <div style="width:100%;height:120px;line-height:120px;text-align:center;">
			        <span class="fzmm fcc">{:L('暂时没有矿机')}</span>
			    </div>
			</div>
			<else />
			<foreach name="mylist" item="myvo">
			<a href="{:U('Orepool/profitline')}?id={$myvo.id}">
			<div class="orebox">
				<div style="width:100%;height:120px;">
					<div style="width:30%;height:120px;float:left;text-align: center;margin-top: -20px;">
						<img src="/Upload/public/{$myvo.imgs}" style="width:100%;"/>
					</div>
					<div style="width:70%;height:120px;float:left;padding:5px;">
						<p class="fzmmm fcc fw" style="margin-bottom:2px;">{$myvo.kjtitle}</p>
						<p class="fzmm fcc" style="margin-bottom:2px;">{:L('购买时间')}：<?php echo date("Y-m-d",strtotime($myvo['addtime']));?></p>
						<p class="fzmm fcc" style="margin-bottom:2px;">{:L('到期时间')}：<?php echo date("Y-m-d",strtotime($myvo['endtime']));?></p>
						<if condition="$myvo.status eq 1">
						<p class="fzmm fcc" style="margin-bottom:0px;text-align:right;margin-top: 5px;">
							<span style="background:#0ecb81;padding:5px 10px;border-radius: 5px;color:#fff;">{:L('正常')}</span>
						</p>
						<elseif condition="$myvo.status eq 2" />
						<p class="fzmm fcc" style="margin-bottom:0px;text-align:right;margin-top: 5px;">
							<span style="background:#f5465c;padding:5px 10px;border-radius: 5px;color:#fff;">{:L('停止')}</span>
						</p>
						<elseif condition="$myvo.status eq 3" />
						<p class="fzmm fcc" style="margin-bottom:0px;text-align:right;margin-top: 5px;">
							<span style="background:#f5465c;padding:5px 10px;border-radius: 5px;color:#fff;">{:L('过期')}</span>
						</p>
						</if>
					</div>
				</div>

				<div style="width:100%;height:80px;margin-top:15px;padding:0px 15px;">
					<div class="obbox" style="width:30%;">
						<div class="obbox_h">
							<span class="fzmm fcc">{:L('产出币种')}</span>
						</div>
						<div class="obbox_h">
							<span class="fzmm fcc"><?php echo strtoupper($myvo['outcoin']);?></span>
						</div>
					</div>
					<div class="obbox" style="width:30%;">
						<div class="obbox_h" style="text-align:center;">
							<span class="fzmm fcc">{:L('收益类型')}</span>
						</div>
						<div class="obbox_h" style="text-align:center;">
						    <if condition="$myvo.outtype eq 1">
							<span class="fzmm fcc">{:L('按产值')}</span>
							<elseif condition="$myvo.outtype eq 2" />
							<span class="fzmm fcc">{:L('恒定币量')}</span>
							</if>
						</div>
					</div>
					<if condition="$myvo.outtype eq 1">
					 <div class="obbox" style="width:40%;">
						<div class="obbox_h" style="text-align:right;">
							<span class="fzmm fcc">{:L('日产值')}</span>
						</div>
						<div class="obbox_h" style="text-align:right;">
							<span class="fzmm fcc">{$myvo.outusdt} USDT</span>
						</div>
					</div>   
					<elseif condition="$myvo.outtype eq 2" />
					<div class="obbox" style="width:40%;">
						<div class="obbox_h" style="text-align:right;">
							<span class="fzmm fcc">{:L('日产币量')}</span>
						</div>
						<div class="obbox_h" style="text-align:right;">
							<span class="fzmm fcc">{$myvo.outnum} <?php echo strtoupper($myvo['outcoin']);?></span>
						</div>
					</div>
					</if>
				</div>

			</div>
			</a>
			</foreach>
			</empty>
			
			<div class="btmbox"></div>
		</div>




	</div>	



	
	<!--底部-->
	<div class="footer hide">

		<a href="{:U('Index/index')}">
			<div class="footer_op">
				<div class="f_op_t" style="line-height: 35px;" >
					<?php
					if($select == 'index') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-1-active.png" style="width: 25px;" >';
					}else{
					echo '<img src="/Public/Static/Icoinfont/icon/nav-1.png" style="width: 25px;" >';
					}
					?>
				</div>
				<div class="f_op_b">
					<span class="fzm fcch">{:L('首页')}</span>
				</div>
			</div>
		</a>

		<a href="{:U('Trade/index')}">
			<div class="footer_op">
				<div class="f_op_t" style="line-height:35px;">
					<?php
					if($select == 'trade') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-2-active.png" style="width: 25px;" >';
					}else{
					echo '<img src="/Public/Static/Icoinfont/icon/nav-2.png" style="width: 25px;" >';
					}
					?>
				</div>
				<div class="f_op_b">
					<span class="fzm fcch">{:L('行情')}</span>
				</div>
			</div>
		</a>

		<a href="/Trade/transinfo.html?symbol=BTC&type=buy">
			<div class="footer_op">
				<div class="f_op_t" style="line-height:35px;">
					<?php
					if($select == 'trans') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-3-active.png" style="width: 25px;" >';
					}else{
					echo '<img src="/Public/Static/Icoinfont/icon/nav-3.png" style="width: 25px;" >';
					}
					?>
				</div>
				<div class="f_op_b">
					<span class="fzm fcy">{:L('交易')}</span>
				</div>
			</div>
		</a>

		<a href="{:U('Contract/index')}">
			<div class="footer_op">
				<div class="f_op_t" style="line-height:35px;">
					<?php
					if($select == 'contract') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-4-active.png" style="width: 25px;" >';
					}else{
					echo '<img src="/Public/Static/Icoinfont/icon/nav-4.png" style="width: 25px;" >';
					}
					?>
				</div>
				<div class="f_op_b">
					<span class="fzm fcch">{:L('合约')}</span>
				</div>
			</div>
		</a>

		<a href="{:U('User/index')}">
			<div class="footer_op">
				<div class="f_op_t" style="line-height:35px;">
					<?php
					if($select == 'user') {
						echo '<img src="/Public/Static/Icoinfont/icon/nav-5-active.png" style="width: 25px;" >';
					}else{
					echo '<img src="/Public/Static/Icoinfont/icon/nav-5.png" style="width: 25px;" >';
					}
					?>
				</div>
				<div class="f_op_b">
					<span class="fzm fcch">{:L('资产')}</span>
				</div>
			</div>
		</a>


	</div>


</body>

<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
<script type="text/javascript" src="/Public/Static/js/layer/layer.js" ></script>

<script type="text/javascript">
	$("#zlbtn").click(function(){
		$("#zlbtn").addClass("hsborder");
		$("#zlbtn_span").css("color","#00b897");
		$("#zlbox").show();
		$("#dzbtn").removeClass("hsborder");
		$("#dzbtn_span").css("color","#707A8A");
		$("#dzbox").hide();
		$("#gxbtn").removeClass("hsborder");
		$("#gxbtn_span").css("color","#707A8A");
		$("#gxbox").hide();
		$("#myorebtn").removeClass("hsborder");
		$("#myorebtn_span").css("color","#707A8A");
		$("#myorebox").hide();
	});
	$("#dzbtn").click(function(){
		$("#dzbtn").addClass("hsborder");
		$("#dzbtn_span").css("color","#00b897");
		$("#zlbox").hide();
		$("#zlbtn").removeClass("hsborder");
		$("#zlbtn_span").css("color","#707A8A");
		$("#dzbox").show();
		$("#gxbtn").removeClass("hsborder");
		$("#gxbtn_span").css("color","#707A8A");
		$("#gxbox").hide();
		$("#myorebtn").removeClass("hsborder");
		$("#myorebtn_span").css("color","#707A8A");
		$("#myorebox").hide();
	});
	$("#gxbtn").click(function(){
		$("#dzbtn").removeClass("hsborder");
		$("#dzbtn_span").css("color","#707A8A");
		$("#zlbox").hide();
		$("#zlbtn").removeClass("hsborder");
		$("#zlbtn_span").css("color","#707A8A");
		$("#dzbox").hide();
		$("#gxbtn").addClass("hsborder");
		$("#gxbtn_span").css("color","#00b897");
		$("#gxbox").show();
		$("#myorebtn").removeClass("hsborder");
		$("#myorebtn_span").css("color","#707A8A");
		$("#myorebox").hide();
	});
	$("#myorebtn").click(function(){
		$("#dzbtn").removeClass("hsborder");
		$("#dzbtn_span").css("color","#707A8A");
		$("#zlbox").hide();
		$("#zlbtn").removeClass("hsborder");
		$("#zlbtn_span").css("color","#707A8A");
		$("#dzbox").hide();
		$("#gxbtn").removeClass("hsborder");
		$("#gxbtn_span").css("color","#707A8A");
		$("#gxbox").hide();
		$("#myorebtn").addClass("hsborder");
		$("#myorebtn_span").css("color","#00b897");
		$("#myorebox").show();
	});
</script>

<script type="text/javascript">
    function goback(){
        window.history.go(-1);
    }
</script>
</html>



