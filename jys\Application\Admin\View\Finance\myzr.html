<include file="Public:header"/>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title">充币列表</span>
			<div class="fr">
			    <button class="btn btn-warning" onClick="location.href='{:U('Finance/myzr')}'">初始化搜索</button>
		    </div>
		</div>
		
		<div class="cf">

			<div class="search-form fl cf" style="float: none !important;">
				<div class="sleft">
					<form name="formSearch" id="formSearch" method="get" name="form1">
						<select style=" width: 100px; float: left; margin-right: 10px;" name="field" class="form-control">
							<option value="username" <eq name="Think.get.field" value="username">selected</eq>>用户名</option>
						</select>
						<input type="text" name="name" class="search-input form-control  " value="{$Think.get.name}" placeholder="请输入查询内容" style="">
						<a class="sch-btn" href="javascript:;" id="search"> <i class="btn-search"></i> </a>
					</form>
					<script>
						//搜索功能
						$(function () {
							$('#search').click(function () {
								$('#formSearch').submit();
							});
						});
						//回车搜索
						$(".search-input").keyup(function (e) {
							if (e.keyCode === 13) {
								$("#search").click();
								return false;
							}
						});
					</script>
				</div>
			</div>
		</div>
		<div class="data-table table-striped">
			<table class="">
				<thead>
				<tr>
					<th class="">ID</th>
					<th class="">用户名</th>
					<th class="">充值币种</th>
					<th class="">充值网络</th>
					<th width="">充值时间</th>
					<th width="">处理时间</th>
					<th width="">充值数量</th>
					<th width="">实际到账</th>
					<th width="">转账凭证</th>
					<th width="">状态</th>
					<th width="">操作</th>
				</tr>
				</thead>
				<tbody>
				<notempty name="list">
					<volist name="list" id="vo">
						<tr>
							<td>{$vo.id}</td>
							<td>{$vo.username}</td>
							<td><?php echo strtoupper($vo['coin']);?></td>
							<td><?php echo strtoupper($vo['czline']);?></td>
							<td>{$vo.addtime}</td>
							<td>{$vo.updatetime}</td>
							<td>{$vo['num']*1}</td>
							<td>{$vo['num']*1}</td>
							<td>
							    <img src="/Public/Static/payimgs/{$vo.payimg}" style="height:60px;"/>
							</td>
							
							<td>
								<eq name="vo.status" value="1">
								    <span style="color:blue;">等待审核</span>
								</eq>
								<eq name="vo.status" value="2">
								    <span style="color:green;">审核通过</span>
								</eq>
								<eq name="vo.status" value="3">
								    <span style="color:red;">驳回充值</span>
								</eq>
							</td>
							<td>
                                <eq name="vo.status" value="1">
                                    <input type="button" class="ajax-get btn btn-primary btn-xs" value="确认" onclick="Upzr('{$vo['id']}');"/>
                                    <input type="button" class="ajax-get btn btn-primary btn-xs" value="驳回" onclick="Upbhzr('{$vo['id']}');"/>
                                </eq>
                                <eq name="vo.status" value="2"><span style="color:blue;">已处理</span></eq>
                                <eq name="vo.status" value="3"><span style="color:blue;">已处理</span></eq>
                            </td>
						</tr>
					</volist>
					<else/>
					<td colspan="12" class="text-center empty-info"><i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据</td>
				</notempty>
				</tbody>
			</table>
			<div class="page">
				<div>{$page}</div>
			</div>
		</div>
	</div>
</div>
<include file="Public:footer"/>
<script type="text/javascript">
    function Upbhzr(id) {
        var zcid = parseInt(id);
        if (zcid == "" || zcid == null || zcid <=0) {
            layer.alert('参数错误！');
            return false;
        }
        layer.load(0, {shade: [0.5,'#8F8F8F']});
        $.post("{:U('Finance/rejectzr')}", {
            id: zcid
        }, function (data) {
            setTimeout("closetanchu()",2000);
            if (data.status == 1) {
                layer.msg(data.info, {
                    icon: 1
                });
                setTimeout("shuaxin()",1000);
            } else {
                layer.msg(data.info, {
                    icon: 2
                });
            }
        }, "json");
    }
</script>
<script type="text/javascript">
    function Upzr(id) {
        var zcid = parseInt(id);
        if (zcid == "" || zcid == null || zcid <=0) {
            layer.alert('参数错误！');
            return false;
        }
        layer.load(0, {shade: [0.5,'#8F8F8F']});
        $.post("{:U('Finance/adoptzr')}", {
            id: zcid
        }, function (data) {
            setTimeout("closetanchu()",2000);
            if (data.status == 1) {
                layer.msg(data.info, {
                    icon: 1
                });
                setTimeout("shuaxin()",1000);
            } else {
                layer.msg(data.info, {
                    icon: 2
                });
            }
        }, "json");
    }
</script>
<script type="text/javascript">
    function closetanchu(){
        layer.closeAll('loading');
    }
    function shuaxin(){
        window.location.href=window.location.href;
    }
</script>
</script>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('Finance/myzr')}");
	</script>
</block>