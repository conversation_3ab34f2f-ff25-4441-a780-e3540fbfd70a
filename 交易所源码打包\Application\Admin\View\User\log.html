<include file="Public:header"/>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title">登陆日志</span>
		</div>
		<div>
			<div class="cf">
				<div class="fl">
					<div class="fl">
						<a class="btn btn-success " href="{:U('User/logEdit')}">新 增</a>
						<button class="ajax-post btn  btn-info " url="{:U('User/logStatus',array('type'=>'resume'))}" target-form="ids">启 用</button>
						<button class="ajax-post btn  btn-warning  ajax-post" url="{:U('User/logStatus',array('type'=>'forbid'))}" target-form="ids">禁 用</button>
						<button class="btn ajax-post confirm btn-danger " url="{:U('User/logStatus',array('type'=>'del'))}" target-form="ids">删 除</button>
					</div>
				</div>
				<div class="search-form fr cf">
					<div class="sleft">
						<form name="formSearch" id="formSearch" method="get" name="form1">
							<select style="width: 160px; float: left; margin-right: 10px;" name="status" class="form-control">
								<option value=""
								<empty name="Think.get.status">selected</empty>
								>全部状态</option>
								<option value="1"
								<eq name="Think.get.status" value="1">selected</eq>
								>冻结状态</option>
								<option value="2"
								<eq name="Think.get.status" value="2">selected</eq>
								>正常状态</option>
							</select>
							<select style=" width: 160px; float: left; margin-right: 10px;" name="field" class="form-control">
								<option value="username"
								<empty name="Think.get.field">selected</empty>
								>用户名</option>
							</select>
							<input type="text" name="name" class="search-input form-control  " value="{$Think.get.name}" placeholder="请输入查询内容" style="">
							<a class="sch-btn" href="javascript:;" id="search"> <i class="btn-search"></i> </a>
						</form>
						<script>
							//搜索功能
							$(function () {
								$('#search').click(function () {
									$('#formSearch').submit();
								});
							});
							//回车搜索
							$(".search-input").keyup(function (e) {
								if (e.keyCode === 13) {
									$("#search").click();
									return false;
								}
							});
						</script>
					</div>
				</div>
			</div>
		</div>
		<div class="data-table table-striped">
			<table class="">
				<thead>
				<tr>
					<th class="row-selected row-selected"><input class="check-all" type="checkbox"/></th>
					<th class="">ID</th>
					<th class="">用户名</th>
					<th class="">类型</th>
					<th class="">内容</th>
					<th class="">操作ip</th>
					<th class="">操作时间</th>
					<th class="">状态</th>
					<th class="">操作</th>
				</tr>
				</thead>
				<tbody>
				<notempty name="list">
					<volist name="list" id="vo">
						<tr>
							<td><input class="ids" type="checkbox" name="id[]" value="{$vo.id}"/></td>
							<td>{$vo.id}</td>
							<td>{$vo['username']}</td>
							<td>{$vo.type}</td>
							<td>{$vo.remark}</td>
							<td>{$vo.addip}</td>
							<td>{$vo.addtime|addtime}</td>
							<td>
								<eq name="vo.status" value="1">可用
									<else/>
								                               禁用
								</eq>
							</td>
							<td><a href="{:U('User/logEdit?id='.$vo['id'])}" class="btn btn-primary btn-xs">编辑</a>
							</td>
						</tr>
					</volist>
					<else/>
					<td colspan="12" class="text-center empty-info"><i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据</td>
				</notempty>
				</tbody>
			</table>
			<div class="page">
				<div>{$page}</div>
			</div>
		</div>
	</div>
</div>
<include file="Public:footer"/>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('User/log')}");
	</script>
</block>