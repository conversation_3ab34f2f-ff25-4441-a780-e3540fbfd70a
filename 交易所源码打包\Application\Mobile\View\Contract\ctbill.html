<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/base2.css" />
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/nologed.css" />
	<link rel="stylesheet" href="/Public/Static/Icoinfont/iconfont.css">
	<script src="/Public/Static/Icoinfont/iconfont.js"></script>
	<title>{$webname}</title>
	<style>
	    ::-webkit-input-placeholder {color: #b5b5b5;font-size: 12px;}
	    ::-moz-placeholder {color: #b5b5b5;font-size: 12px;}
	    input:focus{background:#f5f5f5;outline: 1px solid #f5f5f5;}
	    a:hover,a:link,a:visited,a:active{color:#000000;text-decoration:none;}
	    .no_header{position: fixed;z-index: 9999;padding:0px 10px;top:0px;line-height: 50px;background:#1b1d29;}
	    .contentbox{width:100%;margin-top:45px;background: #1b1d29}
	    .contentbox_top{width:100%;height:50px;line-height:50px;text-align:left;padding:5px 15px;}


        .listbox{width:100%;height:120px;background:#2C3445;padding:5px 15px;margin-bottom:5px;border-radius: 10px;}


        .listbox_title{width:100%;height:30px;line-height:30px;}
        .listbox_title_l{width:50%;height:30px;float:left;text-align:left;}
        .listbox_title_r{width:50%;height:30px;float:right;text-align:right;}
        .progress-bar{background-color: #eeb80d;}
		.bi-chevron-right::before {
			color: #e6e6e6;
		}



	</style>
</head>
<body>
	<div class="container-fluid " style="padding:0px;width:100vw;">

		<empty name="iframeid">
        <div class="no_header">
			<div class="fl allhg txtl">
				<i class="bi bi-arrow-left fcc fw" onclick="goback()" style="font-size: 24px;"></i>

			</div>
			<div id="hylist_btn  " class="fe6im"  style="position:fixed ;width:100%;height: 50px;line-height:50px;float:left;margin-right:10px;text-align: center;z-index: -1;">
				<span id="hylist_btn_span" style="font-size:16px;font-weight:550;">{:L('合约记录')}</span>
			</div>
			<div class="fr allhg txtr" style="line-height:50px;width:10%;"></div>
		</div>
		</empty>

		<empty name="iframeid">
		<div class="contentbox">
		</else>
			<div class="contentbox" style="margin-top: 0px;">
		</empty>




			<!-----------合约订单列表---------->
		    <div id="list_box" style="width:100%;background:#121420;padding: 0px 10px;<empty name="iframeid">margin-top:60px</empty>;display:block">

		    </div>
            


		    <div style="width:100%;height:80px;background:#121420;"></div>
   
		</div>

	</div>	
</body>
<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
<script type="text/javascript" src="/Public/Static/js/layer/layer.js" ></script>
<script type="text/javascript">
    $(function(){
        gethyorder();
        setInterval("gethyorder()",1000);
    });
    $("#hylist_btn").click(function(){
        $("#hylist_btn_span").css('font-size','16px');
        $("#tylist_btn_span").css('font-size','14px');
        $("#list_box").show();
        $("#tylist_box").hide();
    });
     $("#tylist_btn").click(function(){
        $("#hylist_btn_span").css('font-size','14px');
        $("#tylist_btn_span").css('font-size','16px');
        $("#list_box").hide();
        $("#tylist_box").show();
    });
</script>
<script type="text/javascript">
    function set_tyinterval(){
        gethyorder();
        setInterval("get_tyhyorder()",1000);
    }
    function get_tyhyorder(){
        $.post("{:U('Contract/get_tyhyorder')}",
        function(data){
            if(data.code == 1){
                $("#tylist_box").empty();
                var html = '';
                if(data.data == '' || data.data == null){
                    html = '<div class="listbox" style="border:none;">'+ 
		                   '<div style="width:100%;height:100px;line-height:120px;text-align:center;">'+ 
		                   '<span class="fzm fcc">' + "{:L('没有获取数据')}" + '</span>'+ 
		                   '</div>'+ 
		                   '</div>';
                    $("#tylist_box").append(html);
                    
                }else{
                    
                    $.each(data.data,function(key,val){
                        html += '<div class="listbox">'+
                                '<div class="listbox_title">'+
                                '<div class="listbox_title_l">'+
                                '<span class="fcc fe6im" style="font-size:16px;font-weight:500;">'+"{:L('交易对')}"+':</span>'+
                                '<span class="fcc fe6im" style="font-size:16px;font-weight:500;">'+val.coinanme+'</span>'+
                                '</div>'+
                                '<a href="'+val.href+'">'+
                                '<div class="listbox_title_r">'+
                                '<span class="fcc" style="font-size:14px;color: #2ebd85">'+val.statusstr+'</span>'+
                                '<i class="bi bi-chevron-right fzmmm"></i>'+
                                '</div>'+
                                '</a>'+
                                '</div>'+
                                '<div style="width:100%;height:60px;">'+
                                '<div style="width:33.33%;height:60px;float:left;">'+
                                '<div style="width:100%;height:30px;line-height:40px;">'+
                                '<span style="color:#cbcbcb;">'+"{:L('投入金额')}"+'</span>'+
                                '</div>'+
                                '<div style="width:100%;height:30px;line-height:30px;">'+
                                '<span class="fe6im">'+val.num+'</span>'+
                                '</div>'+
                                '</div>'+
                                '<div style="width:33.33%;height:60px;float:left;">'+
                                '<div style="width:100%;height:30px;line-height:40px;">'+
                                '<span style="color:#cbcbcb;">'+"{:L('建仓单价')}"+'</span>'+
                                '</div>'+
                                '<div style="width:100%;height:30px;line-height:30px;">'+
                                '<span class="fe6im">'+val.buyprice+'</span>'+
                                '</div>'+
                                '</div>'+
                                '<div style="width:33.33%;height:60px;float:left;">'+
                                '<div style="width:100%;height:30px;line-height:40px;text-align:right;">'+
                                '<span style="color:#cbcbcb;">'+"{:L('建仓时间')}"+'</span>'+
                                '</div>'+
                                '<div style="width:100%;height:30px;line-height:30px;text-align:right;">'+
                                '<span class="fe6im">'+val.buytime+'</span>'+
                                '</div>'+
                                '</div>'+
                                '</div>'+
                                '<div class="progress" style="height: 10px;">'+
                                '<div class="progress-bar" role="progressbar" style="width:'+val.bl+'" aria-valuenow="'+val.t+'" aria-valuemin="0" aria-valuemax="100">'+val.t+'</div>'+
                                '</div>'+
                                '</div>'; 
  
                    });
                    $("#tylist_box").append(html);
                }
            }else{
                html = '<div class="listbox" style="border:none;">'+ 
		                   '<div style="width:100%;height:100px;line-height:120px;text-align:center;">'+ 
		                   '<span class="fzm fcc">' + "{:L('没有获取数据')}" + '</span>'+ 
		                   '</div>'+ 
		                   '</div>';
                    $("#tylist_box").append(html);
            }
        });
    }
    
    function gethyorder(){
  
        $.post("{:U('Contract/gethyorder')}",
        function(data){
            if(data.code == 1){
                $("#list_box").empty();
                var html = '';
                if(data.data == '' || data.data == null){
                    html = '<div class="listbox" style="border:none;">'+ 
		                   '<div style="width:100%;height:100px;line-height:120px;text-align:center;">'+ 
		                   '<span class="fzm fcc">' + "{:L('没有获取数据')}" + '</span>'+ 
		                   '</div>'+ 
		                   '</div>';
                    $("#list_box").append(html);
                    
                }else{
                    
                    $.each(data.data,function(key,val){
                        html += '<div class="listbox">'+
                                '<div class="listbox_title">'+
                                '<div class="listbox_title_l">'+
                                '<span class="fcc f14 " style="font-size:16px;font-weight:500;color: '+ val.hyzdcolor +' ">'+val.hyzdstr+':</span>'+
                                '<span class="fcc f14 fe6im" style="font-size:16px;font-weight:500;">'+val.coinanme+'</span>'+
                                '</div>'+
                                '<a href="'+val.href+'">'+
                                '<div class="listbox_title_r">'+
                                '<span class="fcc" style="font-size:12px;color:'+ val.fcolr +'">'+val.statusstr+'</span>'+
                                '<i class="bi bi-chevron-right fzmmm"></i>'+
                                '</div>'+
                                '</a>'+
                                '</div>'+
                                '<div style="width:100%;height:60px;">'+
                                '<div style="width:33.33%;height:60px;float:left;">'+
                                '<div style="width:100%;height:30px;line-height:40px;">'+
                                '<span class="f12" style="color:#6B7785;">'+"{:L('投入金额')}"+'</span>'+
                                '</div>'+
                                '<div style="width:100%;height:30px;line-height:30px;">'+
                                '<span class=" f12 fce5">'+val.num+'</span>'+
                                '</div>'+
                                '</div>'+
                                '<div style="width:33.33%;height:60px;float:left;">'+
                                '<div style="width:100%;height:30px;line-height:40px;">'+
                                '<span class="f12"  style="color:#6B7785;">'+"{:L('建仓单价')}"+'</span>'+
                                '</div>'+
                                '<div style="width:100%;height:30px;line-height:30px;">'+
                                '<span class=" f12 fce5">'+val.buyprice+'</span>'+
                                '</div>'+
                                '</div>'+
                                '<div style="width:33.33%;height:60px;float:left;">'+
                                '<div style="width:100%;height:30px;line-height:40px;text-align:right;">'+
                                '<span class="f12"  style="color:#6B7785;">'+"{:L('建仓时间')}"+'</span>'+
                                '</div>'+
                                '<div style="width:100%;height:30px;line-height:30px;text-align:right;">'+
                                '<span class=" f12 fce5">'+val.buytime+'</span>'+
                                '</div>'+
                                '</div>'+
                                '</div>'+
                                '<div class="progress" style="height: 10px;background-color: #2C3445">'+
                                '<div class="progress-bar" role="progressbar" style="width:'+val.bl+';background: linear-gradient(to right, #f77062 , #fe5196);" aria-valuenow="'+val.t+'" aria-valuemin="0" aria-valuemax="100">'+val.t+'</div>'+
                                '</div>'+
                                '</div>'; 
  
                    });
                    $("#list_box").append(html);
                }
            }else{
                html = '<div class="listbox" style="border:none;">'+ 
		                   '<div style="width:100%;height:100px;line-height:120px;text-align:center;">'+ 
		                   '<span class="fzm fcc">' + "{:L('没有获取数据')}" + '</span>'+ 
		                   '</div>'+ 
		                   '</div>';
                    $("#list_box").append(html);
            }
        });
    }
</script>
<script type="text/javascript">
    function goback(){
        window.history.go(-1);
    }
</script>
</html>



