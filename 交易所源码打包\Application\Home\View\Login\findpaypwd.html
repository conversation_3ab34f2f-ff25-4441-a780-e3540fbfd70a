<include file="Public:header_new" />
<link rel="stylesheet" href="__PUBLIC__/intel/css/intlTelInput.css">
<script src="__PUBLIC__/intel/js/intlTelInput.js"></script>
<link rel="stylesheet" href="__PUBLIC__/Home/ecshe_css/pass.css">
<style>
    .nochoose{  
        width:30%;
        margin-left:2.5%;
        margin-top:10px;
        height:36px;
        line-height:36px;
        float:left;
        text-align:center;
        border:1px solid #ccc;
        border-radius:10px;
        cursor:pointer;
    }
    .choose{
        width: 30%;
        margin-left: 2.5%;
        margin-top: 10px;
        height: 36px;
        line-height: 36px;
        float: left;
        text-align: center;
        border: 1px solid #f5f5f5;
        border-radius: 10px;
        cursor: pointer;
        color: #f5f5f5;
    }
    
</style>
<div class="logsbox" style="margin-top:100px;margin-bottom:50px;" id="onebox">
	<h2>{:L('找回交易密码')}</h2>
	<div class="form-group">
		<input type="text" autocomplete="off" id="username" placeholder="{:L('用户登陆账号')}" onblur="usename_check()" />
	</div>
	<!--<div class="form-group">
		<input type="text" autocomplete="off" id="verify" placeholder="{:L('图形验证码')}" onblur="regverify()" />
		<div class="imgcode">
			<img src="{:U('Verify/code')}" title="{:L('换一张')}" onclick="this.src=this.src+'?t='+Math.random()" id="verifycode">
		</div>
	</div>
	<div class="form-group">
		<input type="text" autocomplete="off" id="mobile_verify" placeholder="{:L('动态验证码')}" />
		<a class="code-num" id="regBtn" onclick="SendCode()">{:L('获取验证码')}</a>
	</div>-->

	<div class="form-group">
		<input type="password" autocomplete="off" id="password" onblur="password()" placeholder="{:L('新交易密码')}" />
	</div>
	<div class="form-group">
		<input type="password" autocomplete="off" id="repassword" onblur="repassword()" placeholder="{:L('确认交易密码')}" />
	</div>
	<div class="form-button">
		<input type="button" name="index_submit" id="loginSubmin" onclick="to_next();" class="btn btn-primary" value="{:L('提交')}">
	</div>
</div>

<div class="logsbox" style="margin-top: 30px;margin-bottom: 100px;display:none;" id="twobox">

	<div class="form-group" style="margin-top:0px;">
	    <h4 style="margin-bottom:10px;">备份助记词</h4>
	    <p style="margin-bottom:10px;text-align: left;">请按顺序抄写助记词，确保证备份正确。</p>
		
		<div style="width:100%;height:150px;border-radius:5px;border:1px solid #ccc;background:#fbfbfb;">
		    <ul id="mywords">
		        <!--<?php //foreach($t_words as $k=>$v){?>
		        
		        <li style="width:33.33%;height:36px;line-height:36px;float:left;text-align:center;"><?php //echo $v;?></li>
		        
		        <?php //}?>-->
		    </ul>
		</div>
		<p style="margin-top:10px;text-align: left;">*  妥善保管助记词至隔离网络的安全地方</p>
	</div>

	<div class="form-button">
		<input type="button" name="index_submit"  onclick="next_updata();" class="btn btn-primary" value="{:L('下一步')}">
	</div>
</div>

<div class="logsbox" style="margin-top: 30px;margin-bottom: 100px;display:none;" id="threebox">

	<div class="form-group" style="margin-top:0px;">
	    <h4 style="margin-bottom:10px;">确认助记词</h4>
	    <p style="margin-bottom:10px;text-align: left;">请按顺序点击助记词，以确认您正确的备份。</p>
	    <div style="width:100%;height:150px;border-radius:5px;border:1px solid #ccc;background:#fbfbfb;">
		    <ul id = "tlistid"></ul>
		</div>
		
		<div style="width:100%;height:150px;border-radius:5px;margin-bottom:10px;">
		    <ul id="ld_words"></ul>
		</div>
		<p style="margin-top:15px;text-align: left;height:36px;"></p>
	</div>
    <input type="hidden" id="myusername" value=""/>
    <input type="hidden" id="newpwd" value=""/>
    <input type="hidden" id="renewpwd" value=""/>
    <div style="display:none;" id="buttondiv"></div>
	<div class="form-button">
     
		<input type="button" name="index_submit" id="loginSubmin" onclick="Update_new();" class="btn btn-primary" value="{:L('下一步')}">
	</div>
</div>
<script type="text/javascript">
	function to_next(){
        var username = $("#username").val();
        if(username == ''){
            layer.msg("请输入登陆账号"); return false;
        }
        var password = $("#password").val();
        if(password == ''){
            layer.msg("请输入新交易密码"); return false;
        }
        var repassword = $("#repassword").val();
        if(password != repassword){
            layer.msg("两次输入的密码不一致"); return false;
        }		
        $("#myusername").val(username);
        $("#newpwd").val(password);
        $("#renewpwd").val(repassword);
		$.post("{:U('Login/getwords')}",
		{'username':username},
		function(data){
			if(data.code == 1){
				$.each(data.data,function(key,val){				
					$("#mywords").append('<li style="width:33.33%;height:36px;line-height:36px;float:left;text-align:center;">' + val + '</li>');
				});		
				$("#onebox").hide();
				$("#twobox").show();
				$("#threebox").hide();
			}else{
				layer.msg("该账号不存在！"); return false;
			}
			
		});
	
    }
</script>
<script type="text/javascript">
    function next_updata(){
		var username = $("#username").val();
		$.post("{:U('Login/getlwords')}",
		{'username':username},
		function(data){
			$.each(data.data,function(key,val){
				$("#ld_words").append('<li class="nochoose" onclick="choose_word('+"'"+val+"'"+","+key+')" id="lid_'+val+'">'+val+'</li>');
			});
			$("#onebox").hide();
			$("#twobox").hide();
			$("#threebox").show();
		});
	
        
    }
</script>
<script type="text/javascript">
    function choose_word(str,num){
        var strid = "tid_" + str;
        var word = str;
        var l_str = "lid_" + str;
        var ascid = num;
        $("#tlistid").append("<li style='width:33.33%;height:36px;line-height:36px;float:left;text-align:center;cursor: pointer' id = 'bword"+word+"'> " + word + "</li>");
        $("#buttondiv").append("<input type='hidden' id='"+ascid+"' value='"+word+"'>");
        $("#"+l_str).addClass('choose').removeAttr('onclick');
    }
</script>
<script type="text/javascript">
    function Update_new(){
        var val_a = $("#buttondiv input:eq(0)").val();
        var val_b = $("#buttondiv input:eq(1)").val();
        var val_c = $("#buttondiv input:eq(2)").val();
        var val_d = $("#buttondiv input:eq(3)").val();
        var val_e = $("#buttondiv input:eq(4)").val();
        var val_f = $("#buttondiv input:eq(5)").val();
        var val_g = $("#buttondiv input:eq(6)").val();
        var val_h = $("#buttondiv input:eq(7)").val();
        var val_i = $("#buttondiv input:eq(8)").val();
        var val_j = $("#buttondiv input:eq(9)").val();
        var val_k = $("#buttondiv input:eq(10)").val();
        var val_l = $("#buttondiv input:eq(11)").val();
        if(val_a== '' || val_b == '' || val_c == '' || val_d == '' || val_e == '' || val_f == '' || val_g == '' || val_h == '' || val_i == '' || val_j == '' || val_k == '' || val_l == '' ){
            layer.msg("助记词输入不完整"); return false;
        }
        var username = $("#myusername").val();
        var newpwd = $("#newpwd").val();
        var renewpwd = $("#renewpwd").val();
        if(username == '' || newpwd == ''){
            layer.msg("缺少重要参数，请重新填写资料"); return false;
        }
        $.post("{:U('Login/findpaypwd')}",
        {
          'val_a':val_a,
          'val_b':val_b,
          'val_c':val_c,
          'val_d':val_d,
          'val_e':val_e,
          'val_f':val_f,
          'val_g':val_g,
          'val_h':val_h,
          'val_i':val_i,
          'val_j':val_j,
          'val_k':val_k,
          'val_l':val_l,
          'username':username,
          'newpwd':newpwd,
          'renewpwd':renewpwd
        },
        function(data){
            if(data.code==1){
                layer.msg(data.msg);
                setTimeout(function(args){
                    window.location.href = "{:U('Login/index')}";
                },3000); 
            }else{
                layer.msg(data.msg);
                setTimeout(function(args){
                   window.location.reload();
                },3000);
            }
        });
    }
</script>
<script type="text/javascript">
function regmobile() {
	var mobile = $('#mobile').val();
	var intlNumber = $("#mobile").intlTelInput("getSelectedCountryData");//获取国家代码
	var isValid = $("#mobile").intlTelInput("isValidNumber");//验证手机号码是否正确

	// console.log(isValid)
	// console.log(intlNumber['dialCode'])

	if (mobile == "" || mobile == null) {
		layer.tips('{$Think.lang.请输入手机号码}', '#mobile', { tips: 3 });
		return false;
	}
	if (isValid!=true) {
		layer.tips('{$Think.lang.手机号码不正确}', '#mobile', { tips: 3 });
		return false;
	}
}
	
function regverify() {
    var verify = $('#verify').val();
    if (verify == "" || verify == null) {
        layer.tips('{$Think.lang.请输入图形验证码 }', '#verify', { tips: 3 });
        return false;
    }
}
$("#mobile").intlTelInput({
  allowDropdown: true,
  // autoHideDialCode: true,
  // autoPlaceholder: "off",
  dropdownContainer: "body",
  // excludeCountries: ["us"],
  // formatOnDisplay: true,
  // geoIpLookup: function(callback) {
  //   $.get("http://ipinfo.io", function() {}, "jsonp").always(function(resp) {
  //     var countryCode = (resp && resp.country) ? resp.country : "";
  //     callback(countryCode);
  //   });
  // },
  initialCountry: "cn",
  nationalMode: true,
  // onlyCountries: ['us', 'fr', 'ca', 'jp','kr','de','cn','ru'],
  // placeholderNumberType: "MOBILE",
  // preferredCountries: ['cn', 'jp'],
  separateDialCode: true,
  utilsScript: "/Public/intel/js/utils.js"
});
</script>
<script>
var mbTest_username = /^(?![^a-zA-Z]+$)(?!\D+$).{5,15}$/;
var mbTest_password = /^[a-zA-Z0-9_]{5,15}$/;
	
function password() {
    var password = $('#password').val();
    if (password == "" || password == null) {
        layer.tips('{$Think.lang.请输入登录密码 }', '#password', { tips: 3 });
        return false;
    } else {
        if (!mbTest_password.test(password)) {
            layer.tips('{$Think.lang.密码格式为6~16 位， 不含特殊符号！ }', '#password', { tips: 3 });
            return false;
        }
    }
}

function repassword() {
    var password = $('#password').val();
    var repassword = $('#repassword').val();
    if (repassword == "" || repassword == null) {
        layer.tips('{$Think.lang.请输入确认密码 }', '#repassword', { tips: 3 });
        return false;
    } else {
        if (!mbTest_password.test(repassword)) {
            layer.tips('{$Think.lang.密码格式为6~16 位， 不含特殊符号！ }', '#repassword', { tips: 3 });
            return false;
        } else {
            if (password != repassword) {
                layer.tips('{$Think.lang.两次输入的密码不一致 }', '#repassword', { tips: 3 });
                return false;
            }
        }
    }
}
	

function SendCode(){
	var mobile = $("#mobile").val();
	var verify = $("#verify").val();
	var intlNumber = $("#mobile").intlTelInput("getSelectedCountryData");//获取国家代码
	var intnum = intlNumber['dialCode'];//获取国家代码
	var isValid = $("#mobile").intlTelInput("isValidNumber");//验证手机号码是否正确

    if (mobile == "" || mobile == null) {
        layer.tips('{$Think.lang.请输入手机号码}', '#mobile', { tips: 3 });
        return false;
    }
    if (isValid!=true) {
        layer.tips('{$Think.lang.手机号码不正确}', '#mobile', { tips: 3 });
        return false;
    }
    if (verify == "" || verify == null) {
        layer.tips('{$Think.lang.请先输入图形验证码}', '#verify', { tips: 3 });
        return false;
    }
	
	layer.load(0, {shade: [0.5,'#8F8F8F']});
	$.post("{:U('Verify/findpaypwd')}",{mobile:mobile,verify:verify,intnum:intnum},function(data){
		layer.closeAll();
		if(data.status==1){
			$('#regBtn').removeAttr('onclick');
			layer.msg(data.info,{icon:1});
            var obj = $('#regBtn');
            var wait = 60;
            var interval = setInterval(function() {
                obj.css('color', '#a7a7a7');
                obj.css('cursor', 'default');
                obj.removeAttr('onclick');
                obj.text(wait + '{$Think.lang.秒再次发送 }');

                wait--;
                if (wait < 1) {
                    //obj.removeAttr("disabled");
                    obj.css('color', '#00a7e1');
                    obj.css('cursor', 'pointer');
                    obj.attr("onclick","SendCode();");
                    clearInterval(interval);
                    obj.text('{$Think.lang.获取验证码 }');
                };
            }, 1000);
		}else{
            $("#verifycode").click();
			$('#regBtn').attr("onclick","SendCode();");
			layer.msg(data.info,{icon:2});
		}
	},"json");
}
function Update(){
	var username = $("#mobile").val();
	var mobile = $("#mobile").val();
	var verify = $("#verify").val();
	var mobile_verify = $("#mobile_verify").val();
	var password = $("#password").val();
	var repassword = $("#repassword").val();

    if (username == "" || username == null) {
        layer.tips('{$Think.lang.请输入手机号码 }', '#mobile', { tips: 3 });
        return false;
    }
    if (mobile == "" || mobile == null) {
        layer.tips('{$Think.lang.请输入手机号码 }', '#mobile', { tips: 3 });
        return false;
    }
    if (verify == "" || verify == null) {
        layer.tips('{$Think.lang.请输入图形验证码 }', '#verify', { tips: 3 });
        return false;
    }
    if (mobile_verify == "" || mobile_verify == null) {
        layer.tips('{$Think.lang.请输入手机验证码 }', '#mobile_verify', { tips: 3 });
        return false;
    }
	
    if (password == "" || password == null) {
        layer.tips('{$Think.lang.请输入新交易密码 }', '#password', { tips: 3 });
        return false;
    }
    if (repassword == "" || repassword == null) {
        layer.tips('{$Think.lang.请输入确认交易密码 }', '#repassword', { tips: 3 });
        return false;
    }
    if (password != repassword) {
        layer.tips('{$Think.lang.两次输入的密码不一致 }', '#repassword', { tips: 3 });
        return false;
    }

	$.post("{:U('Login/findpaypwd')}",{username:username,mobile:mobile,mobile_verify:mobile_verify,verify:verify,password:password,repassword:repassword},function(data){
		if(data.status==1){
			layer.msg(data.info,{icon:1});
			window.setTimeout("window.location='/'",1000);
		}else{
			layer.msg(data.info,{icon:2});
		}
	},"json");
}
</script>
<script>
$('#menu_top_index').addClass('current');
$('title').html('{:L('找回交易密码')} - '+'{:L(C('web_title'))}');
</script>
<include file="Public:footer_pass"/>