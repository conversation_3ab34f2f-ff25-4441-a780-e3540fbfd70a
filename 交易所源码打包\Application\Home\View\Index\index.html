<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width,minimum-scale=1,maximum-scale=1.0,initial-scale=1,user-scalable=no,viewport-fit=true" data-shuvi-head="true">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
	    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/base.css" />
	    <title>{$webname}</title>
	   
	    <style>
            .css-1wr4jig {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
            }
            .css-1pysja1 {box-sizing: border-box;margin: 0;min-width: 0; -webkit-flex: 1;-ms-flex: 1;flex: 1;}
            .css-6nqu2s {box-sizing: border-box; margin: 0;min-width: 0;}
            .css-b22026 {box-sizing: border-box;margin: 0;min-width: 0;}
            .css-1xamyaw {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                /*margin-left: auto;*/
                margin-right: auto;
                max-width: 1200px;
                font-size: 12px;
                -webkit-flex-wrap: wrap;
                -ms-flex-wrap: wrap;
                flex-wrap: wrap;
                -webkit-flex-direction: row;
                -ms-flex-direction: row;
                flex-direction: row;
                margin-bottom: 0;
                padding-left: 16px;
                padding-right: 16px;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
            }
            .css-1xamyaw {
                margin-bottom: 0;
                padding-left: 24px;
                padding-right: 24px;
            }
            .css-o32gok {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                font-weight: 600;
                font-size: 28px;
                line-height: 36px;
                font-size: 24px;
                /*color: #1E2329;*/
                color: #FAFAFA;
            }
            .css-jwys36 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                display: none;
                margin-left: auto;
            }
            .css-8puh95 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-inline-box;
                display: -webkit-inline-flex;
                display: -ms-inline-flexbox;
                display: inline-flex;
                position: relative;
                height: 32px;
                margin-top: 4px;
                margin-bottom: 0px;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                line-height: 1.6;
                border: 1px solid transparent;
                border-color: #EAECEF;
                border-radius: 4px;
                padding-left: 8px;
                padding-right: 8px;
                background-color: #FFFFFF;
                margin-top: 0;
                width: 100%;
                height: 44px;
            }
            .css-1t9l9dt {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                /*background-color: #FAFAFA;*/
                padding-top: 24px;
                padding-bottom: 24px;
                padding-left: 16px;
                padding-right: 16px;
                display: none;
            }
            .css-1t9l9dt {
                padding-left: 24px;
                padding-right: 24px;
                display: block;
            }
            .css-8hstpq {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                margin-left: auto;
                margin-right: auto;
                max-width: 1200px;
                font-size: 12px;
                -webkit-flex-wrap: wrap;
                -ms-flex-wrap: wrap;
                flex-wrap: wrap;
                -webkit-flex-direction: row;
                -ms-flex-direction: row;
                flex-direction: row;
                margin-bottom: -16px;
                margin-left: 0;
            }
            .css-8hstpq {
                margin-bottom: 0;
                margin-left: auto;
            }
            .tophangqi{height:120px;width:22%;background-color:#f3f3f3;border-radius:10px;padding:0px 15px;}
            .css-194m5n4 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                background-color: #FFFFFF;
                /*padding-bottom: 24px;*/
                border-radius: 32px 32px 0px 0px;

            }
            .css-1hc8c4h {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                margin-left: auto;
                margin-right: auto;
                max-width: 1200px;
                padding-left: 0;
                padding-right: 0;
                border-radius: 32px 32px 0px 0px;
            }
            .css-1hc8c4h {
                padding-left: 8px;
                padding-right: 8px;
            }
            .css-dlistbox{width:100%;min-height:500px;}
            .css-dlbbox{width:100%;height:30px;background-color:#f3f3f3;padding:0px 10px;}
            .listtitle{height:30px;line-height:30px;}
            .css-dlbbox2{width:100%;height:50px;background-color:#fff;padding:0px 10px;border-bottom:1px solid #f3f3f3;}
            .listtitle2{height:50px;line-height:50px;}

			.bg-box {
				background:#fff;
				width: 100%;
				position: relative;
				height: 560px;
				padding-top: 158px;
				text-align: center;
			}

			.css-8hstpq_img {
				width: 100%;
				height: 120px;
			}

			.tophangqi {
				/*margin-right: 1%;*/
				width: 25%;
			}

			.reginput{
				width: 380px !important;
				height: 60px !important;
				border-radius: 10px !important;
			}

			.regbtn {
				width: 100px !important;
				height: 60px !important;
				background: #07c160 !important;
				border-radius: 10px !important;
				border: none;
				color: #fff;
				font-size: 18px;
			}

			.css-1hc8c4h {
				border: 1px solid #e9e2e2;
				border-radius: 0px;
				padding: 0px;
			}

			.regbtnimg {
				width: 720px;
				position: absolute;
				right: 0px;
				bottom: -120px;
				box-shadow: none !important;


			}

			.css-1xamyaw-content {
				width: 100%;
				color: #fff;
				text-align: center;
				padding-right: 100px;
			}

			.bg-box {
				padding-top: 0px;
    		}

			.reg-input-box {
				margin-top: 60px;
			}

			.option-box {
				width: 40%;
				height: 60%;
				background: #07c160;
				margin-top: 7%;
				margin: 7% auto;
				line-height: 30px;
				border-radius:3px;
				text-decoration :none !important;
			}

			.profit_loss_g {
				background: #eaf6f3;
				padding: 7px;
			}

			.profit_loss_r {
				background: #feeeee;
				padding: 7px;
			}

			.fred {
				color: #fa5252;
			}

			.rgreen {
				color: #12b886;
			}
			.body {
				background: #f6f7f9;
				background-color: #f6f7f9;
			}

			.img-fluid {
				width: 288px !important;
				height: 122px !important;
				border-radius: 5px;
			}

			.notice {
				height: 50px;
				width: 100%;
			}

			.box {
				width: 1000px;
				height: 50px;
				background-color: #fff;
				position: relative;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				/*margin: auto;*/
				overflow: hidden;
				color: #000;
				border-radius: 0px 10px 10px 0px;
			}
			.box ul {
				position: absolute;
				top: 0;
				left: 0;
			}
			.box ul li {
				line-height: 50px;
				list-style: none;
				padding: 0 30px;
				box-sizing: border-box;
				cursor: pointer;
			}

			.box_ul >li {
				padding-left: 0px;
			}





		</style>
		<link rel="stylesheet" href="/Public/Static/bootstrap5Slide/bootstrap.min.css">
		<link rel="stylesheet" href="/Public/Static/bootstrap5Slide/style.css">


	</head>
	<body style="background-color: #f6f7f9;">
	    <div class="App">
	        <div class="css-tq0shg">
	            <include file="Public:header"/>
	            
	            <main class="css-1wr4jig">

	                <div class="css-1pysja1">
						<div class="bg-box">
							<div class="css-1t9l9dt" style="width: 100%;padding-top:74px;">
							<div class="css-8hstpq-bg css-8hstpq" >
									<div class="css-b22026 reg-input-box">
									<div class="css-1xamyaw  css-1xamyaw-l">
										<div class="css-1xamyaw-content">
											<p  class="fw" style="font-size: 48px;padding-bottom: 40px;text-align: initial;color:#151617;">Find Your Next Moonshot</p>

											<p style="font-size: 26px;color:#444545;width: 500px;"> {:L('24小时交易量')} $289,789,518.011</p>
											<p style="font-size: 18px;color:#444545;width: 500px;height: 39px;"> </p>
										</div>
										<if condition="$uid elt 0">
										<form class="form-inline" action="{:U('Login/register')}" >
											<input type="email" class="form-control mb-2 mr-sm-2 reginput" placeholder="{:L('请输入邮箱或手机')}" id="email">
											<button type="submit" class=" mb-2 regbtn">{:L('注册')}</button>
										</form>
										</if>
									</div>

									<div class="css-1xamyaw  css-xamyaw-r">
<!--										<img src="/Public/Home/static/imgs/home_head_bg.png" class="regbtnimg" />-->
									</div>
								</div>
							</div>
							</div>

						</div>

						<div>
							<div class="css-1t9l9dt" style="width: 100%;background: #f6f7f9;padding: 55px 24px;">

								<div class="css-8hstpq" style="height: 220px;">
									<div style="
										width: 120px;
										margin-left: 40px;
										/*margin-right: 20px;*/
										height: 50px;
										background: #0052fe !important;
										float: right;
										text-align: center;
										line-height: 50px;
										font-size: 18px;
										color: #fff;
										border-radius: 10px 0px 0px 10px;
									">
										<p>{:L('公告')}: </p>
									</div>
									<div class="box">
										<ul id="box_ul">

										</ul>
									</div>

									<div class="container" >

										<div class="row mx-auto my-auto justify-content-center">
											<div id="recipeCarousel" class="carousel slide" data-bs-ride="carousel">
												<div class="carousel-inner" role="listbox">
													<div class="carousel-item active">
														<div class="col-md-3">
															<div class="card">
																<div class="card-img">
																	<img src="/Upload/public/{:get_config('wapsildea')}" class="img-fluid">
																</div>
<!--																<div class="card-img-overlay">Slide 1</div>-->
															</div>
														</div>
													</div>
													<div class="carousel-item">
														<div class="col-md-3">
															<div class="card">
																<div class="card-img">
																	<img src="/Upload/public/{:get_config('wapsildeb')}" class="img-fluid">
																</div>
<!--																<div class="card-img-overlay">Slide 2</div>-->
															</div>
														</div>
													</div>
													<div class="carousel-item">
														<div class="col-md-3">
															<div class="card">
																<div class="card-img">
																	<img src="/Upload/public/{:get_config('wapsildec')}" class="img-fluid">
																</div>
<!--																<div class="card-img-overlay">Slide 3</div>-->
															</div>
														</div>
													</div>
													<div class="carousel-item">
														<div class="col-md-3">
															<div class="card">
																<div class="card-img">
																	<img src="/Upload/public/{:get_config('wapsilded')}" class="img-fluid">
																</div>
<!--																<div class="card-img-overlay">Slide 4</div>-->
															</div>
														</div>
													</div>
<!--													<div class="carousel-item">-->
<!--														<div class="col-md-3">-->
<!--															<div class="card">-->
<!--																<div class="card-img">-->
<!--																	<img src="https://via.placeholder.com/500x400/aba?text=5" class="img-fluid">-->
<!--																</div>-->
<!--																<div class="card-img-overlay">Slide 5</div>-->
<!--															</div>-->
<!--														</div>-->
<!--													</div>-->
<!--													<div class="carousel-item">-->
<!--														<div class="col-md-3">-->
<!--															<div class="card">-->
<!--																<div class="card-img">-->
<!--																	<img src="https://via.placeholder.com/500x400/fc0?text=6" class="img-fluid">-->
<!--																</div>-->
<!--																<div class="card-img-overlay">Slide 6</div>-->
<!--															</div>-->
<!--														</div>-->
<!--													</div>-->
												</div>
												<a class="carousel-control-prev bg-transparent w-aut" href="#recipeCarousel" role="button" data-bs-slide="prev">
													<span class="carousel-control-prev-icon" aria-hidden="true"></span>
												</a>
												<a class="carousel-control-next bg-transparent w-aut" href="#recipeCarousel" role="button" data-bs-slide="next">
													<span class="carousel-control-next-icon" aria-hidden="true"></span>
												</a>
											</div>
										</div>

									</div>


								</div>

							</div>
						</div>
	                	<div class="css-194m5n4" style="padding-top:30px;">
	                    <div class="css-1hc8c4h">

	                        <div class="css-dlistbox" >
	                            <div class="css-dlbbox">
	                                <div class="listtitle tcc fl col-2">
	                                    <span class="f12 fch">{:L('名称')}</span>
	                                </div>
	                                <div class="listtitle tcc fl col-2">
	                                    <span class="f12 fch">{:L('价格')}</span>
	                                </div>
	                                <div class="listtitle tcc fl col-2">
	                                    <span class="f12 fch">{:L('涨跌幅')}</span>
	                                </div>
	                                <div class="listtitle tcc fl col-2">
	                                    <span class="f12 fch">{:L('最高')}/{:L('最低')}</span>
	                                </div>
	                                <div class="listtitle tcc fl col-2">
	                                    <span class="f12 fch">24H{:L('量')}</span>
	                                </div>
	                                <div class="listtitle tcc fl col-2">
	                                    <span class="f12 fch">{:L('操作')}</span>
	                                </div>
	                            </div>
	                            
	                            <foreach name="market" item="vo">
	                            <div class="css-dlbbox2 market-div" >
	                                <div class="listtitle2 tcl fl col-2" >
										<img src="{$vo.logo}" class="cion_logo">
	                                    <span class="f14 fch fw cn_{$vo.coinname}"><?php echo strtoupper($vo['coinname']);?>/USDT</span>
	                                </div>

	                                <div class="listtitle2 tcc fl col-2" >
	                                    <span class="f14 fch cpr_{$vo.coinname}">--:--</span>
	                                </div>

	                                <div class="listtitle2 tcc fl col-2 cch_{$vo.coinname}" >
											<span class="f14 fch fw profit_loss">0.00%</span>
	                                </div>

	                                <div class="listtitle2 tcc fl col-2" >
	                                    <span class="f14 fch fw hl_{$vo.coinname}">--.--/--.--</span>
	                                </div>
	                                <div class="listtitle2 tcc fl col-2" >
	                                    <span class="f14 fch fw vol_{$vo.coinname}">--.--</span>
	                                </div>
	                                <div class="listtitle2 tcc fl col-2" >
										<div class="tcc option-box">
	                                    <a href="{:U('Trade/index')}?type=buy&symbol=<?php echo strtoupper($vo['coinname']);?>" href="" class="f14 " style="color: #fff;text-decoration:none">{:L('交易')}</a>
										</div>
	                                </div>
	                            </div>
	                            </foreach>

	                        </div>

	                    </div>

						<div class="css-1hc8c4h index-box-2" >
								<div class="css-dlistbox" style="min-height: 800px;" >
									<!-- 顶部显示-->
									<div class="css-dlistbox-top css-dlistbox-sub" >
										<div class="ss-dlistbox-top-text">
											<p class="ss-dlistbox-top-p1" >{:L('立即开始您的加密货币之旅')}</p>
											<p class="ss-dlistbox-top-p2">{:get_config('webname')}{:L(' 全球公司拥有多种功能，使其成为买卖数字资产的理想场所')}</p>
										</div>

									</div>
									<!-- 顶部显示-->
									<!-- 左侧-->
									<div class="css-dlistbox-l css-dlistbox-sub" >
										<div class="css-dlistbox-l-item1">
											<div class="css-dlistbox-l-content">
												<div class="icon1-73beb614 icon-73beb614"></div>
												<p class="data-p-title">{:L('管理您的资产')}</p>
												<p>{:L('以高达5倍的杠杆率进行现货交易')}</p>
											</div>
											<div class="css-dlistbox-l-content">
												<div class="icon2-73beb614 icon-73beb614"></div>
												<p class="data-p-title">{:L('信用卡付款')}</p>
												<p>{:L('通过我们的合作伙伴用您的信用卡购买加密货币')}</p>
											</div>
											<div class="css-dlistbox-l-content css-dlistbox-l-content-4">
												<div class="icon3-73beb614 icon-73beb614"></div>
												<p class="data-p-title">{:L('安全储存')}</p>
												<p class="data-p-content">{:L('客户资金存放在专用的多签名')}</p>
												<p class="data-p-content">{:L('冷钱包中.24/7全天候安全监控')}</p>
												<p class="data-p-content">{:L('专用20,000 BTC安全储备金')}</b></p>
											</div>
											<div class="css-dlistbox-l-content">
												<div class="icon-73beb614"></div>
												<p class="data-p-title">{:L('随处访问')}</p>
												<p class="data-p-content">{:L('在我们用于Android和iOS的移动应用上进行24/7全')}</p>
												<p class="data-p-content">{:L('天候存款，取款和交易')}</p>
											</div>

											<div class="css-dlistbox-l-download css-dlistbox-l-content">
												<div class="icon-73beb614"></div>
												<div class="ios-down-73beb614"></div>
												<div class="android-down-73beb614"></div>
											</div>
										</div>

									</div>
									<!-- 左侧-->
									<!-- 右侧-->
									<div class="css-dlistbox-r css-dlistbox-sub" >
										<div class="css-dlistbox-bg">

										</div>
										<div class="css-dlistbox-phone">

										</div>
										<div class="img1">

										</div>
										<div class="img2">

										</div>
									</div>
									<!-- 右侧-->
								</div>



							</div>

						<div class="css-1hc8c4h index-box-3">
							<div class="css-dlistbox" style="min-height: 300px" >
								<!-- 顶部显示-->
								<div class="css-dlistbox-top-desc css-dlistbox-sub" >
									<div class="css-dlistbox-l-item1">
										<div class="">
											<p class="ss-dlistbox-top-p1 tcl" >The most complete trading cryptocurrency platform</p>
											<p class="ss-dlistbox-top-p2 tcl">Here are a few reasons why you should choose {:get_config('webname')} </p>
										</div>
									</div>
								</div>
								<!-- 顶部显示-->
								<div class="css-dlistbox-top-desc css-dlistbox-sub-desc" >
										<div class="css-dlistbox-desc-box col-4">
											<div class="home_infoWrapper__G_KFW">
												<img src="/Public/Home/static/imgs/icon_margin.svg" class="home_margin__qse_K" alt="">
												<p>Maximize profit with leverage</p>
											</div>
										</div>
										<div class="css-dlistbox-desc-box col-4">
											<div class="home_infoWrapper__G_KFW">
												<img src="/Public/Home/static/imgs/icon_contract.svg" class="home_margin__qse_K" alt="">
												<p>Up to 125x leverage with superior spreads</p>
											</div>
										</div>
										<div class="css-dlistbox-desc-box col-4">
											<div class="home_infoWrapper__G_KFW">
												<img src="/Public/Home/static/imgs/icon_etf.svg" class="home_margin__qse_K" alt="">
												<p>Increased leverage, no liquidation risk</p>
											</div>
										</div>

								</div>

							</div>
						</div>

						<div class="index-box-4">
							<div class="css-1hc8c4h css-1hc8c4h-box-4 st">
								<div class="css-dlistbox css-dlistbox-4" >
								<!-- 顶部显示-->
								<div class="css-dlistbox-top-desc css-dlistbox-sub" >
									<div class="css-dlistbox-l-item1">
										<div class="">
											<p class="ss-dlistbox-top-p1 tcl" >Join the {:get_config('webname')} Community Today</p>
											<p class="ss-dlistbox-top-p2 tcl">Always there for you</p>
										</div>
									</div>
								</div>
								<if condition="$uid elt 0">
								<div class="css-1xamyaw  css-1xamyaw-l" style="padding: 20px 20px 20px 20px;">
									<form class="form-inline" action="{:U('Login/register')}" >
										<input type="email" class="form-control mb-2 mr-sm-2 reginput" placeholder="{:L('请输入邮箱或手机')}" id="email">
										<button type="submit" class=" mb-2 regbtn">{:L('注册')}</button>
									</form>
								</div>
								</if>
								<div class="svg_list" style="padding: 20px 20px 20px 20px">
									<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" class="footer_svg" fill="#f21515"><path d="M20 0a4 4 0 0 1 4 4v16a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4h16zm-7.7 7h-.6l-1.1.01c-1.48.03-3.7.1-4.46.29-.65.16-1.15.65-1.33 1.26-.18.64-.25 1.7-.29 2.46l-.02.82v.75c.03.76.1 2.09.31 2.85.18.61.68 1.1 1.33 1.26.74.19 2.87.26 4.34.29l1.41.01h1.16c1.45-.03 4-.09 4.81-.3a1.84 1.84 0 0 0 1.33-1.26c.2-.75.28-2.05.3-2.82v-.93c0-.67-.06-2.26-.3-3.13a1.84 1.84 0 0 0-1.33-1.26 25.9 25.9 0 0 0-3.88-.28L12.3 7zM10.46 9.9L14.39 12l-3.92 2.11V9.89z"/></svg>

									<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" class="footer_svg"  fill="#4495ee"><path d="M20 0a4 4 0 0 1 4 4v16a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4h16zm-4 7.28V4.5h-2.29c-2.1 0-3.42 1.6-3.42 3.89v1.67H8v2.77h2.29v6.67h2.85v-6.67h2.29l.57-2.77h-2.86V8.94c0-1.1.58-1.66 1.72-1.66H16z"/></svg>

									<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" class="footer_svg"  fill="#5815b1"><path d="M20 0a4 4 0 0 1 4 4v16a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4h16zM8.95 9.4H6.16v8.1h2.8V9.4zm6.84-.19c-1.32 0-2 .63-2.38 1.16l-.13.18V9.4h-2.79l.01.49V17.5h2.78v-4.52a1.52 1.52 0 0 1 1.52-1.64c.96 0 1.37.66 1.41 1.66v4.5H19v-4.64c0-2.49-1.37-3.65-3.2-3.65zM7.58 5.5C6.62 5.5 6 6.1 6 6.9c0 .73.54 1.32 1.38 1.4h.18c.97 0 1.57-.62 1.57-1.4-.01-.8-.6-1.4-1.55-1.4z"/></svg>


									<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" class="footer_svg"  fill="#000000"><path d="M20 0a4 4 0 0 1 4 4v16a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4h16zm-5.08 7.63l-.23-.01c-1.06 0-2.05.7-2.6.7-.57 0-1.47-.68-2.42-.66a3.6 3.6 0 0 0-3.05 1.88c-1.3 2.3-.34 5.7.93 7.56l.22.33c.58.8 1.25 1.6 2.1 1.57.94-.04 1.3-.62 2.42-.62 1.13 0 1.45.62 2.43.6.78-.01 1.33-.55 1.83-1.22l.1-.14.33-.48c.42-.63.7-1.26.86-1.66l.1-.3L18 15l-.12-.05a3.34 3.34 0 0 1-.55-5.64l.14-.1.14-.1a3.4 3.4 0 0 0-2.53-1.47l-.16-.01-.23-.01h.23zM14.93 4c-.74.03-1.63.5-2.17 1.14-.47.56-.89 1.45-.78 2.32.83.06 1.67-.43 2.19-1.07.51-.63.86-1.51.76-2.39z"/></svg>



									<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" class="footer_svg"  fill="#15d756"><path d="M20 0a4 4 0 0 1 4 4v16a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4h16zm-7.86 4.5a7.34 7.34 0 0 0-6.46 10.82l.15.26L4.5 19.5l4.08-1.3a7.38 7.38 0 0 0 10.92-6.4c0-4.03-3.3-7.3-7.36-7.3zm0 1.16c3.41 0 6.19 2.76 6.19 6.15a6.17 6.17 0 0 1-9.37 5.27l-.23-.15-2.38.76.77-2.28a6.08 6.08 0 0 1-1.17-3.6 6.17 6.17 0 0 1 6.19-6.15zM9.66 8.47a.67.67 0 0 0-.48.23l-.14.15c-.2.23-.5.65-.5 1.34 0 .72.43 1.41.64 1.71l.14.2a7.26 7.26 0 0 0 3.04 2.65l.4.14c1.44.54 1.47.33 1.77.3.33-.03 1.07-.43 1.22-.85.15-.42.15-.78.1-.85-.02-.05-.08-.08-.15-.12l-1.12-.54a5.15 5.15 0 0 0-.3-.13c-.17-.06-.3-.1-.41.09-.12.18-.47.58-.57.7-.1.1-.18.13-.32.08l-.4-.18a4.64 4.64 0 0 1-2.13-1.98c-.1-.18-.01-.28.08-.37l.27-.31c.1-.1.12-.18.18-.3a.3.3 0 0 0 .01-.26l-.1-.23-.48-1.15c-.15-.36-.3-.3-.4-.3l-.35-.02z"/></svg>


									<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" class="footer_svg"  fill="#005cfc"><path d="M12 0c6.627 0 12 5.373 12 12s-5.373 12-12 12S0 18.627 0 12 5.373 0 12 0zm3.193 7c-1.586 0-2.872 1.243-2.872 2.777 0 .217.025.43.074.633a8.251 8.251 0 0 1-5.92-2.902c-.247.41-.389.887-.389 1.397 0 .963.507 1.813 1.278 2.311a2.94 2.94 0 0 1-1.301-.348v.036c0 1.345.99 2.467 2.304 2.723a2.98 2.98 0 0 1-1.298.047c.366 1.103 1.427 1.906 2.683 1.928a5.889 5.889 0 0 1-3.567 1.19c-.231 0-.46-.014-.685-.04A8.332 8.332 0 0 0 9.903 18c5.283 0 8.172-4.231 8.172-7.901 0-.12-.002-.24-.008-.36A5.714 5.714 0 0 0 19.5 8.302a5.869 5.869 0 0 1-1.65.437 2.8 2.8 0 0 0 1.263-1.536 5.87 5.87 0 0 1-1.824.674A2.915 2.915 0 0 0 15.193 7z"/></svg>

									<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" class="footer_svg"  fill="#1578d7"><path d="M12 0a12 12 0 1 1 0 24 12 12 0 0 1 0-24zM8.87 5A3.85 3.85 0 0 0 5 8.82c0 .7.2 1.36.53 1.93a6.63 6.63 0 0 0 7.76 7.8 3.9 3.9 0 0 0 1.84.45 3.85 3.85 0 0 0 3.47-5.51 6.63 6.63 0 0 0-7.67-7.9c-.6-.37-1.3-.59-2.06-.59zm3.16 2.44c.6 0 1.12.07 1.56.2.44.14.81.32 1.1.55.3.22.51.46.65.72.14.25.2.5.2.75a.9.9 0 0 1-.26.63.92.92 0 0 1-.69.28.86.86 0 0 1-.58-.17 2.16 2.16 0 0 1-.4-.53 2.19 2.19 0 0 0-.6-.73c-.22-.17-.6-.25-1.1-.25-.49 0-.88.1-1.17.28-.27.18-.4.39-.4.63 0 .15.03.28.12.39.1.11.23.21.4.3.18.08.36.15.54.2l.3.08.62.14c.54.12 1.04.25 1.48.38.45.14.83.32 1.14.52.32.2.58.47.76.78.18.32.27.7.27 1.16 0 .54-.16 1.04-.47 1.47-.3.43-.77.77-1.36 1.01-.58.24-1.28.37-2.08.37-.96 0-1.77-.17-2.4-.5a3.1 3.1 0 0 1-1.1-.96c-.28-.4-.42-.8-.42-1.19 0-.24.1-.45.28-.62a.99.99 0 0 1 .7-.26.9.9 0 0 1 .58.2c.13.1.23.25.33.43l.06.14c.12.27.25.5.39.67.13.17.32.31.56.43.24.1.57.*********** 0 1-.12 1.34-.35.34-.22.5-.5.5-.82a.8.8 0 0 0-.26-.62c-.18-.17-.42-.3-.71-.4a12.6 12.6 0 0 0-.98-.24l-.25-.05a9.91 9.91 0 0 1-1.75-.52c-.48-.2-.86-.48-1.15-.83-.28-.35-.43-.8-.43-1.31 0-.5.15-.95.46-1.34.3-.38.73-.68 1.3-.88.55-.2 1.2-.3 1.95-.3z"/></svg>

								</div>



							</div>
							</div>
						</div>


	                </div>
					</div>
	            </main>
	            
	            
	            <include file="Public:footer"/>
	        </div>   
	    </div>
	    
	</body>
	<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
    <script type="text/javascript" src="/Public/Home/static/js/layer/layer.js" ></script>
    <script type="text/javascript" src="/Public/Home/static/js/jquery.SuperSlide.2.1.1.js" ></script>
    <script type="text/javascript">
        function obtain_btc(){
            var coin = "btc";
            var nameclass = ".cn_btc";
            var priceclass = ".cpr_btc";
            var pricehl = ".hl_btc";
            var pricevol = ".vol_btc";
            var changeclass = ".cch_btc";
            $.post("{:U('Ajaxtrade/obtain_btc')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_eth(){
            var coin = "eth";
            var nameclass = ".cn_eth";
            var priceclass = ".cpr_eth";
            var pricehl = ".hl_eth";
            var pricevol = ".vol_eth";
            var changeclass = ".cch_eth";
            $.post("{:U('Ajaxtrade/obtain_eth')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_eos(){
            var coin = "eos";
            var nameclass = ".cn_eos";
            var priceclass = ".cpr_eos";
            var pricehl = ".hl_eos";
            var pricevol = ".vol_eos";
            var changeclass = ".cch_eos";
            $.post("{:U('Ajaxtrade/obtain_eos')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_doge(){
            var coin = "doge";
            var nameclass = ".cn_doge";
            var priceclass = ".cpr_doge";
            var pricehl = ".hl_doge";
            var pricevol = ".vol_doge";
            var changeclass = ".cch_doge";
            $.post("{:U('Ajaxtrade/obtain_doge')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_bch(){
            var coin = "bch";
            var nameclass = ".cn_bch";
            var priceclass = ".cpr_bch";
            var pricehl = ".hl_bch";
            var pricevol = ".vol_bch";
            var changeclass = ".cch_bch";
            $.post("{:U('Ajaxtrade/obtain_bch')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_ltc(){
            var coin = "ltc";
            var nameclass = ".cn_ltc";
            var priceclass = ".cpr_ltc";
            var pricehl = ".hl_ltc";
            var pricevol = ".vol_ltc";
            var changeclass = ".cch_ltc";
            $.post("{:U('Ajaxtrade/obtain_ltc')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_iota(){
            var coin = "iota";
            var nameclass = ".cn_iota";
            var priceclass = ".cpr_iota";
            var pricehl = ".hl_iota";
            var pricevol = ".vol_iota";
            var changeclass = ".cch_iota";
            $.post("{:U('Ajaxtrade/obtain_iota')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_fil(){
            var coin = "fil";
            var nameclass = ".cn_fil";
            var priceclass = ".cpr_fil";
            var pricehl = ".hl_fil";
            var pricevol = ".vol_fil";
            var changeclass = ".cch_fil";
            $.post("{:U('Ajaxtrade/obtain_fil')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_flow(){
            var coin = "flow";
            var nameclass = ".cn_flow";
            var priceclass = ".cpr_flow";
            var pricehl = ".hl_flow";
            var pricevol = ".vol_flow";
            var changeclass = ".cch_flow";
            $.post("{:U('Ajaxtrade/obtain_flow')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_jst(){
            var coin = "jst";
            var nameclass = ".cn_jst";
            var priceclass = ".cpr_jst";
            var pricehl = ".hl_jst";
            var pricevol = ".vol_jst";
            var changeclass = ".cch_jst";
            $.post("{:U('Ajaxtrade/obtain_jst')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_itc(){
            var coin = "itc";
            var nameclass = ".cn_itc";
            var priceclass = ".cpr_itc";
            var pricehl = ".hl_itc";
            var pricevol = ".vol_itc";
            var changeclass = ".cch_itc";
            $.post("{:U('Ajaxtrade/obtain_itc')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_ht(){
            var coin = "ht";
            var nameclass = ".cn_ht";
            var priceclass = ".cpr_ht";
            var pricehl = ".hl_ht";
            var pricevol = ".vol_ht";
            var changeclass = ".cch_ht";
            $.post("{:U('Ajaxtrade/obtain_ht')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_usdz(){
            var coin = "usdz";
            var nameclass = ".cn_usdz";
            var priceclass = ".cpr_usdz";
            var pricehl = ".hl_usdz";
            var pricevol = ".vol_usdz";
            var changeclass = ".cch_usdz";
            $.post("{:U('Ajaxtrade/obtain_usdz')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
    </script>
    <script type="text/javascript">
        $(function(){
              //obtain_btc();
            setInterval("obtain_btc()",2000); 
            //obtain_eth();
            setInterval("obtain_eth()",3000); 
            //obtain_eos();
            setInterval("obtain_eos()",5000);
            obtain_doge();
            setInterval("obtain_doge()",7000);
            obtain_bch();
            setInterval("obtain_bch()",9000);
            obtain_ltc();
            setInterval("obtain_ltc()",11000);
            
            obtain_iota();
            setInterval("obtain_iota()",13000);
            
            obtain_fil();
            setInterval("obtain_fil()",15000);
            
            obtain_flow();
            setInterval("obtain_flow()",17000);
            
            obtain_jst();
            setInterval("obtain_jst()",19000);
            
            obtain_itc();
            setInterval("obtain_itc()",21000);
            
            obtain_ht();
            setInterval("obtain_ht()",23000);
            
            obtain_usdz();
            setInterval("obtain_usdz()",25000);
        });
    </script>
    <script type="text/javascript">
		$("#nav").slide({ 
			type:"menu",// 效果类型，针对菜单/导航而引入的参数（默认slide）
			titCell:".nLi", //鼠标触发对象
			targetCell:".sub", //titCell里面包含的要显示/消失的对象
			effect:"slideDown", //targetCell下拉效果
			delayTime:300 , //效果时间
			triggerTime:0, //鼠标延迟触发时间（默认150）
			returnDefault:true //鼠标移走后返回默认状态，例如默认频道是“预告片”，鼠标移走后会返回“预告片”（默认false）
		});
	</script>


	<script src="/Public/Static/bootstrap5Slide/bootstrap.bundle.min.js"></script>
	<script src="/Public/Static/bootstrap5Slide/scripts.js" type="text/javascript"></script>

	<script>
		//消息内容，可以任意长度
		let arr = ["announcement1 : announcement", "announcement2 : announcement2", "announcement3 : announcement3"];

		var settings = {
			speeds: 10, //滚动的速度,单位ms
			isPause: true, //滚动后每个消息是否需要暂停，true和false,
			isHover:true, //鼠标悬停是否需要暂停
		};
		var ul = $('#box_ul')[0];
		//渲染数据
		arr.forEach((item) => {
			var li = document.createElement("li");
			li.innerHTML = item;
			ul.appendChild(li);
		});
		//获取当前滚动的高度，支持ie请自行使用currentStyle
		var currentTop = parseInt(window.getComputedStyle(ul).top);

		//滚动函数
		function run() {
			currentTop--;
			ul.style.top = currentTop + "px";

			//当页面滚动最后一个时，把第一个复制push到尾部
			if (currentTop == (arr.length - 1) * -50) {
				let li = document.createElement("li");
				li.innerHTML = arr[0];
				ul.appendChild(li);
			}

			//无缝替换
			if (currentTop == arr.length * -50) {
				currentTop = 0;
				ul.style.top = currentTop + "px";
				var li = document.querySelectorAll("li");
				ul.removeChild(li[li.length - 1]);
			}

			//滚动后每个消息是否需要暂停
			if (settings.isPause) {
				if (currentTop % 50 == 0) {
					clearInterval(timer);
					setTimeout(function () {
						timer = setInterval(run, settings.speeds);
					}, 3000);
				}
			}
		}
		var timer = setInterval(run, settings.speeds);

		//鼠标悬停是否需要暂停
		ul.onmouseover = function () {
			if(settings.isHover){
				clearInterval(timer);
			}
		};
		ul.onmouseleave = function () {
			clearInterval(timer);
			if(settings.isHover){
				timer = setInterval(run, settings.speeds);
			}
		};

	</script>

	<script>

		$('.market-div').hover(
				function () {
					$(this).css('transform', 'scale(1.02)');
					$(this).css('background', '#f3f3f3');
				},
				function () {
					$(this).css('transform', 'scale(1)');
					$(this).css('background', '#fff');

				}
		)
	</script>
    
</html>