html, body {
	background-color: #121420;
	margin:0px;
	padding:0px;
	font-family: BinancePlex,Arial,sans-serif!important;
	font-size:14px;
}
::-webkit-input-placeholder { /* WebKit browsers */
  color: #424852;
  font-size: 14px;
}

::-moz-placeholder { /* Mozilla Firefox 19+ */
  color: #424852;
  font-size: 14px;
}
.fe6im { color: #e6e6e6 !important;}

:-ms-input-placeholder { /* Internet Explorer 10+ */
  color: #424852;
  font-size: 14px;
}
input:focus{background:#1b1e25;outline: 1px solid #1b1e25;}
select:focus{background:#1b1e25;outline: 1px solid #1b1e25;}
textarea:focus{background:#1b1e25;outline: 1px solid #1b1e25;}
.fl{float:left;}/*左浮动*/
.fr{float:right;}/*右浮动*/
.bhalf{width:50%;}/*div50%的宽度*/
.allhg{height:100%;}/*继承父元素100%的高度*/
.txtl{text-align: left;}
.txtr{text-align: right;}
.fcy{color:#FCD535;} /*黄色*/
.fcc{color:#707A8A;} /*灰色*/
.fch{color:#000;} /*黑色*/
.fzm{font-size:12px;}
.fzmm{font-size:14px;}
.fzmmm{font-size:16px;}
.fw{font-weight: bold;}
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: #00b897;
  overflow: hidden;
}
.alltn{width:100%;height:40px;line-height:40px;text-align:center;border-radius:5px;background:#FCD535;}
.allbtn{width:100%;height:50px;line-height:50px;text-align:center;border-radius:5px;background: #00b897;color: #fff;margin-top:20px;}




