<!DOCTYPE html>
<html lang="en">
	<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="edge">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="renderer" content="webkit">
    <meta name="renderer" content="ie-comp">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-Control" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="format-detection" content="email=no">
    <link rel="stylesheet" type="text/css" href="static/css/swiper.min.css">
    <link rel="stylesheet" type="text/css" href="static/css/index.css">
    <title>Finam LA</title>
    <style type="text/css">
        .loader {
            border: 4px solid #f3f3f3;
            border-radius: 50%;
            border-top: 4px solid #0477f9;
            width: 26px;
            height: 26px;
            float: left;
            margin-left: 4px;
            -webkit-animation: spin 2s linear infinite; /* Safari */
            animation: spin 2s linear infinite;
        }

        /* Safari */
        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
<div class="contain-page">
    <!-- info -->
    <div class="app-info">
        <div class="app-logo">
            <img src="static/picture/logo.png">
        </div>
        <div class="app-info-rig">
            <strong>Finam LA</strong>
            <p>App size：3M</p>
            <div class="clr">
                <a class="arouse"><b>?</b>Safety Certificate</a>
                <a class="btn btn-mini step2 blue" href="javascript:;;" id="install_btn">Free installation</a>
            </div>
        </div>
    </div>
    <!-- 评价 -->
    <div class="app-show">
        <div class="app-score">
            <strong>4.9</strong><img src="static/picture/star.png" alt="">
            <p><span class="down-count">9999,999</span>Ratings</p>
        </div>
        <div class="app-age">
            <strong>18+</strong>
            <p>Age</p>
        </div>
    </div>
    <div class="app-intro">
        <div class="app-intro-con" style="height: auto;">
            <p style="padding: 8px 8px 8px 8px; color: white; background-color: #e64141; border-radius: 5px;">
                1、After downloading the Android phone, click Install<br>
                2、Click on the Iphone phone to return to the desktop after installation. If the download is complete, click on Settings &gt;&gt; Universal &gt;&gt; description file &gt;&gt; Finam LA &gt;&gt;Install
            </p>
        </div>
    </div>
    <!-- 图片展示 -->
    <!-- intro -->
    <div class="app-intro">
        <h2 class="app-title">Introduction</h2>
        <div class="app-intro-con" style="height: auto;">
            <p>Extremely fast download</p>
            <span style="display: none;">More</span>
        </div>
    </div>
    <!-- 评分及评论 -->
    <div class="comment-box">
        <h2 class="app-title">
            Ratings and Reviews
        </h2>
        <div class="comment-con">
            <div class="comment-left">
                <strong>4.9</strong>
                <p>Out of 5</p>
            </div>
            <div class="comment-right">
                <ul class="comment-star-list">
                    <li>
                        <div class="comment-star">
                            <img src="static/picture/star.png" alt="">
                            <div></div>
                        </div>
                        <div class="comment-progress">
                            <div></div>
                        </div>
                    </li>
                    <li>
                        <div class="comment-star">
                            <img src="static/picture/star.png" alt="">
                            <div></div>
                        </div>
                        <div class="comment-progress">
                            <div></div>
                        </div>
                    </li>
                    <li>
                        <div class="comment-star">
                            <img src="static/picture/star.png" alt="">
                            <div></div>
                        </div>
                        <div class="comment-progress">
                            <div></div>
                        </div>
                    </li>
                    <li>
                        <div class="comment-star">
                            <img src="static/picture/star.png" alt="">
                            <div></div>
                        </div>
                        <div class="comment-progress">
                            <div></div>
                        </div>
                    </li>
                    <li>
                        <div class="comment-star">
                            <img src="static/picture/star.png" alt="">
                            <div></div>
                        </div>
                        <div class="comment-progress">
                            <div></div>
                        </div>
                    </li>
                </ul>
                <p><span class="down-count">9,999</span>Ratings</p>
            </div>
        </div>
    </div>
    <!-- 版本号 -->
    <div class="app-intro">
        <h2 class="app-title">New function</h2>
        <div class="app-intro-con" style="height: auto;">
            <p>Version V1.0#</p>
        </div>
    </div>
    <!-- 信息 -->
    <div class="information-box">
        <h2 class="app-title">Information</h2>
        <ul class="information-list">
            <li>
                <span class="l">Sellers</span>
                <div class="r"></div>
            </li>
            <li>
                <span class="l">Size</span>
                <div class="r">3M</div>
            </li>
            <li>
                <span class="l">Language</span>
                <div class="r">Multi-language</div>
            </li>
            <li>
                <span class="l">Age rating</span>
                <div class="r">18 years old and above</div>
            </li>
            <li>
                <span class="l">Price</span>
                <div class="r">Free</div>
            </li>
            <li>
                <span class="l blue-color">Privacy Policy</span>
                <div class="r"></div>
            </li>
        </ul>
    </div>
    <!-- 免责声明 -->
    <div class="disclaimer">
        Disclaimer：<br>
        This website only provides download hosting, and the developer is responsible for the content of the App, which has nothing to do with this website.。
    </div>
    <!-- 蒙版 -->
    <div class="mask">
        <img src="static/picture/go-safari.png" alt="">
    </div>
    <!-- safari提示框 -->
    <div class="mask-box safari-tips">
        <div class="mask-bg"></div>
        <div class="mask-pop">
            <span class="mask-colsed"><img src="static/picture/colsed.png" alt=""></span>
            <img class="copy-url-img" src="static/picture/safari-tip.png" alt="">
            <div class="copy-url">
                <input id="foo" type="text">
                <button data-clipboard-target="#foo">copy</button>
            </div>
        </div>
    </div>
</div>
<!-- 电脑展示 -->
<div class="pc-box">
    <div class="pc-logo">
        <img src="static/picture/logo.png">
    </div>
    <p>Finam LA </p>
    <!--<img src="static/picture/zhongrenju.png" alt="">-->
    <div class="info">Please use your mobile phone to open the download</div>
</div>

<script type="text/javascript" src="static/js/jquery.js "></script>
<script type="text/javascript" src="static/js/fingerprint2.min.js "></script>
<script type="text/javascript" src="static/js/download.js "></script>
<script type="text/javascript" src="static/js/swiper.min.js "></script>
<script type="text/javascript" src="static/js/clipboard.min.js "></script>
<script type="text/javascript">
    $(function () {
        var iosplace = 'appstore-hongtao-2',
            androidplace = 'android-hongtao-1',
            iosplacecode = 'appstore-hongtao-999',
            androidplacecode = 'android-hongtao-4';

        var andurl = '../android/app.apk';     //安卓端下载地址
        var iosurl = '../ios/app.mobileconfig';     //苹果端下载地址

        var ua = navigator.userAgent.toLowerCase(),
            iphoneos = (ua.match(/iphone os/i) == "iphone os") || (ua.match(/iph os/i) == "iph os") || (ua.match(/ipad/i) == "ipad"),
            android = (ua.match(/android/i) == "android") || (ua.match(/adr/i) == "adr") || (ua.match(/android/i) == "mi pad");
        $("#install_btn").on("click", function () {
            DownSoft();
        })

        function auto_download() {
            var d = "1"
            if (d != "1") {
                return
            }
            var issafariBrowser = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)
            if (issafariBrowser) {
                location = ""
                if ("" != "1") {
                    return
                }
                setTimeout(function () {
                    location.href = ''
                }, 1 * 3000)
            }
        }

        //auto_download()

        function DownSoft() {
            //复制
            //copytoclip();
            var s = 'https:' == document.location.protocol ? true : false;
            var pid = iphoneos ? iosplace : androidplace;

            if (iphoneos) {
                console.log(iosurl);
                window.location.href = iosurl;
                // doLocation(iosurl);
            } else {
                console.log(andurl);
                window.location.href = andurl;
                // doLocation(andurl);
            }
        }

        function doLocation(url) {
            var a = document.createElement("a");
            if(!a.click)
            {
                window.location = url;
                return;
            }
            a.setAttribute("href", url);
            a.setAttribute("target", '__blank');
            a.style.display = "none";
            document.body.appendChild(a);
            a.click();
        }

    })
</script>


</body></html>